// 测试规则管理API的PUT请求参数传递方式
import RuleManagementService from './src/services/ruleManagementService.js';

// 测试更新规则API
async function testUpdateRule() {
    console.log('=== 测试更新规则API ===');
    
    const rulesId = 1;
    const ruleData = {
        status: "1",
        rulesType: "1",
        indexNumber: "test-001",
        language: "zh-CN",
        content: "测试规则内容"
    };
    
    try {
        // 这个调用应该会发送：
        // PUT /rules/1?rulesLangId=1&rulesSanctionsId=1
        // Body: { status: "1", rulesType: "1", ... }
        const response = await RuleManagementService.updateRule(rulesId, ruleData);
        console.log('更新规则成功:', response);
    } catch (error) {
        console.error('更新规则失败:', error);
    }
}

// 测试批量更新规则状态API
async function testBatchUpdateRulesStatus() {
    console.log('=== 测试批量更新规则状态API ===');
    
    const ids = [1, 2, 3];
    const status = "1";
    
    try {
        // 这个调用应该会发送：
        // PUT /rules/batch/status?ids=1&ids=2&ids=3&status=1
        // Body: null
        const response = await RuleManagementService.batchUpdateRulesStatus(ids, status);
        console.log('批量更新规则状态成功:', response);
    } catch (error) {
        console.error('批量更新规则状态失败:', error);
    }
}

// 运行测试
async function runTests() {
    console.log('开始测试规则管理API的PUT请求参数传递方式...\n');
    
    await testUpdateRule();
    console.log('\n');
    await testBatchUpdateRulesStatus();
    
    console.log('\n测试完成！');
    console.log('\n请检查浏览器开发者工具的Network标签页，确认：');
    console.log('1. /rules/{rulesId} PUT请求的query参数包含rulesLangId和rulesSanctionsId');
    console.log('2. /rules/batch/status PUT请求的query参数包含ids数组（重复参数形式）和status');
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
    window.testRuleManagementAPI = runTests;
    console.log('测试函数已添加到window对象，请在浏览器控制台中运行: testRuleManagementAPI()');
}

export { testUpdateRule, testBatchUpdateRulesStatus, runTests };
