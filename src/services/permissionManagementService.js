import axios from "../api/axios";

// 获取权限列表
export const getPermissionList = async (params = {}) => {
  // 确保必填参数存在
  const queryParams = {
    page: params.page || 1,
    pageSize: params.pageSize || 20
  };

  // 分离查询参数和请求体参数
  const { page, pageSize, ...bodyParams } = params;

  const response = await axios.post("/manage/permissions/list", bodyParams, {
    params: queryParams
  });
  return response;
};

// 获取单个权限详情
export const getPermissionDetail = async (id) => {
  const response = await axios.get(`/manage/permissions/${id}`);
  return response;
};

// 添加权限
export const addPermission = async (data) => {
  const response = await axios.post("/manage/permissions", data);
  return response;
};

// 更新权限
export const updatePermission = async (id, data) => {
  const response = await axios.put(`/manage/permissions/${id}`, data);
  return response;
};

// 删除单个权限
export const deletePermission = async (id) => {
  const response = await axios.delete(`/manage/permissions/${id}`);
  return response;
};

// 批量删除权限
export const batchDeletePermissions = async (ids) => {
  const response = await axios.delete("/manage/permissions/batch", {
    params: { permissionIds: ids },
    paramsSerializer: params => {
      // 处理数组参数，使用重复参数名的形式：permissionIds=1&permissionIds=2&permissionIds=3
      const searchParams = new URLSearchParams();
      Object.keys(params).forEach(key => {
        if (Array.isArray(params[key])) {
          params[key].forEach(value => searchParams.append(key, value));
        } else {
          searchParams.append(key, params[key]);
        }
      });
      return searchParams.toString();
    }
  });
  return response;
};

// 获取所有员工列表（用于筛选器）
export const getAllStaffList = async () => {
  const response = await axios.get("/manage/list/all");
  return response;
};

// 获取所有资源列表（用于筛选器）
export const getAllResourcesList = async () => {
  const response = await axios.get("/resources/list/all");
  return response;
};
