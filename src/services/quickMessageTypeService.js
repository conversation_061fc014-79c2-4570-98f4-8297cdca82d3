import axios from "../api/axios";

/**
 * 获取快速消息类型列表
 * @param {Object} params 查询参数
 * @returns {Promise} 请求Promise
 */
export const getQuickMessageTypeList = (params = {}) => {
  // 确保必填参数存在
  const queryParams = {
    page: 1,
    pageSize: 20,
    ...params
  };

  return axios.get("/quick/message/type/list", { params: queryParams });
};

/**
 * 获取单个快速消息类型详情
 * @param {number} id 快速消息类型ID
 * @returns {Promise} 请求Promise
 */
export const getQuickMessageTypeDetail = (id) => {
  return axios.get(`/quick/message/type/${id}`);
};

/**
 * 添加快速消息类型
 * @param {Object} data 快速消息类型数据
 * @returns {Promise} 请求Promise
 */
export const addQuickMessageType = (data) => {
  return axios.post("/quick/message/type", data);
};

/**
 * 更新快速消息类型
 * @param {number} id 快速消息类型ID
 * @param {Object} data 快速消息类型数据
 * @returns {Promise} 请求Promise
 */
export const updateQuickMessageType = (id, data) => {
  return axios.put(`/quick/message/type/${id}`, data);
};

/**
 * 批量更新快速消息类型状态
 * @param {Object} data 包含ids和status的对象
 * @returns {Promise} 请求Promise
 */
export const batchUpdateQuickMessageTypeStatus = (data) => {
  return axios.put("/quick/message/type/batch/status", null, {
    params: data,
    paramsSerializer: params => {
      // 处理数组参数，使用重复参数名的形式：ids=1&ids=2&ids=3
      const searchParams = new URLSearchParams();
      Object.keys(params).forEach(key => {
        if (Array.isArray(params[key])) {
          params[key].forEach(value => searchParams.append(key, value));
        } else {
          searchParams.append(key, params[key]);
        }
      });
      return searchParams.toString();
    }
  });
};

/**
 * 删除快速消息类型
 * @param {number} id 快速消息类型ID
 * @returns {Promise} 请求Promise
 */
export const deleteQuickMessageType = (id) => {
  return axios.delete(`/quick/message/type/${id}`);
};

/**
 * 批量删除快速消息类型
 * @param {Array} ids 快速消息类型ID数组
 * @returns {Promise} 请求Promise
 */
export const batchDeleteQuickMessageType = (ids) => {
  return axios.delete("/quick/message/type/batch", { params: { ids } });
};
