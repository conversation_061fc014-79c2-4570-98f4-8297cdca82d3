import { useEffect } from "react";
import { useGlobalConstants } from "@/hooks/useGlobalConstants";

/**
 * 全局枚举配置初始化组件
 * 负责在应用启动时初始化全局枚举配置
 */
const GlobalConstantsInitializer = ({ children }) => {
  const { initialized, initConstants, fetchConstants } = useGlobalConstants();

  useEffect(() => {
    // 应用启动时初始化枚举配置
    if (!initialized) {
      console.log("开始初始化全局枚举配置...");

      // 先从缓存加载
      initConstants();

      // 然后异步获取最新数据
      setTimeout(() => {
        fetchConstants();
      }, 100); // 稍微延迟以避免同时发起多个请求
    }
  }, [initialized, initConstants, fetchConstants]);

  // 直接渲染子组件，不阻塞应用启动
  return children;
};

export default GlobalConstantsInitializer;
