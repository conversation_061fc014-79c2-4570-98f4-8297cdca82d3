import React, { useState } from 'react';
import { Card, Select, Table, Tag, Space, Typography, Button, Form, Input } from 'antd';
import { useGlobalConstants } from '@/hooks/useGlobalConstants';

const { Title, Text } = Typography;

/**
 * 全局枚举配置使用示例组件
 * 展示如何在实际业务组件中使用全局枚举配置
 */
const GlobalConstantsUsageExample = () => {
  const {
    getSelectOptions,
    getSelectOptionsByKey,
    getEnumName,
    getEnumOptions,
    loading
  } = useGlobalConstants();

  const [selectedUser, setSelectedUser] = useState({
    verified: null,
    orderStatus: null,
    category: null,
    paymentMethod: null
  });

  // 模拟用户数据
  const userData = [
    {
      key: '1',
      name: '张三',
      verified: 1, // 已验证
      orderStatus: 2, // 已完成
      category: 1, // 电子产品
      paymentMethod: 4 // 支付宝
    },
    {
      key: '2',
      name: '李四',
      verified: 0, // 未验证
      orderStatus: 1, // 处理中
      category: 2, // 服装
      paymentMethod: 5 // 微信支付
    },
    {
      key: '3',
      name: '王五',
      verified: 1, // 已验证
      orderStatus: 0, // 待处理
      category: 3, // 图书
      paymentMethod: 1 // 信用卡
    }
  ];

  // 表格列定义
  const columns = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '验证状态',
      dataIndex: 'verified',
      key: 'verified',
      render: (value) => {
        const name = getEnumName('User.Verified', value);
        const color = value === 1 ? 'green' : 'orange';
        return <Tag color={color}>{name}</Tag>;
      }
    },
    {
      title: '订单状态',
      dataIndex: 'orderStatus',
      key: 'orderStatus',
      render: (value) => {
        const name = getEnumName('Order.Status', value);
        const colorMap = { 0: 'default', 1: 'processing', 2: 'success', 3: 'error' };
        return <Tag color={colorMap[value]}>{name}</Tag>;
      }
    },
    {
      title: '商品类别',
      dataIndex: 'category',
      key: 'category',
      render: (value) => getEnumName('Product.Category', value)
    },
    {
      title: '支付方式',
      dataIndex: 'paymentMethod',
      key: 'paymentMethod',
      render: (value) => getEnumName('Payment.Method', value)
    }
  ];

  const handleFormSubmit = (values) => {
    console.log('表单提交数据:', values);
    console.log('验证状态名称:', getEnumName('User.Verified', values.verified));
    console.log('订单状态名称:', getEnumName('Order.Status', values.orderStatus));
  };

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>全局枚举配置使用示例</Title>
      
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        
        {/* 表单示例 */}
        <Card title="表单示例 - 使用枚举配置">
          <Form
            layout="vertical"
            onFinish={handleFormSubmit}
            initialValues={selectedUser}
          >
            <Space wrap>
              <Form.Item label="用户名" name="name" style={{ width: 200 }}>
                <Input placeholder="请输入用户名" />
              </Form.Item>
              
              <Form.Item label="验证状态" name="verified" style={{ width: 200 }}>
                <Select
                  placeholder="选择验证状态"
                  options={getSelectOptions('User.Verified')}
                  loading={loading}
                />
              </Form.Item>
              
              <Form.Item label="订单状态" name="orderStatus" style={{ width: 200 }}>
                <Select
                  placeholder="选择订单状态"
                  options={getSelectOptions('Order.Status')}
                  loading={loading}
                />
              </Form.Item>
              
              <Form.Item label="商品类别" name="category" style={{ width: 200 }}>
                <Select
                  placeholder="选择商品类别"
                  options={getSelectOptions('Product.Category')}
                  loading={loading}
                />
              </Form.Item>
              
              <Form.Item label="支付方式" name="paymentMethod" style={{ width: 200 }}>
                <Select
                  placeholder="选择支付方式"
                  options={getSelectOptions('Payment.Method')}
                  loading={loading}
                />
              </Form.Item>
              
              <Form.Item style={{ alignSelf: 'end' }}>
                <Button type="primary" htmlType="submit">
                  提交
                </Button>
              </Form.Item>
            </Space>
          </Form>
        </Card>

        {/* 表格示例 */}
        <Card title="表格示例 - 枚举值显示">
          <Table
            columns={columns}
            dataSource={userData}
            pagination={false}
            size="small"
          />
        </Card>

        {/* 使用 Key 作为值的示例 */}
        <Card title="使用 Key 作为值的示例">
          <Space direction="vertical">
            <Text>有时候我们需要使用枚举的 key 而不是 id 作为值：</Text>
            <Select
              style={{ width: 300 }}
              placeholder="选择验证状态 (使用 key)"
              options={getSelectOptionsByKey('User.Verified')}
              onChange={(value) => {
                console.log('选择的 key:', value);
                console.log('对应的名称:', getEnumName('User.Verified', value));
              }}
            />
          </Space>
        </Card>

        {/* 枚举选项展示 */}
        <Card title="所有枚举选项" size="small">
          <Space direction="vertical" style={{ width: '100%' }}>
            {['User.Verified', 'Order.Status', 'Product.Category', 'Payment.Method'].map(enumKey => (
              <div key={enumKey}>
                <Text strong>{enumKey}:</Text>
                <div style={{ marginLeft: 16, marginTop: 4 }}>
                  {getEnumOptions(enumKey).map(option => (
                    <Tag key={option.id} style={{ margin: '2px 4px 2px 0' }}>
                      {option.name} (ID: {option.id}, Key: {option.key})
                    </Tag>
                  ))}
                </div>
              </div>
            ))}
          </Space>
        </Card>
      </Space>
    </div>
  );
};

export default GlobalConstantsUsageExample;
