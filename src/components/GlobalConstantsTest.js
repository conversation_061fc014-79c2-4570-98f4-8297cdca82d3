import React from 'react';
import { Card, Button, Select, Table, Space, Typography, Spin, Alert } from 'antd';
import { useGlobalConstants } from '@/hooks/useGlobalConstants';

const { Title, Text, Paragraph } = Typography;

/**
 * 全局枚举配置测试组件
 * 用于测试和演示全局枚举配置的功能
 */
const GlobalConstantsTest = () => {
  const {
    constants,
    loading,
    error,
    initialized,
    lastUpdated,
    refreshConstants,
    getEnumOptions,
    getEnumName,
    getSelectOptions,
    getEnumKeys,
    getAllConstants
  } = useGlobalConstants();

  // 获取所有枚举键名
  const enumKeys = getEnumKeys();

  // 表格列定义
  const columns = [
    {
      title: '枚举键',
      dataIndex: 'enumKey',
      key: 'enumKey',
      width: 200,
    },
    {
      title: '选项数量',
      dataIndex: 'count',
      key: 'count',
      width: 100,
    },
    {
      title: '示例选项',
      dataIndex: 'example',
      key: 'example',
      render: (_, record) => {
        const options = getEnumOptions(record.enumKey);
        const firstOption = options[0];
        if (firstOption) {
          return (
            <Space direction="vertical" size="small">
              <Text>ID: {firstOption.id}</Text>
              <Text>Key: {firstOption.key}</Text>
              <Text>Name: {firstOption.name}</Text>
            </Space>
          );
        }
        return '-';
      }
    }
  ];

  // 表格数据
  const tableData = enumKeys.map(key => ({
    key,
    enumKey: key,
    count: getEnumOptions(key).length
  }));

  if (error) {
    return (
      <Alert
        message="加载全局枚举配置失败"
        description={error.message || '未知错误'}
        type="error"
        showIcon
        action={
          <Button size="small" onClick={refreshConstants}>
            重试
          </Button>
        }
      />
    );
  }

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>全局枚举配置测试</Title>
      
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* 状态信息 */}
        <Card title="状态信息">
          <Space direction="vertical" size="small">
            <Text>初始化状态: {initialized ? '已初始化' : '未初始化'}</Text>
            <Text>加载状态: {loading ? '加载中' : '已完成'}</Text>
            <Text>最后更新: {lastUpdated ? new Date(lastUpdated).toLocaleString() : '未知'}</Text>
            <Text>枚举数量: {enumKeys.length}</Text>
            <Button onClick={refreshConstants} loading={loading}>
              刷新配置
            </Button>
          </Space>
        </Card>

        {/* 枚举列表 */}
        <Card title="枚举列表">
          {loading ? (
            <div style={{ textAlign: 'center', padding: 50 }}>
              <Spin size="large" />
            </div>
          ) : (
            <Table
              columns={columns}
              dataSource={tableData}
              pagination={false}
              size="small"
            />
          )}
        </Card>

        {/* 功能演示 */}
        {enumKeys.length > 0 && (
          <Card title="功能演示">
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <div>
                <Text strong>选择一个枚举查看详情:</Text>
                <Select
                  style={{ width: 300, marginLeft: 8 }}
                  placeholder="选择枚举"
                  options={enumKeys.map(key => ({ label: key, value: key }))}
                  onChange={(selectedKey) => {
                    const options = getEnumOptions(selectedKey);
                    console.log(`${selectedKey} 的选项:`, options);
                    console.log(`Select 组件选项:`, getSelectOptions(selectedKey));
                  }}
                />
              </div>

              {/* 示例：用户验证状态 */}
              {constants['User.Verified'] && (
                <div>
                  <Text strong>示例 - 用户验证状态:</Text>
                  <div style={{ marginTop: 8 }}>
                    <Select
                      style={{ width: 200 }}
                      placeholder="选择验证状态"
                      options={getSelectOptions('User.Verified')}
                      onChange={(value) => {
                        const name = getEnumName('User.Verified', value);
                        console.log(`选择的状态: ID=${value}, Name=${name}`);
                      }}
                    />
                  </div>
                </div>
              )}
            </Space>
          </Card>
        )}

        {/* 原始数据 */}
        <Card title="原始数据" size="small">
          <pre style={{ 
            background: '#f5f5f5', 
            padding: 16, 
            borderRadius: 4,
            fontSize: 12,
            maxHeight: 300,
            overflow: 'auto'
          }}>
            {JSON.stringify(getAllConstants(), null, 2)}
          </pre>
        </Card>
      </Space>
    </div>
  );
};

export default GlobalConstantsTest;
