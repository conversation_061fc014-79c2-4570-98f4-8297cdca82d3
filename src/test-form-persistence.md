# 表单数据持久化功能测试

## 功能说明
修改了系统设置页面，使得在"添加设置"页面填写数据后，切换到"系统设置"列表页面时，表单数据不会被清空，能够保持页面状态和数据。

## 实现方案

### 1. 修改 `usePageTabs` Hook
- 添加了 `tabFormData` 状态来保存每个标签页的表单数据
- 新增了三个函数：
  - `saveTabFormData(tabKey, formData)`: 保存标签页表单数据
  - `getTabFormData(tabKey)`: 获取标签页表单数据
  - `clearTabFormData(tabKey)`: 清除标签页表单数据
- 在标签关闭时自动清除对应的表单数据

### 2. 修改 `AddSystemSettings` 组件
- 在组件初始化时，如果是添加模式且有保存的表单数据，则恢复表单数据
- 使用 Form 的 `onValuesChange` 属性实时保存表单数据变化
- 在表单提交成功或取消时清除保存的表单数据

### 3. 修改 `SystemSettings` 页面
- 传递表单数据管理函数和当前标签key给 `AddSystemSettings` 组件

## 测试步骤

1. 打开系统设置页面
2. 点击"添加设置"按钮，打开添加设置页面
3. 在表单中填写一些数据（如配置键、解析对象等）
4. 切换到"系统设置"列表页面
5. 再次切换回"添加设置"页面
6. 验证之前填写的数据是否还在

## 预期结果
- 表单数据应该被保留，用户不需要重新填写
- 只有在以下情况下表单数据才会被清除：
  - 用户点击"取消"按钮
  - 用户成功提交表单
  - 用户关闭添加设置标签页

## 技术细节
- 使用 React 状态管理来保存表单数据
- 每个标签页都有唯一的 key，用于区分不同的表单实例
- 只在添加模式下保存表单数据，编辑模式不需要此功能
- 使用 Ant Design Form 的 `onValuesChange` 实现实时保存
