import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { message } from "antd";
import dayjs from "dayjs";
import {
  getStaffList,
  getStaffDetail,
  addStaff,
  updateStaff,
  deleteStaff,
  batchDeleteStaff,
  batchUpdateStaffStatus,
  getRoleList,
} from "../../services/staffManagementService";

// 初始状态
const initialState = {
  staffList: [],
  roleList: [],
  pagination: {
    current: 1,
    pageSize: 20,
    total: 0,
  },
  loading: false,
  detailLoading: false,
  submitLoading: false,
  batchDeleteLoading: false,
  currentStaff: null,
  deleteData: {
    loading: false,
    error: null,
  },
  batchDeleteData: {
    loading: false,
    error: null,
  },
  filters: {
    status: "全部状态",
    roleId: undefined,
    username: "",
    email: "",
    joinTime: null,
  },
};

// 异步 Thunk Actions
export const fetchStaffList = createAsyncThunk(
  "staffManagement/fetchStaffList",
  async (params, { rejectWithValue, getState }) => {
    try {
      const state = getState();
      const { pagination, filters } = state.staffManagementPage;

      const requestParams = {
        page: params?.page || pagination.current,
        pageSize: params?.pageSize || pagination.pageSize,
        ...filters,
      };

      // 处理状态过滤
      if (requestParams.status === "全部状态") {
        delete requestParams.status;
      } else if (requestParams.status === "ACTIVE") {
        requestParams.status = "1";
      } else if (requestParams.status === "INACTIVE") {
        requestParams.status = "0";
      }

      // 处理日期
      if (requestParams.joinTime) {
        requestParams.joinTime = dayjs(requestParams.joinTime).format(
          "YYYY-MM-DD"
        );
      }

      const response = await getStaffList(requestParams);
      return response;
    } catch (error) {
      message.error("获取员工列表失败");
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const fetchRoleList = createAsyncThunk(
  "staffManagement/fetchRoleList",
  async (params = {}, { rejectWithValue }) => {
    try {
      const queryParams = {
        page: 1,
        pageSize: 20,
        ...params
      };
      const response = await getRoleList(queryParams);
      return response;
    } catch (error) {
      message.error("获取角色列表失败");
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const fetchStaffDetail = createAsyncThunk(
  "staffManagement/fetchStaffDetail",
  async (id, { rejectWithValue }) => {
    try {
      const response = await getStaffDetail(id);
      return response;
    } catch (error) {
      message.error("获取员工详情失败");
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const saveStaff = createAsyncThunk(
  "staffManagement/saveStaff",
  async ({ id, data }, { rejectWithValue, dispatch }) => {
    try {
      let response;
      if (id) {
        // 更新员工
        response = await updateStaff(id, data);
        message.success("更新员工成功");
      } else {
        // 添加员工
        response = await addStaff(data);
        message.success("添加员工成功");
      }

      // 刷新列表
      dispatch(fetchStaffList({
        page: 1,
        pageSize: 20
      }));
      return response;
    } catch (error) {
      message.error(id ? "更新员工失败" : "添加员工失败");
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const removeStaff = createAsyncThunk(
  "staffManagement/removeStaff",
  async (id, { rejectWithValue, dispatch }) => {
    try {
      const response = await deleteStaff(id);
      message.success("删除员工成功");

      // 刷新列表
      dispatch(fetchStaffList({
        page: 1,
        pageSize: 20
      }));
      return response;
    } catch (error) {
      message.error("删除员工失败");
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const removeBatchStaff = createAsyncThunk(
  "staffManagement/removeBatchStaff",
  async (ids, { rejectWithValue, dispatch }) => {
    try {
      // 确保ids是数组
      const idsArray = Array.isArray(ids) ? ids : [ids];

      if (idsArray.length === 0) {
        message.warning("请选择要删除的员工");
        return rejectWithValue("未选择员工");
      }

      const response = await batchDeleteStaff(idsArray);
      message.success(`已删除 ${idsArray.length} 个员工`);

      // 刷新列表
      dispatch(fetchStaffList({
        page: 1,
        pageSize: 20
      }));
      return response;
    } catch (error) {
      message.error("批量删除员工失败");
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const updateBatchStatus = createAsyncThunk(
  "staffManagement/updateBatchStatus",
  async ({ ids, status }, { rejectWithValue, dispatch }) => {
    try {
      const response = await batchUpdateStaffStatus(ids, status);
      message.success(`已修改 ${ids.length} 个员工的状态`);

      // 刷新列表
      dispatch(fetchStaffList({
        page: 1,
        pageSize: 20
      }));
      return response;
    } catch (error) {
      message.error("批量修改状态失败");
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Slice
const staffManagementPageSlice = createSlice({
  name: "staffManagementPage",
  initialState,
  reducers: {
    setFilters: (state, action) => {
      state.filters = {
        ...state.filters,
        ...action.payload,
      };
    },
    setPagination: (state, action) => {
      state.pagination = {
        ...state.pagination,
        ...action.payload,
      };
    },
    resetCurrentStaff: (state) => {
      state.currentStaff = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // 获取员工列表
      .addCase(fetchStaffList.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchStaffList.fulfilled, (state, action) => {
        state.loading = false;
        if (action.payload?.data) {
          const { curPage, maxPage, total, data } = action.payload.data;
          state.staffList = Array.isArray(data) ? data : [data];
          state.pagination.current = curPage || 1;
          state.pagination.total = total || 0;
        }
      })
      .addCase(fetchStaffList.rejected, (state) => {
        state.loading = false;
      })

      // 获取角色列表
      .addCase(fetchRoleList.fulfilled, (state, action) => {
        if (action.payload?.data?.data) {
          state.roleList = Array.isArray(action.payload.data.data)
            ? action.payload.data.data
            : [action.payload.data.data];
        }
      })

      // 获取员工详情
      .addCase(fetchStaffDetail.pending, (state) => {
        state.detailLoading = true;
      })
      .addCase(fetchStaffDetail.fulfilled, (state, action) => {
        state.detailLoading = false;
        if (action.payload?.data) {
          state.currentStaff = action.payload.data;
        }
      })
      .addCase(fetchStaffDetail.rejected, (state) => {
        state.detailLoading = false;
      })

      // 保存员工信息
      .addCase(saveStaff.pending, (state) => {
        state.submitLoading = true;
      })
      .addCase(saveStaff.fulfilled, (state) => {
        state.submitLoading = false;
        state.currentStaff = null;
      })
      .addCase(saveStaff.rejected, (state) => {
        state.submitLoading = false;
      })

      // 删除单个员工
      .addCase(removeStaff.pending, (state) => {
        state.deleteData.loading = true;
        state.deleteData.error = null;
      })
      .addCase(removeStaff.fulfilled, (state) => {
        state.deleteData.loading = false;
        // 不需要手动更新列表，因为会重新获取数据
      })
      .addCase(removeStaff.rejected, (state, action) => {
        state.deleteData.loading = false;
        state.deleteData.error = action.payload;
      })

      // 批量删除员工
      .addCase(removeBatchStaff.pending, (state) => {
        state.batchDeleteLoading = true;
        state.batchDeleteData.loading = true;
        state.batchDeleteData.error = null;
      })
      .addCase(removeBatchStaff.fulfilled, (state) => {
        state.batchDeleteLoading = false;
        state.batchDeleteData.loading = false;
      })
      .addCase(removeBatchStaff.rejected, (state, action) => {
        state.batchDeleteLoading = false;
        state.batchDeleteData.loading = false;
        state.batchDeleteData.error = action.payload;
      });
  },
});

export const { setFilters, setPagination, resetCurrentStaff } =
  staffManagementPageSlice.actions;

export default staffManagementPageSlice.reducer;
