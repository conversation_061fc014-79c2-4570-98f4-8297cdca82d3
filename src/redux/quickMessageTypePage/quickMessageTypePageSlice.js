import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import {
  getQuickMessageTypeList,
  getQuickMessageTypeDetail,
  addQuickMessageType,
  updateQuickMessageType,
  batchUpdateQuickMessageTypeStatus,
  deleteQuickMessageType,
  batchDeleteQuickMessageType,
} from "../../services/quickMessageTypeService";
import { message } from "antd";
import dayjs from "dayjs";

// 获取快速消息类型列表
export const fetchQuickMessageTypeList = createAsyncThunk(
  "quickMessageType/fetchList",
  async (params, { rejectWithValue }) => {
    try {
      const response = await getQuickMessageTypeList(params);
      return response;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// 获取单个快速消息类型详情
export const fetchQuickMessageTypeDetail = createAsyncThunk(
  "quickMessageType/fetchDetail",
  async (id, { rejectWithValue }) => {
    try {
      const response = await getQuickMessageTypeDetail(id);
      return response;
    } catch (error) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// 添加快速消息类型
export const createQuickMessageType = createAsyncThunk(
  "quickMessageType/create",
  async (data, { rejectWithValue, dispatch }) => {
    try {
      const response = await addQuickMessageType(data);
      message.success("添加成功");
      // 添加成功后重新获取数据
      dispatch(fetchQuickMessageTypeList({
        page: 1,
        pageSize: 20
      }));
      return response;
    } catch (error) {
      message.error("添加失败");
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// 更新快速消息类型
export const updateQuickMessageTypeItem = createAsyncThunk(
  "quickMessageType/update",
  async ({ id, data }, { rejectWithValue, dispatch }) => {
    try {
      const response = await updateQuickMessageType(id, data);
      message.success("更新成功");
      // 更新成功后重新获取数据
      dispatch(fetchQuickMessageTypeList({
        page: 1,
        pageSize: 20
      }));
      return { id, ...response };
    } catch (error) {
      message.error("更新失败");
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// 批量更新快速消息类型状态
export const batchUpdateStatus = createAsyncThunk(
  "quickMessageType/batchUpdateStatus",
  async (data, { rejectWithValue, dispatch }) => {
    try {
      const response = await batchUpdateQuickMessageTypeStatus(data);
      message.success(`成功更新${data.ids.length}条记录的状态`);
      // 更新成功后重新获取数据
      dispatch(fetchQuickMessageTypeList({
        page: 1,
        pageSize: 20
      }));
      return response;
    } catch (error) {
      message.error("批量更新状态失败");
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// 删除快速消息类型
export const removeQuickMessageType = createAsyncThunk(
  "quickMessageType/remove",
  async (id, { rejectWithValue, dispatch }) => {
    try {
      await deleteQuickMessageType(id);
      message.success("删除成功");
      // 删除成功后重新获取数据
      dispatch(fetchQuickMessageTypeList({
        page: 1,
        pageSize: 20
      }));
      return id;
    } catch (error) {
      message.error("删除失败");
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// 批量删除快速消息类型
export const batchRemoveQuickMessageType = createAsyncThunk(
  "quickMessageType/batchRemove",
  async (ids, { rejectWithValue, dispatch }) => {
    try {
      await batchDeleteQuickMessageType(ids);
      message.success(`成功删除${ids.length}条记录`);
      // 批量删除成功后重新获取数据
      dispatch(fetchQuickMessageTypeList({
        page: 1,
        pageSize: 20
      }));
      return ids;
    } catch (error) {
      message.error("批量删除失败");
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// 格式化API返回的数据为前端显示格式
const formatQuickMessageTypeItem = (item) => {
  return {
    key: item.id.toString(),
    id: item.id,
    状态: item.status === "1" ? "启用" : "禁用",
    消息类型名称: item.typeName || "",
    使用时间: item.mark || "",
    创建时间: item.createTime
      ? dayjs(item.createTime * 1000).format("YYYY-MM-DD HH:mm:ss")
      : "-",
  };
};

const initialState = {
  list: {
    data: [],
    total: 0,
    curPage: 1,
    maxPage: 1,
    loading: false,
    error: null,
  },
  detail: {
    data: null,
    loading: false,
    error: null,
  },
  deleteData: {
    loading: false,
    error: null,
  },
  batchDeleteData: {
    loading: false,
    error: null,
  },
  searchParams: {
    typeName: "",
    status: "",
    startDate: null,
    endDate: null,
  },
  selectedRowKeys: [],
};

const quickMessageTypePageSlice = createSlice({
  name: "quickMessageTypePage",
  initialState,
  reducers: {
    setSearchParams: (state, action) => {
      state.searchParams = { ...state.searchParams, ...action.payload };
    },
    setSelectedRowKeys: (state, action) => {
      state.selectedRowKeys = action.payload;
    },
    clearSelectedRowKeys: (state) => {
      state.selectedRowKeys = [];
    },
  },
  extraReducers: (builder) => {
    // 获取列表
    builder
      .addCase(fetchQuickMessageTypeList.pending, (state) => {
        state.list.loading = true;
        state.list.error = null;
      })
      .addCase(fetchQuickMessageTypeList.fulfilled, (state, action) => {
        state.list.loading = false;
        if (action.payload && action.payload.data) {
          state.list.data = Array.isArray(action.payload.data.data)
            ? action.payload.data.data.map(formatQuickMessageTypeItem)
            : action.payload.data.data
              ? [formatQuickMessageTypeItem(action.payload.data.data)]
              : [];
          state.list.total = action.payload.data.total || 0;
          state.list.curPage = action.payload.data.curPage || 1;
          state.list.maxPage = action.payload.data.maxPage || 1;
        }
      })
      .addCase(fetchQuickMessageTypeList.rejected, (state, action) => {
        state.list.loading = false;
        state.list.error = action.payload;
      })

      // 获取详情
      .addCase(fetchQuickMessageTypeDetail.pending, (state) => {
        state.detail.loading = true;
        state.detail.error = null;
      })
      .addCase(fetchQuickMessageTypeDetail.fulfilled, (state, action) => {
        state.detail.loading = false;
        state.detail.data = action.payload.data;
      })
      .addCase(fetchQuickMessageTypeDetail.rejected, (state, action) => {
        state.detail.loading = false;
        state.detail.error = action.payload;
      })

      // 删除单个
      .addCase(removeQuickMessageType.pending, (state) => {
        state.deleteData.loading = true;
        state.deleteData.error = null;
      })
      .addCase(removeQuickMessageType.fulfilled, (state) => {
        state.deleteData.loading = false;
        // 不需要手动更新列表，因为会重新获取数据
      })
      .addCase(removeQuickMessageType.rejected, (state, action) => {
        state.deleteData.loading = false;
        state.deleteData.error = action.payload;
      })

      // 批量删除
      .addCase(batchRemoveQuickMessageType.pending, (state) => {
        state.batchDeleteData.loading = true;
        state.batchDeleteData.error = null;
      })
      .addCase(batchRemoveQuickMessageType.fulfilled, (state) => {
        state.batchDeleteData.loading = false;
        // 清空选中的行
        state.selectedRowKeys = [];
      })
      .addCase(batchRemoveQuickMessageType.rejected, (state, action) => {
        state.batchDeleteData.loading = false;
        state.batchDeleteData.error = action.payload;
      })

      // 批量更新状态
      .addCase(batchUpdateStatus.fulfilled, (state) => {
        // 清空选中的行
        state.selectedRowKeys = [];
      });
  },
});

export const { setSearchParams, setSelectedRowKeys, clearSelectedRowKeys } =
  quickMessageTypePageSlice.actions;

export default quickMessageTypePageSlice.reducer;
