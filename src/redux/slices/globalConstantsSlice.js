import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import GlobalConstantsService from "@/services/globalConstantsService";
import globalConstantsManager from "@/utils/globalConstantsManager";

// 初始状态
const initialState = {
  constants: {}, // 存储所有枚举数据
  loading: false,
  error: null,
  lastUpdated: null, // 最后更新时间
  initialized: false, // 是否已初始化
};

// 异步Thunk - 初始化全局枚举配置
export const initGlobalConstants = createAsyncThunk(
  "globalConstants/initGlobalConstants",
  async (_, { rejectWithValue }) => {
    try {
      // 先从缓存加载
      globalConstantsManager.init();

      // 获取缓存的数据
      const cachedConstants = globalConstantsManager.getAllConstants();
      const cachedLastUpdated = globalConstantsManager.getLastUpdated();

      // 返回缓存数据（如果有的话）
      return {
        constants: cachedConstants,
        lastUpdated: cachedLastUpdated,
        fromCache: true,
      };
    } catch (error) {
      console.error("初始化全局枚举配置失败:", error);
      return rejectWithValue(
        error.response?.data || { message: error.message }
      );
    }
  }
);

// 异步Thunk - 获取最新的全局枚举配置
export const fetchGlobalConstants = createAsyncThunk(
  "globalConstants/fetchGlobalConstants",
  async (_, { rejectWithValue }) => {
    try {
      const response = await GlobalConstantsService.getAllConstants();
      console.log("获取全局枚举配置API响应:", response);

      // 更新管理器中的数据
      if (response && response.data) {
        globalConstantsManager.updateConstants(response.data);
      }

      return response;
    } catch (error) {
      console.error("获取全局枚举配置失败:", error);
      return rejectWithValue(
        error.response?.data || { message: error.message }
      );
    }
  }
);

// 异步Thunk - 刷新全局枚举配置
export const refreshGlobalConstants = createAsyncThunk(
  "globalConstants/refreshGlobalConstants",
  async (_, { rejectWithValue }) => {
    try {
      // 刷新就是重新获取最新数据
      const response = await GlobalConstantsService.getAllConstants();
      console.log("刷新全局枚举配置API响应:", response);

      // 更新管理器中的数据
      if (response && response.data) {
        globalConstantsManager.updateConstants(response.data);
      }

      return response;
    } catch (error) {
      console.error("刷新全局枚举配置失败:", error);
      return rejectWithValue(
        error.response?.data || { message: error.message }
      );
    }
  }
);

// 创建切片
const globalConstantsSlice = createSlice({
  name: "globalConstants",
  initialState,
  reducers: {
    // 重置状态
    resetGlobalConstantsState: () => initialState,

    // 清除缓存
    clearGlobalConstantsCache: (state) => {
      globalConstantsManager.clearCache();
      state.constants = {};
      state.lastUpdated = null;
      state.initialized = false;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // 初始化全局枚举配置
      .addCase(initGlobalConstants.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(initGlobalConstants.fulfilled, (state, action) => {
        state.loading = false;
        const { constants, lastUpdated, fromCache } = action.payload;

        state.constants = constants || {};
        state.lastUpdated = lastUpdated;
        state.initialized = true;

        if (fromCache) {
          console.log("全局枚举配置已从缓存加载");
        }
      })
      .addCase(initGlobalConstants.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || { message: "初始化全局枚举配置失败" };
        console.error("Redux错误:", state.error);
      })

      // 获取全局枚举配置
      .addCase(fetchGlobalConstants.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchGlobalConstants.fulfilled, (state, action) => {
        state.loading = false;

        if (action.payload && action.payload.data) {
          state.constants = action.payload.data;
          state.lastUpdated = new Date().toISOString();
          state.initialized = true;
          console.log("全局枚举配置已更新:", state.constants);
        }
      })
      .addCase(fetchGlobalConstants.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || { message: "获取全局枚举配置失败" };
        console.error("Redux错误:", state.error);
      })

      // 刷新全局枚举配置
      .addCase(refreshGlobalConstants.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(refreshGlobalConstants.fulfilled, (state, action) => {
        state.loading = false;

        if (action.payload && action.payload.data) {
          state.constants = action.payload.data;
          state.lastUpdated = new Date().toISOString();
          state.initialized = true;
          console.log("全局枚举配置已刷新:", state.constants);
        }
      })
      .addCase(refreshGlobalConstants.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || { message: "刷新全局枚举配置失败" };
        console.error("Redux错误:", state.error);
      });
  },
});

// 导出 actions
export const { resetGlobalConstantsState, clearGlobalConstantsCache } =
  globalConstantsSlice.actions;

// 导出 reducer
export default globalConstantsSlice.reducer;
