import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { Card, Form, Input, Button, Space, Typography, message, Select } from 'antd';
import { SaveOutlined, UndoOutlined } from '@ant-design/icons';
import { useGlobalConstants } from '@/hooks/useGlobalConstants';
import {
  addLanguageConfig,
  updateLanguageConfig,
  batchAddLanguageConfig,
  batchUpdateLanguageCode,
} from '../../../redux/languageConfigPage/languageConfigPageSlice';

const { Title } = Typography;
const { Option } = Select;

// 注意：语言选项已移至组件内部，使用全局枚举

function AddLanguageConfigComponent({
  editingRecord,
  onSave,
  onCancel,
  tabType,
  selectedRowKeys,
  tabKey,
  saveTabFormData,
  getTabFormData,
  clearTabFormData
}) {
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState(false);

  // 获取全局枚举配置
  const { getSelectOptions } = useGlobalConstants();

  // 获取语言选项（Locale.Language枚举显示和传值都使用key字段）
  const languageOptions = getSelectOptions('Locale.Language').map(option => ({
    label: option.key,  // Locale.Language 特殊：显示用 key
    value: option.key   // 传值也用 key
  }));

  // 智能表单初始化
  useEffect(() => {
    if (editingRecord && tabType === 'edit') {
      form.setFieldsValue({
        language: editingRecord.language,
        langKey: editingRecord.langKey,
        languageValue: editingRecord.languageValue,
      });
    } else {
      // 添加模式：尝试恢复之前保存的表单数据
      if (tabKey && getTabFormData) {
        const savedFormData = getTabFormData(tabKey);
        if (savedFormData) {
          form.setFieldsValue(savedFormData);
        } else {
          form.resetFields();
        }
      } else {
        form.resetFields();
      }
    }
  }, [editingRecord, form, tabType, tabKey, getTabFormData]);

  const handleFormSubmit = async () => {
    try {
      setSubmitting(true);
      const values = await form.validateFields();

      if (tabType === 'edit' && editingRecord) {
        // 编辑模式
        await dispatch(
          updateLanguageConfig({
            id: editingRecord.id,
            data: values,
          })
        ).unwrap();
        message.success('更新成功');
      } else if (tabType === 'add') {
        // 添加模式
        await dispatch(addLanguageConfig(values)).unwrap();
        message.success('添加成功');
        form.resetFields();
      } else if (tabType === 'batchAdd') {
        // 批量添加模式
        const { keys, values: langValues, language } = values;
        const entries = keys
          .split("\n")
          .map((key, index) => {
            const value = langValues.split("\n")[index] || "";
            return { language, langKey: key.trim(), languageValue: value.trim() };
          })
          .filter((entry) => entry.langKey);

        await dispatch(batchAddLanguageConfig(entries)).unwrap();
        message.success('批量添加成功');
        form.resetFields();
      } else if (tabType === 'batchUpdate') {
        // 批量修改语言模式
        await dispatch(
          batchUpdateLanguageCode({
            ids: selectedRowKeys,
            language: values.language,
          })
        ).unwrap();
        message.success('批量修改语言成功');
        form.resetFields();
      }

      // 清除表单数据（仅在添加模式下）
      if (tabType === 'add' && tabKey && clearTabFormData) {
        clearTabFormData(tabKey);
      }

      onSave && onSave();
    } catch (error) {
      console.error('操作失败:', error);
      const errorMessage =
        tabType === 'edit' ? '更新失败' :
          tabType === 'add' ? '添加失败' :
            tabType === 'batchAdd' ? '批量添加失败' :
              tabType === 'batchUpdate' ? '批量修改失败' : '操作失败';
      message.error(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    if (tabKey && clearTabFormData) {
      clearTabFormData(tabKey);
    }
    onCancel && onCancel();
  };

  const getTitle = () => {
    switch (tabType) {
      case 'edit':
        return '编辑语言配置';
      case 'add':
        return '添加语言配置';
      case 'batchAdd':
        return '批量添加语言配置';
      case 'batchUpdate':
        return '批量修改语言';
      default:
        return '语言配置';
    }
  };

  const renderFormItems = () => {
    if (tabType === 'batchAdd') {
      return (
        <>
          <Form.Item
            name="language"
            label="语言"
            rules={[{ required: true, message: "请选择语言" }]}
          >
            <Select
              placeholder="选择语言"
              options={languageOptions}
              showSearch
              filterOption={(input, option) =>
                (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
              }
            />
          </Form.Item>
          <Form.Item
            name="keys"
            label="语言键（每行一个）"
            rules={[{ required: true, message: "请输入语言键" }]}
          >
            <Input.TextArea rows={6} placeholder="输入语言键，每行一个" />
          </Form.Item>
          <Form.Item
            name="values"
            label="语言值（每行一个，与语言键一一对应）"
            rules={[{ required: true, message: "请输入语言值" }]}
          >
            <Input.TextArea
              rows={6}
              placeholder="输入语言值，每行一个，与语言键一一对应"
            />
          </Form.Item>
        </>
      );
    } else if (tabType === 'batchUpdate') {
      return (
        <Form.Item
          name="language"
          label="目标语言"
          rules={[{ required: true, message: "请选择语言" }]}
        >
          <Select
            placeholder="选择语言"
            options={languageOptions}
            showSearch
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
          />
        </Form.Item>
      );
    } else {
      // 普通添加和编辑模式
      return (
        <>
          <Form.Item
            name="language"
            label="语言"
            rules={[{ required: true, message: "请选择语言" }]}
          >
            <Select
              placeholder="选择语言"
              options={languageOptions}
              showSearch
              filterOption={(input, option) =>
                (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
              }
            />
          </Form.Item>
          <Form.Item
            name="langKey"
            label="语言键"
            rules={[{ required: true, message: "请输入语言键" }]}
          >
            <Input placeholder="输入语言键" />
          </Form.Item>
          <Form.Item
            name="languageValue"
            label="语言值"
            rules={[{ required: true, message: "请输入语言值" }]}
          >
            <Input.TextArea rows={4} placeholder="输入语言值" />
          </Form.Item>
        </>
      );
    }
  };

  return (
    <div>
      <Title level={3}>
        {getTitle()}
      </Title>
      <Card>
        <Form
          form={form}
          layout="vertical"
          style={{ maxWidth: 600 }}
          onValuesChange={() => {
            // 实时保存表单数据（仅在添加模式下）
            if (tabType === 'add' && tabKey && saveTabFormData) {
              const formData = form.getFieldsValue();
              const hasData = Object.values(formData).some(
                (value) => value && value.toString().trim() !== ""
              );
              if (hasData) {
                saveTabFormData(tabKey, formData);
              }
            }
          }}
        >
          {renderFormItems()}
          <Form.Item>
            <Space>
              <Button
                type="primary"
                onClick={handleFormSubmit}
                icon={<SaveOutlined />}
                loading={submitting}
              >
                保存
              </Button>
              <Button
                onClick={handleCancel}
                icon={<UndoOutlined />}
                disabled={submitting}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
}

export default AddLanguageConfigComponent;
