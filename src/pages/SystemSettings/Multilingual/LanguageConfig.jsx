import React, { useState, useEffect } from "react";
import {
  Card,
  Table,
  Button,
  Space,
  Select,
  Input,
  DatePicker,
  Typography,
  Row,
  Col,
  message,
  Popconfirm,
} from "antd";
import {
  DeleteOutlined,
  PlusOutlined,
  UploadOutlined,
  DownloadOutlined,
  EditOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchLanguageList,
  setSelectedRowKeys,
  setFilters,
  resetFilters,
  deleteLanguageConfig,
  batchDeleteLanguageConfig,
  addLanguageConfig,
  updateLanguageConfig,
  batchAddLanguageConfig,
  batchUpdateLanguageCode,
} from "../../../redux/languageConfigPage/languageConfigPageSlice";
import dayjs from "dayjs";
import PageTabs from '@/components/PageTabs/PageTabs';
import AddLanguageConfigComponent from './AddLanguageConfigComponent';
import { usePageTabs } from '@/hooks/usePageTabs';
import { useGlobalConstants } from '@/hooks/useGlobalConstants';

const { Title } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

// 注意：语言选项映射和语言选项已移至组件内部，使用全局枚举

function LanguageConfigPage() {
  const dispatch = useDispatch();
  const { loading, languageList, pagination, filters, selectedRowKeys, deleteData, batchDeleteData } =
    useSelector((state) => state.languageConfigPage);

  // 计算是否有任何删除操作正在进行，用于覆盖整个表格的加载状态
  const isDeleting = deleteData.loading || batchDeleteData.loading;

  // 获取全局枚举配置
  const { getSelectOptions, getSelectOptionsByKey, getEnumName } = useGlobalConstants();

  const [dateRange, setDateRange] = useState([null, null]);

  // 获取语言选项（Locale.Language枚举显示和传值都使用key字段）
  const languageOptions = [
    { label: "全部语言", value: "全部语言" },
    ...getSelectOptions('Locale.Language').map(option => ({
      label: option.key,  // Locale.Language 特殊：显示用 key
      value: option.key   // 传值也用 key
    }))
  ];

  // 使用统一的标签管理 Hook
  const {
    activeTab, editingRecord, tabPanes,
    handleAdd, handleEdit, handleTabChange, handleTabEdit,
    handleSaveSuccess, handleCancel, isListTab, createTab,
    saveTabFormData, getTabFormData, clearTabFormData,
  } = usePageTabs({
    listTabLabel: '语言配置管理',
    tabTypes: {
      add: {
        label: '添加语言配置',
        prefix: 'add'
      },
      edit: {
        label: '编辑',
        prefix: 'edit',
        getLabelFn: (record) => `编辑语言配置 - ${record.langKey}`
      },
      batchAdd: {
        label: '批量添加',
        prefix: 'batch-add'
      },
      batchUpdate: {
        label: '批量修改语言',
        prefix: 'batch-update'
      }
    },
    dataList: languageList,
    onSaveSuccess: () => {
      dispatch(
        fetchLanguageList({
          current: pagination.current,
          pageSize: pagination.pageSize,
          language: filters.language,
          langKey: filters.langKey,
        })
      );
    },
  });

  // 初始加载数据
  useEffect(() => {
    dispatch(
      fetchLanguageList({
        current: 1,
        pageSize: 10,
      })
    );
  }, [dispatch]);

  // 表格列定义
  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 60,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "语言",
      dataIndex: "language",
      key: "language",
      width: 120,
      render: (text) => {
        // 对于 Locale.Language，直接显示 key 值
        return text;
      },
    },
    {
      title: "语言键",
      dataIndex: "langKey",
      key: "langKey",
      width: 150,
    },
    {
      title: "语言值",
      dataIndex: "languageValue",
      key: "languageValue",
      width: 300,
      ellipsis: true,
    },
    {
      title: "创建时间",
      dataIndex: "createTime",
      key: "createTime",
      width: 160,
      render: (text) => dayjs.unix(text).format("YYYY-MM-DD HH:mm:ss"),
      sorter: (a, b) => a.createTime - b.createTime,
    },
    {
      title: "更新时间",
      dataIndex: "updateTime",
      key: "updateTime",
      width: 160,
      render: (text) => dayjs.unix(text).format("YYYY-MM-DD HH:mm:ss"),
      sorter: (a, b) => a.updateTime - b.updateTime,
    },
    {
      title: "操作",
      key: "action",
      width: 150,
      fixed: "right",
      render: (_, record) => (
        <Space size="small">
          <Button
            size="small"
            onClick={() => handleEdit(record)}
            style={{
              backgroundColor: "#ff7a00",
              borderColor: "#ff7a00",
              color: "white",
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定删除该语言配置吗?"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button size="small" danger loading={deleteData.loading}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];



  // 批量删除
  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要删除的语言配置");
      return;
    }
    dispatch(batchDeleteLanguageConfig(selectedRowKeys));
    dispatch(setSelectedRowKeys([]));
  };

  // 批量修改语言
  const handleBatchModifyLanguage = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要修改语言的配置");
      return;
    }
    return createTab('batchUpdate');
  };

  // 批量添加
  const handleBatchAdd = () => {
    return createTab('batchAdd');
  };

  // 批量导出
  const handleBatchExport = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要导出的语言配置");
      return;
    }

    const selectedItems = languageList.filter((item) =>
      selectedRowKeys.includes(item.id)
    );
    const content = selectedItems
      .map((item) => `${item.langKey}=${item.languageValue}`)
      .join("\n");

    const blob = new Blob([content], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `language_export_${dayjs().format("YYYYMMDD_HHmmss")}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 批量更新
  const handleBatchUpdate = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要更新的语言配置");
      return;
    }
    message.success(`已更新 ${selectedRowKeys.length} 个语言配置`);
    dispatch(setSelectedRowKeys([]));
  };



  // 删除
  const handleDelete = (record) => {
    dispatch(deleteLanguageConfig(record.id));
  };

  // 搜索
  const handleSearch = () => {
    dispatch(
      fetchLanguageList({
        current: 1,
        pageSize: pagination.pageSize,
        language: filters.language === "全部语言" ? null : filters.language,
        langKey: filters.langKey,
        createTimeStart: filters.createTimeStart,
        createTimeEnd: filters.createTimeEnd,
      })
    );
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys) => {
      dispatch(setSelectedRowKeys(selectedRowKeys));
    },
  };

  return (
    <Card style={{ backgroundColor: '#fff' }}>
      {/* 页面标签栏 */}
      <div className="page-tabs-wrapper">
        <PageTabs
          activeKey={activeTab}
          onChange={handleTabChange}
          onEdit={handleTabEdit}
          items={tabPanes}
          type="editable-card"
        />
      </div>

      {/* 条件渲染 */}
      {isListTab ? (
        <>
          {/* 筛选条件 */}
          <Card style={{ marginBottom: 16 }}>
            <Row gutter={[16, 16]} align="middle">
              <Col>
                <Select
                  value={filters.language || "全部语言"}
                  onChange={(value) => dispatch(setFilters({ language: value }))}
                  style={{ width: 120 }}
                >
                  {languageOptions.map((option) => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Col>
              <Col>
                <Input
                  placeholder="语言键"
                  value={filters.langKey}
                  onChange={(e) =>
                    dispatch(setFilters({ langKey: e.target.value }))
                  }
                  style={{ width: 120 }}
                />
              </Col>
              <Col>
                <span>创建时间</span>
              </Col>
              <Col>
                <RangePicker
                  value={dateRange}
                  onChange={(dates) => {
                    setDateRange(dates);
                    dispatch(setFilters({
                      createTimeStart: dates && dates[0] ? dates[0].format('YYYY-MM-DD') : null,
                      createTimeEnd: dates && dates[1] ? dates[1].format('YYYY-MM-DD') : null
                    }));
                  }}
                  format="YYYY-MM-DD"
                  placeholder={["开始时间", "结束时间"]}
                  style={{ width: 240 }}
                />
              </Col>
              <Col>
                <Button type="primary" onClick={handleSearch}>
                  查询
                </Button>
              </Col>
              <Col>
                <Button onClick={() => {
                  dispatch(resetFilters());
                  setDateRange([null, null]);
                  dispatch(fetchLanguageList({
                    current: 1,
                    pageSize: pagination.pageSize,
                  }));
                  message.success("筛选条件已重置");
                }}>
                  重置
                </Button>
              </Col>
            </Row>
          </Card>

          {/* 批量操作按钮 */}
          <Card style={{ marginBottom: 16 }}>
            <Space wrap>
              <Button
                onClick={handleAdd}
                style={{
                  backgroundColor: "#ff7a00",
                  borderColor: "#ff7a00",
                  color: "white",
                }}
              >
                添加
              </Button>
              <Popconfirm
                title="确定删除所选项吗?"
                onConfirm={handleBatchDelete}
                okText="确定"
                cancelText="取消"
                disabled={selectedRowKeys.length === 0}
              >
                <Button
                  danger
                  icon={<DeleteOutlined />}
                  disabled={selectedRowKeys.length === 0}
                  loading={batchDeleteData.loading}
                >
                  批量删除
                </Button>
              </Popconfirm>
              <Button
                onClick={handleBatchModifyLanguage}
                disabled={selectedRowKeys.length === 0}
                style={{
                  backgroundColor: "#1890ff",
                  borderColor: "#1890ff",
                  color: "white",
                }}
              >
                批量修改语言
              </Button>
              <Button
                onClick={handleBatchAdd}
                style={{
                  backgroundColor: "#52c41a",
                  borderColor: "#52c41a",
                  color: "white",
                }}
              >
                批量添加
              </Button>
              <Button
                onClick={handleBatchExport}
                disabled={selectedRowKeys.length === 0}
                style={{
                  backgroundColor: "#722ed1",
                  borderColor: "#722ed1",
                  color: "white",
                }}
              >
                批量导出
              </Button>
              <Button
                onClick={handleBatchUpdate}
                disabled={selectedRowKeys.length === 0}
                style={{
                  backgroundColor: "#fa8c16",
                  borderColor: "#fa8c16",
                  color: "white",
                }}
              >
                批量更新
              </Button>
            </Space>
          </Card>

          {/* 数据表格 */}
          <Card>
            <Table
              rowKey="id"
              columns={columns}
              dataSource={languageList}
              loading={loading || isDeleting}
              rowSelection={rowSelection}
              pagination={{
                current: pagination.current,
                pageSize: pagination.pageSize,
                total: pagination.total,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]} 页 / 共 ${Math.ceil(
                    total / pagination.pageSize
                  )} 页`,
                pageSizeOptions: ["10", "20", "50"],
                size: "small",
              }}
              scroll={{ x: 1200 }}
              size="middle"
              bordered
              onChange={(pagination, filters, sorter) => {
                dispatch(
                  fetchLanguageList({
                    current: pagination.current,
                    pageSize: pagination.pageSize,
                    language:
                      filters.language === "全部语言" ? null : filters.language,
                    langKey: filters.langKey,
                  })
                );
              }}
            />
          </Card>
        </>
      ) : (
        /* 新的添加/编辑组件 */
        <AddLanguageConfigComponent
          editingRecord={activeTab.startsWith('add-') ? null : editingRecord}
          onSave={handleSaveSuccess}
          onCancel={handleCancel}
          tabType={activeTab.startsWith('batch-') ? activeTab.split('-')[1] : (activeTab.startsWith('add-') ? 'add' : 'edit')}
          selectedRowKeys={selectedRowKeys}
          tabKey={activeTab}
          saveTabFormData={saveTabFormData}
          getTabFormData={getTabFormData}
          clearTabFormData={clearTabFormData}
        />
      )}
    </Card>

  );
}

export default LanguageConfigPage;
