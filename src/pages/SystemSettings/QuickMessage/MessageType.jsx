import React, { useState, useEffect } from "react";
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  DatePicker,
  Typography,
  Row,
  Col,
  message,
  Tag,
  Popconfirm,
} from "antd";
import { DeleteOutlined, PlusOutlined, SearchOutlined } from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import dayjs from "dayjs";
import {
  fetchQuickMessageTypeList,
  removeQuickMessageType,
  batchRemoveQuickMessageType,
  batchUpdateStatus,
  setSearchParams,
  setSelectedRowKeys,
} from "../../../redux/quickMessageTypePage/quickMessageTypePageSlice";
import PageTabs from '@/components/PageTabs/PageTabs';
import AddMessageType from './AddMessageType';
import { usePageTabs } from '@/hooks/usePageTabs';
import { useGlobalConstants } from '@/hooks/useGlobalConstants';

const { Title } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

// Mock 数据 - 消息类型管理（与截图完全一致）
const mockMessageTypeData = [
  {
    key: "1",
    id: 1,
    状态: "禁用",
    消息类型名称: "退款处理 1",
    使用时间: '用于执行退款的数据处理流程，例如："订单处理"公司退款，请留意。',
    创建时间: "2024-07-29 11:50:13",
  },
  {
    key: "2",
    id: 2,
    状态: "禁用",
    消息类型名称: "退款处理 2",
    使用时间: '用于优惠券的数据处理流程，例如："其他公司退款，请留意。"',
    创建时间: "2025-05-30 11:50:13",
  },
  {
    key: "3",
    id: 3,
    状态: "禁用",
    消息类型名称: "其他 3",
    使用时间: '用于常见问题的数据处理流程，例如："其他公司退款，请留意。"',
    创建时间: "2025-02-15 11:50:13",
  },
  {
    key: "4",
    id: 4,
    状态: "启用",
    消息类型名称: "物流通知 4",
    使用时间: '用于问候语的数据处理流程，例如："退款处理"公司退款，请留意。',
    创建时间: "2024-08-19 11:50:13",
  },
  {
    key: "5",
    id: 5,
    状态: "禁用",
    消息类型名称: "常见问题 5",
    使用时间: '用于订单状态的数据处理流程，例如："退款处理"公司退款，请留意。',
    创建时间: "2024-10-23 11:50:13",
  },
  {
    key: "6",
    id: 6,
    状态: "启用",
    消息类型名称: "售后服务 6",
    使用时间: '用于订单状态的数据处理流程，例如："常见问题"公司退款，请留意。',
    创建时间: "2025-05-17 11:50:13",
  },
  {
    key: "7",
    id: 7,
    状态: "启用",
    消息类型名称: "常见问题 7",
    使用时间: '用于客户回复的数据处理流程，例如："售后服务"公司退款，请留意。',
    创建时间: "2025-03-07 11:50:13",
  },
  {
    key: "8",
    id: 8,
    状态: "启用",
    消息类型名称: "问候语 8",
    使用时间: '用于客户回复的数据处理流程，例如："售后服务"公司退款，请留意。',
    创建时间: "2024-09-17 11:50:13",
  },
  {
    key: "9",
    id: 9,
    状态: "启用",
    消息类型名称: "订单处理 9",
    使用时间: '用于优惠活动的数据处理流程，例如："售后服务"公司退款，请留意。',
    创建时间: "2024-06-24 11:50:13",
  },
  {
    key: "10",
    id: 10,
    状态: "启用",
    消息类型名称: "售后咨询 10",
    使用时间: '用于其他查询的数据处理流程，例如："投诉处理"公司退款，请留意。',
    创建时间: "2024-08-24 11:50:13",
  },
  {
    key: "11",
    id: 11,
    状态: "禁用",
    消息类型名称: "物流通知 11",
    使用时间: '用于优惠活动的数据处理流程，例如："客户回复"公司退款，请留意。',
    创建时间: "2024-08-29 11:50:13",
  },
  {
    key: "12",
    id: 12,
    状态: "启用",
    消息类型名称: "常见问题 12",
    使用时间: '用于其他查询的数据处理流程，例如："常见问题"公司退款，请留意。',
    创建时间: "2025-01-03 11:50:13",
  },
  {
    key: "13",
    id: 13,
    状态: "禁用",
    消息类型名称: "常见问题 13",
    使用时间: '用于退货处理的数据处理流程，例如："售后服务"公司退款，请留意。',
    创建时间: "2024-08-16 11:50:13",
  },
  {
    key: "14",
    id: 14,
    状态: "启用",
    消息类型名称: "优惠促销 14",
    使用时间: '用于问候语的数据处理流程，例如："系统消息"公司退款，请留意。',
    创建时间: "2024-08-23 11:50:13",
  },
  {
    key: "15",
    id: 15,
    状态: "启用",
    消息类型名称: "优惠促销 15",
    使用时间: '用于优惠活动的数据处理流程，例如："订单状态"公司退款，请留意。',
    创建时间: "2024-07-05 11:50:13",
  },
  {
    key: "16",
    id: 16,
    状态: "启用",
    消息类型名称: "订单处理 16",
    使用时间: '用于常见问题的数据处理流程，例如："退款处理"公司退款，请留意。',
    创建时间: "2024-10-31 11:50:13",
  },
  {
    key: "17",
    id: 17,
    状态: "启用",
    消息类型名称: "物流通知 17",
    使用时间: '用于客户回复的数据处理流程，例如："问候语"公司退款，请留意。',
    创建时间: "2025-02-28 11:50:13",
  },
  {
    key: "18",
    id: 18,
    状态: "禁用",
    消息类型名称: "问候语 18",
    使用时间: '用于客户回复的数据处理流程，例如："退货处理"公司退款，请留意。',
    创建时间: "2024-07-15 11:50:13",
  },
  {
    key: "19",
    id: 19,
    状态: "启用",
    消息类型名称: "投诉处理 19",
    使用时间: '用于退货处理的数据处理流程，例如："售后服务"公司退款，请留意。',
    创建时间: "2024-07-25 11:50:13",
  },
  {
    key: "20",
    id: 20,
    状态: "启用",
    消息类型名称: "其他 20",
    使用时间: '用于订单状态的数据处理流程，例如："售后服务"公司退款，请留意。',
    创建时间: "2024-11-28 11:50:13",
  },
];

function MessageTypePage() {
  const dispatch = useDispatch();

  // 从Redux获取状态
  const { list, searchParams, selectedRowKeys, deleteData, batchDeleteData } = useSelector(
    (state) => state.quickMessageType
  );
  const loading = list.loading;

  // 计算是否有任何删除操作正在进行，用于覆盖整个表格的加载状态
  const isDeleting = deleteData.loading || batchDeleteData.loading;

  // 获取全局枚举配置
  const { getSelectOptionsByKey, getEnumName } = useGlobalConstants();

  // 本地状态
  const [messageTypeFilter, setMessageTypeFilter] = useState("消息类型");
  const [statusFilter, setStatusFilter] = useState("全部状态");
  const [dateRange, setDateRange] = useState([null, null]);

  // 获取状态选项（Common.Status枚举显示和传值都使用key字段）
  const statusOptions = [
    { label: "全部状态", value: "全部状态" },
    ...getSelectOptionsByKey('Common.Status')
  ];

  // 使用通用的标签管理 Hook
  const {
    activeTab,
    editingRecord,
    tabPanes,
    handleAdd,
    handleEdit,
    handleTabChange,
    handleTabEdit,
    handleSaveSuccess,
    handleCancel,
    isListTab,
    saveTabFormData,
    getTabFormData,
    clearTabFormData,
  } = usePageTabs({
    listTabLabel: '消息类型',
    tabTypes: {
      add: {
        label: '添加消息类型',
        prefix: 'add'
      },
      edit: {
        label: '编辑',
        prefix: 'edit',
        getLabelFn: (record) => `编辑消息类型 - ${record.id}`
      }
    },
    dataList: list.data,
    onSaveSuccess: () => {
      dispatch(fetchQuickMessageTypeList({
        page: 1,
        pageSize: 20
      })); // 重新获取数据
    },
  });

  // 首次加载时获取数据
  useEffect(() => {
    dispatch(fetchQuickMessageTypeList({
      page: 1,
      pageSize: 20
    }));
  }, [dispatch]);

  // 获取状态标签颜色
  const getStatusColor = (status) => {
    switch (status) {
      case "启用":
        return "green";
      case "禁用":
        return "red";
      default:
        return "default";
    }
  };

  // 表格列定义
  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 60,
    },
    {
      title: "状态",
      dataIndex: "状态",
      key: "状态",
      width: 100,
      render: (status) => {
        // 使用全局枚举显示状态名称
        const statusName = getEnumName('Common.Status', status) || status;
        return <Tag color={getStatusColor(status)}>{statusName}</Tag>;
      },
    },
    {
      title: "类型名称",
      dataIndex: "消息类型名称",
      key: "消息类型名称",
      width: 150,
    },
    {
      title: "使用说明",
      dataIndex: "使用时间",
      key: "使用时间",
      width: 400,
      ellipsis: true,
    },
    {
      title: "创建时间",
      dataIndex: "创建时间",
      key: "创建时间",
      width: 160,
      sorter: (a, b) => new Date(a.创建时间) - new Date(b.创建时间),
    },
    {
      title: "操作",
      key: "action",
      width: 150,
      fixed: "right",
      render: (_, record) => (
        <Space size="small">
          <Button
            size="small"
            onClick={() => handleEdit(record)}
            style={{
              backgroundColor: "#ff7a00",
              borderColor: "#ff7a00",
              color: "white",
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定删除该条记录吗?"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button size="small" danger loading={deleteData.loading}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys) => {
      dispatch(setSelectedRowKeys(selectedKeys));
    },
  };

  // 处理批量删除
  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要删除的消息类型");
      return;
    }
    dispatch(batchRemoveQuickMessageType(selectedRowKeys));
  };

  // 处理批量修改状态
  const handleBatchModifyStatus = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要修改状态的消息类型");
      return;
    }

    dispatch(
      batchUpdateStatus({
        ids: selectedRowKeys,
        status: "1", // 默认设置为启用状态，可以根据需求修改
      })
    );
  };

  // 处理删除
  const handleDelete = (record) => {
    dispatch(removeQuickMessageType(record.id));
  };

  // 处理日期范围变更
  const handleDateRangeChange = (dates) => {
    setDateRange(dates || [null, null]);
  };

  // 处理查询
  const handleSearch = () => {
    const params = {
      typeName: messageTypeFilter === "消息类型" ? "" : messageTypeFilter,
      status: statusFilter === "全部状态" ? "" : statusFilter,
      startDate: dateRange && dateRange[0] ? dateRange[0].unix() : null,
      endDate: dateRange && dateRange[1] ? dateRange[1].unix() : null,
      page: 1,
      pageSize: 20,
    };

    dispatch(setSearchParams(params));
    dispatch(fetchQuickMessageTypeList(params));
  };

  return (
    <Card style={{ backgroundColor: '#fff' }}>
      {/* 页面标签栏 */}
      <div className="page-tabs-wrapper">
        <PageTabs
          activeKey={activeTab}
          onChange={handleTabChange}
          onEdit={handleTabEdit}
          items={tabPanes}
          type="editable-card"
        />
      </div>

      {/* 根据当前标签显示不同内容 */}
      {isListTab ? (
        <>
          {/* 筛选条件 */}
          <Card style={{ marginBottom: 16 }}>
            <Row gutter={[16, 16]} align="middle">
              <Col>
                <Select
                  value={messageTypeFilter}
                  onChange={setMessageTypeFilter}
                  style={{ width: 120 }}
                >
                  <Option value="消息类型">消息类型</Option>
                  <Option value="退款处理">退款处理</Option>
                  <Option value="物流通知">物流通知</Option>
                  <Option value="常见问题">常见问题</Option>
                  <Option value="售后服务">售后服务</Option>
                  <Option value="问候语">问候语</Option>
                  <Option value="订单处理">订单处理</Option>
                  <Option value="售后咨询">售后咨询</Option>
                  <Option value="优惠促销">优惠促销</Option>
                  <Option value="投诉处理">投诉处理</Option>
                  <Option value="其他">其他</Option>
                </Select>
              </Col>
              <Col>
                <Select
                  value={statusFilter}
                  onChange={setStatusFilter}
                  style={{ width: 120 }}
                  options={statusOptions}
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                  }
                />
              </Col>
              <Col>
                <span>创建时间</span>
              </Col>
              <Col>
                <RangePicker
                  value={dateRange}
                  onChange={handleDateRangeChange}
                  style={{ width: 280 }}
                />
              </Col>
              <Col>
                <Button
                  type="primary"
                  onClick={handleSearch}
                  loading={loading}
                  icon={<SearchOutlined />}
                >
                  查询
                </Button>
              </Col>
            </Row>
          </Card>

          {/* 批量操作按钮 */}
          <Card style={{ marginBottom: 16 }}>
            <Space wrap>
              <Button
                onClick={handleAdd}
                style={{
                  backgroundColor: "#ff7a00",
                  borderColor: "#ff7a00",
                  color: "white",
                }}
                icon={<PlusOutlined />}
              >
                添加
              </Button>
              <Popconfirm
                title="确定批量删除"
                description={`确定要删除选中的 ${selectedRowKeys.length} 个消息类型吗？`}
                onConfirm={handleBatchDelete}
                disabled={selectedRowKeys.length === 0}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  danger
                  icon={<DeleteOutlined />}
                  disabled={selectedRowKeys.length === 0}
                  loading={batchDeleteData.loading}
                >
                  批量删除
                </Button>
              </Popconfirm>
              <Popconfirm
                title="确定批量修改状态"
                description={`确定要修改选中的 ${selectedRowKeys.length} 个消息类型的状态吗？`}
                onConfirm={handleBatchModifyStatus}
                disabled={selectedRowKeys.length === 0}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  disabled={selectedRowKeys.length === 0}
                  style={{
                    backgroundColor: "#ff7a00",
                    borderColor: "#ff7a00",
                    color: "white",
                  }}
                >
                  批量修改状态
                </Button>
              </Popconfirm>
            </Space>
          </Card>

          {/* 数据表格 */}
          <Card>
            <Table
              columns={columns}
              dataSource={list.data}
              loading={loading || isDeleting}
              rowSelection={rowSelection}
              rowKey="id"
              pagination={{
                total: list.total,
                current: list.curPage,
                pageSize: 20,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `总计 ${total} 条数据`,
                onChange: (page, pageSize) => {
                  dispatch(
                    fetchQuickMessageTypeList({
                      ...searchParams,
                      page,
                      pageSize,
                    })
                  );
                },
                pageSizeOptions: ["10", "20", "50"],
                size: "small",
              }}
              scroll={{ x: 1200 }}
              size="middle"
              bordered
            />
          </Card>
        </>
      ) : (
        /* 添加/编辑标签内容 */
        <AddMessageType
          editingRecord={activeTab.startsWith('add-') ? null : editingRecord}
          onSave={handleSaveSuccess}
          onCancel={handleCancel}
          tabKey={activeTab}
          saveTabFormData={saveTabFormData}
          getTabFormData={getTabFormData}
          clearTabFormData={clearTabFormData}
        />
      )}
    </Card>
  );
}

export default MessageTypePage;
