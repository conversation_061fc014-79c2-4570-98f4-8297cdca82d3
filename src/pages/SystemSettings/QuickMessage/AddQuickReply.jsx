import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Card,
  Form,
  Input,
  Button,
  Space,
  Typography,
  Select,
  Spin,
  message,
} from 'antd';
import { useGlobalConstants } from '@/hooks/useGlobalConstants';
import { SaveOutlined, UndoOutlined } from '@ant-design/icons';
import {
  createQuickMessage,
  updateQuickMessageItem,
  fetchQuickMessageDetail,
} from '@/redux/quickMessagePage/quickMessagePageSlice';

const { Title } = Typography;
const { TextArea } = Input;
const { Option } = Select;

/**
 * 添加/编辑快速回复组件
 * @param {Object} props 组件属性
 * @param {Object} props.editingRecord 编辑记录对象，为null时表示添加模式
 * @param {Function} props.onSave 保存成功回调
 * @param {Function} props.onCancel 取消操作回调
 * @param {string} props.tabKey 标签页key
 * @param {Function} props.saveTabFormData 保存表单数据函数
 * @param {Function} props.getTabFormData 获取表单数据函数
 * @param {Function} props.clearTabFormData 清除表单数据函数
 */
function AddQuickReply({
  editingRecord,
  onSave,
  onCancel,
  tabKey,
  saveTabFormData,
  getTabFormData,
  clearTabFormData
}) {
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState(false);

  // 从Redux获取状态
  const { detail } = useSelector((state) => state.quickMessage);
  const loading = detail.loading;

  // 获取全局枚举配置
  const { getSelectOptions } = useGlobalConstants();

  // 获取语言选项（Locale.Language枚举显示和传值都使用key字段）
  const languageOptions = getSelectOptions('Locale.Language').map(option => ({
    label: option.key,  // Locale.Language 特殊：显示用 key
    value: option.key   // 传值也用 key
  }));

  // 智能表单初始化
  useEffect(() => {
    if (editingRecord && editingRecord.id) {
      // 编辑模式：获取详细数据
      dispatch(fetchQuickMessageDetail(editingRecord.id)).then((action) => {
        if (action.type === fetchQuickMessageDetail.fulfilled.type) {
          const detailData = action.payload.data;
          form.setFieldsValue({
            quickMessageTypeId: detailData.quickMessageTypeId,
            language: detailData.language,
            message: detailData.message,
            mark: detailData.mark,
          });
        }
      });
    } else {
      // 添加模式：尝试恢复之前保存的表单数据
      if (tabKey && getTabFormData) {
        const savedFormData = getTabFormData(tabKey);
        if (savedFormData) {
          form.setFieldsValue(savedFormData);
        } else {
          form.resetFields();
          form.setFieldsValue({
            language: 'zh_CN', // 默认中文
          });
        }
      } else {
        form.resetFields();
        form.setFieldsValue({
          language: 'zh_CN', // 默认中文
        });
      }
    }
  }, [editingRecord, form, dispatch, tabKey, getTabFormData]);

  // 处理表单提交
  const handleFormSubmit = async () => {
    try {
      setSubmitting(true);
      const values = await form.validateFields();

      if (editingRecord && editingRecord.id) {
        // 更新快速回复
        await dispatch(updateQuickMessageItem({
          id: editingRecord.id,
          data: values
        })).unwrap();
        message.success('快速回复更新成功');
      } else {
        // 添加快速回复
        await dispatch(createQuickMessage(values)).unwrap();
        message.success('快速回复添加成功');
        form.resetFields(); // 添加成功后重置表单
      }

      // 清除表单数据（仅在添加模式下）
      if (!editingRecord && tabKey && clearTabFormData) {
        clearTabFormData(tabKey);
      }

      onSave && onSave();
    } catch (error) {
      console.error('操作失败:', error);
      message.error(editingRecord ? '更新失败' : '添加失败');
    } finally {
      setSubmitting(false);
    }
  };

  // 处理取消操作
  const handleCancel = () => {
    form.resetFields();
    if (tabKey && clearTabFormData) {
      clearTabFormData(tabKey);
    }
    onCancel && onCancel();
  };

  return (
    <div>
      <Title level={3}>
        {editingRecord ? '编辑快速回复' : '添加快速回复'}
      </Title>

      <Card>
        <Spin spinning={loading}>
          <Form
            form={form}
            layout="vertical"
            style={{ maxWidth: 600 }}
            initialValues={{
              quickMessageTypeId: "",
              language: "zh_CN",
              message: "",
              mark: "",
            }}
            onValuesChange={() => {
              // 实时保存表单数据（仅在添加模式下）
              if (!editingRecord && tabKey && saveTabFormData) {
                const formData = form.getFieldsValue();
                const hasData = Object.values(formData).some(
                  (value) => value && value.toString().trim() !== ""
                );
                if (hasData) {
                  saveTabFormData(tabKey, formData);
                }
              }
            }}
          >
            <Form.Item
              name="quickMessageTypeId"
              label="消息类型ID"
              rules={[{ required: true, message: "请输入消息类型ID" }]}
            >
              <Input placeholder="请输入消息类型ID" />
            </Form.Item>

            <Form.Item
              name="language"
              label="语言"
              rules={[{ required: true, message: "请选择语言" }]}
            >
              <Select
                placeholder="请选择语言"
                options={languageOptions}
                showSearch
                filterOption={(input, option) =>
                  (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                }
              />
            </Form.Item>

            <Form.Item
              name="message"
              label="消息内容"
              rules={[{ required: true, message: "请输入消息内容" }]}
            >
              <TextArea rows={4} placeholder="请输入消息内容" />
            </Form.Item>

            <Form.Item
              name="mark"
              label="备注"
            >
              <Input placeholder="请输入备注" />
            </Form.Item>

            <Form.Item>
              <Space>
                <Button
                  type="primary"
                  onClick={handleFormSubmit}
                  icon={<SaveOutlined />}
                  loading={loading || submitting}
                >
                  保存
                </Button>
                <Button
                  onClick={handleCancel}
                  icon={<UndoOutlined />}
                  disabled={loading || submitting}
                >
                  取消
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Spin>
      </Card>
    </div>
  );
}

export default AddQuickReply;
