import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import TaskConfigForm from "../TaskConfigForm";
import taskConfigReducer from "../../../../redux/taskConfigPage/taskConfigPageSlice";

// 创建测试用的 store
const createTestStore = () => {
  return configureStore({
    reducer: {
      taskConfig: taskConfigReducer,
    },
  });
};

describe("TaskConfigForm 状态处理测试", () => {
  test("编辑时应该正确设置数字类型的状态值", async () => {
    const editingRecord = {
      id: 1,
      status: 1, // 数字类型
      taskName: "测试任务",
      mark: "测试备注",
      taskClass: "com.test.Task",
    };

    const store = createTestStore();
    const mockOnSave = jest.fn();
    const mockOnCancel = jest.fn();

    render(
      <Provider store={store}>
        <TaskConfigForm
          editingRecord={editingRecord}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      </Provider>
    );

    // 检查状态选择器是否正确设置了值
    const statusSelect = screen.getByDisplayValue("启用");
    expect(statusSelect).toBeInTheDocument();
  });

  test("编辑时应该正确设置字符串类型的状态值", async () => {
    const editingRecord = {
      id: 1,
      status: "0", // 字符串类型
      taskName: "测试任务",
      mark: "测试备注",
      taskClass: "com.test.Task",
    };

    const store = createTestStore();
    const mockOnSave = jest.fn();
    const mockOnCancel = jest.fn();

    render(
      <Provider store={store}>
        <TaskConfigForm
          editingRecord={editingRecord}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      </Provider>
    );

    // 检查状态选择器是否正确设置了值
    const statusSelect = screen.getByDisplayValue("禁用");
    expect(statusSelect).toBeInTheDocument();
  });

  test("新增时应该显示空表单", () => {
    const store = createTestStore();
    const mockOnSave = jest.fn();
    const mockOnCancel = jest.fn();

    render(
      <Provider store={store}>
        <TaskConfigForm
          editingRecord={null}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      </Provider>
    );

    // 检查表单标题
    expect(screen.getByText("添加任务配置")).toBeInTheDocument();

    // 检查状态选择器是否为空
    const statusSelect = screen.getByRole("combobox");
    expect(statusSelect).toHaveValue("");
  });

  test("状态选择器应该显示正确的选项文本", async () => {
    const store = createTestStore();
    const mockOnSave = jest.fn();
    const mockOnCancel = jest.fn();

    render(
      <Provider store={store}>
        <TaskConfigForm
          editingRecord={null}
          onSave={mockOnSave}
          onCancel={mockOnCancel}
        />
      </Provider>
    );

    // 点击状态选择器
    const statusSelect = screen.getByRole("combobox");
    fireEvent.mouseDown(statusSelect);

    // 等待选项出现
    await waitFor(() => {
      expect(screen.getByText("启用")).toBeInTheDocument();
      expect(screen.getByText("禁用")).toBeInTheDocument();
    });
  });
});
