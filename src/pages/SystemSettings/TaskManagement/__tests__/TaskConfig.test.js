import React from "react";
import { render, screen } from "@testing-library/react";
import { Provider } from "react-redux";
import { configureStore } from "@reduxjs/toolkit";
import TaskConfigPage from "../TaskConfig";
import taskConfigReducer from "../../../../redux/taskConfigPage/taskConfigPageSlice";

// 创建测试用的 store
const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      taskConfig: taskConfigReducer,
    },
    preloadedState: {
      taskConfig: {
        taskConfigList: [],
        loading: false,
        pagination: {
          current: 1,
          pageSize: 20,
          total: 0,
        },
        filters: {
          taskName: "",
          createTimeStart: null,
          createTimeEnd: null,
        },
        deleteData: { loading: false },
        batchDeleteData: { loading: false },
        ...initialState,
      },
    },
  });
};

// Mock usePageTabs hook
jest.mock("../../../../hooks/usePageTabs", () => ({
  usePageTabs: () => ({
    activeTab: "list",
    editingRecord: null,
    tabPanes: [{ key: "list", label: "任务配置", closable: false }],
    handleAdd: jest.fn(),
    handleEdit: jest.fn(),
    handleTabChange: jest.fn(),
    handleTabEdit: jest.fn(),
    handleSaveSuccess: jest.fn(),
    handleCancel: jest.fn(),
    isListTab: true,
  }),
}));

describe("TaskConfig 状态显示测试", () => {
  test("应该正确显示数字类型的状态", () => {
    const testData = [
      {
        id: 1,
        status: 1, // 数字类型
        taskName: "测试任务1",
        mark: "测试备注",
        taskClass: "com.test.Task1",
        createTime: **********,
        updateTime: **********,
      },
      {
        id: 2,
        status: 0, // 数字类型
        taskName: "测试任务2",
        mark: "测试备注2",
        taskClass: "com.test.Task2",
        createTime: **********,
        updateTime: **********,
      },
    ];

    const store = createTestStore({
      taskConfigList: testData,
    });

    render(
      <Provider store={store}>
        <TaskConfigPage />
      </Provider>
    );

    // 检查状态是否正确显示为文本
    expect(screen.getByText("启用")).toBeInTheDocument();
    expect(screen.getByText("禁用")).toBeInTheDocument();
  });

  test("应该正确显示字符串类型的状态", () => {
    const testData = [
      {
        id: 1,
        status: "1", // 字符串类型
        taskName: "测试任务1",
        mark: "测试备注",
        taskClass: "com.test.Task1",
        createTime: **********,
        updateTime: **********,
      },
      {
        id: 2,
        status: "0", // 字符串类型
        taskName: "测试任务2",
        mark: "测试备注2",
        taskClass: "com.test.Task2",
        createTime: **********,
        updateTime: **********,
      },
    ];

    const store = createTestStore({
      taskConfigList: testData,
    });

    render(
      <Provider store={store}>
        <TaskConfigPage />
      </Provider>
    );

    // 检查状态是否正确显示为文本
    expect(screen.getByText("启用")).toBeInTheDocument();
    expect(screen.getByText("禁用")).toBeInTheDocument();
  });

  test("应该正确处理未知状态", () => {
    const testData = [
      {
        id: 1,
        status: 999, // 未知状态
        taskName: "测试任务1",
        mark: "测试备注",
        taskClass: "com.test.Task1",
        createTime: **********,
        updateTime: **********,
      },
    ];

    const store = createTestStore({
      taskConfigList: testData,
    });

    render(
      <Provider store={store}>
        <TaskConfigPage />
      </Provider>
    );

    // 检查未知状态是否正确显示
    expect(screen.getByText("未知")).toBeInTheDocument();
  });
});
