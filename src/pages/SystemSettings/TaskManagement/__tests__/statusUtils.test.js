// 测试状态格式化函数
describe('状态格式化函数测试', () => {
  // 获取状态标签颜色
  const getStatusColor = (status) => {
    // 统一转换为字符串进行比较，兼容数字和字符串类型
    const statusStr = String(status);
    switch (statusStr) {
      case "1":
        return "green";
      case "0":
        return "red";
      default:
        return "gray";
    }
  };

  // 格式化状态显示文本
  const formatStatus = (status) => {
    // 统一转换为字符串进行比较，兼容数字和字符串类型
    const statusStr = String(status);
    if (statusStr === "1") return "启用";
    if (statusStr === "0") return "禁用";
    return "未知";
  };

  // 数据标准化函数 - 确保状态字段为字符串类型
  const normalizeTaskConfigData = (item) => {
    if (!item) return item;
    return {
      ...item,
      status: String(item.status), // 确保状态为字符串类型
    };
  };

  describe('getStatusColor', () => {
    test('应该为数字 1 返回 green', () => {
      expect(getStatusColor(1)).toBe('green');
    });

    test('应该为字符串 "1" 返回 green', () => {
      expect(getStatusColor("1")).toBe('green');
    });

    test('应该为数字 0 返回 red', () => {
      expect(getStatusColor(0)).toBe('red');
    });

    test('应该为字符串 "0" 返回 red', () => {
      expect(getStatusColor("0")).toBe('red');
    });

    test('应该为未知状态返回 gray', () => {
      expect(getStatusColor(999)).toBe('gray');
      expect(getStatusColor("unknown")).toBe('gray');
      expect(getStatusColor(null)).toBe('gray');
      expect(getStatusColor(undefined)).toBe('gray');
    });
  });

  describe('formatStatus', () => {
    test('应该为数字 1 返回 "启用"', () => {
      expect(formatStatus(1)).toBe('启用');
    });

    test('应该为字符串 "1" 返回 "启用"', () => {
      expect(formatStatus("1")).toBe('启用');
    });

    test('应该为数字 0 返回 "禁用"', () => {
      expect(formatStatus(0)).toBe('禁用');
    });

    test('应该为字符串 "0" 返回 "禁用"', () => {
      expect(formatStatus("0")).toBe('禁用');
    });

    test('应该为未知状态返回 "未知"', () => {
      expect(formatStatus(999)).toBe('未知');
      expect(formatStatus("unknown")).toBe('未知');
      expect(formatStatus(null)).toBe('未知');
      expect(formatStatus(undefined)).toBe('未知');
    });
  });

  describe('normalizeTaskConfigData', () => {
    test('应该将数字状态转换为字符串', () => {
      const input = {
        id: 1,
        status: 1,
        taskName: '测试任务',
      };
      const result = normalizeTaskConfigData(input);
      expect(result.status).toBe('1');
      expect(typeof result.status).toBe('string');
    });

    test('应该保持字符串状态不变', () => {
      const input = {
        id: 1,
        status: "0",
        taskName: '测试任务',
      };
      const result = normalizeTaskConfigData(input);
      expect(result.status).toBe('0');
      expect(typeof result.status).toBe('string');
    });

    test('应该处理 null 输入', () => {
      expect(normalizeTaskConfigData(null)).toBe(null);
    });

    test('应该处理 undefined 输入', () => {
      expect(normalizeTaskConfigData(undefined)).toBe(undefined);
    });

    test('应该保持其他字段不变', () => {
      const input = {
        id: 1,
        status: 1,
        taskName: '测试任务',
        mark: '备注',
        taskClass: 'com.test.Task',
      };
      const result = normalizeTaskConfigData(input);
      expect(result.id).toBe(1);
      expect(result.taskName).toBe('测试任务');
      expect(result.mark).toBe('备注');
      expect(result.taskClass).toBe('com.test.Task');
    });
  });
});
