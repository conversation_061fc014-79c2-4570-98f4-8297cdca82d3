import React from 'react';
import { Card, Table, Tag, Space, Typography } from 'antd';

const { Title, Paragraph } = Typography;

// 状态格式化函数（修复后的版本）
const getStatusColor = (status) => {
  // 统一转换为字符串进行比较，兼容数字和字符串类型
  const statusStr = String(status);
  switch (statusStr) {
    case "1":
      return "green";
    case "0":
      return "red";
    default:
      return "gray";
  }
};

const formatStatus = (status) => {
  // 统一转换为字符串进行比较，兼容数字和字符串类型
  const statusStr = String(status);
  if (statusStr === "1") return "启用";
  if (statusStr === "0") return "禁用";
  return "未知";
};

// 演示数据 - 包含不同类型的状态值
const demoData = [
  {
    id: 1,
    status: 1, // 数字类型
    taskName: '数字状态1',
    description: '后端返回数字 1',
  },
  {
    id: 2,
    status: 0, // 数字类型
    taskName: '数字状态0',
    description: '后端返回数字 0',
  },
  {
    id: 3,
    status: "1", // 字符串类型
    taskName: '字符串状态"1"',
    description: '后端返回字符串 "1"',
  },
  {
    id: 4,
    status: "0", // 字符串类型
    taskName: '字符串状态"0"',
    description: '后端返回字符串 "0"',
  },
  {
    id: 5,
    status: 999, // 未知状态
    taskName: '未知状态',
    description: '后端返回未知值 999',
  },
];

const StatusDemo = () => {
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 60,
    },
    {
      title: '原始状态值',
      dataIndex: 'status',
      key: 'originalStatus',
      width: 120,
      render: (status) => (
        <code style={{ backgroundColor: '#f5f5f5', padding: '2px 4px' }}>
          {typeof status === 'string' ? `"${status}"` : String(status)} 
          <br />
          <small>({typeof status})</small>
        </code>
      ),
    },
    {
      title: '状态显示',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={getStatusColor(status)}>{formatStatus(status)}</Tag>
      ),
    },
    {
      title: '任务名称',
      dataIndex: 'taskName',
      key: 'taskName',
      width: 150,
    },
    {
      title: '说明',
      dataIndex: 'description',
      key: 'description',
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <Title level={2}>任务配置状态显示修复演示</Title>
        
        <Paragraph>
          <strong>问题描述：</strong>
          原来的代码只能正确处理字符串类型的状态值（"0", "1"），
          当后端返回数字类型（0, 1）时，状态显示会出现问题。
        </Paragraph>

        <Paragraph>
          <strong>修复方案：</strong>
          <ul>
            <li>在 <code>getStatusColor</code> 和 <code>formatStatus</code> 函数中使用 <code>String(status)</code> 统一转换为字符串进行比较</li>
            <li>在 Redux slice 中添加数据标准化函数，确保状态字段始终为字符串类型</li>
            <li>在表单组件中确保编辑时状态值为字符串类型</li>
          </ul>
        </Paragraph>

        <Title level={3}>演示效果</Title>
        <Paragraph>
          下表展示了不同类型状态值的处理效果，可以看到无论后端返回数字还是字符串，
          都能正确显示为对应的中文文本：
        </Paragraph>

        <Table
          columns={columns}
          dataSource={demoData}
          rowKey="id"
          pagination={false}
          bordered
          size="middle"
        />

        <div style={{ marginTop: '24px' }}>
          <Title level={4}>修复前后对比</Title>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Card size="small" title="修复前" style={{ backgroundColor: '#fff2f0' }}>
              <Paragraph>
                • 数字 1 → 显示为 "未知" ❌<br />
                • 数字 0 → 显示为 "未知" ❌<br />
                • 字符串 "1" → 显示为 "启用" ✅<br />
                • 字符串 "0" → 显示为 "禁用" ✅
              </Paragraph>
            </Card>
            <Card size="small" title="修复后" style={{ backgroundColor: '#f6ffed' }}>
              <Paragraph>
                • 数字 1 → 显示为 "启用" ✅<br />
                • 数字 0 → 显示为 "禁用" ✅<br />
                • 字符串 "1" → 显示为 "启用" ✅<br />
                • 字符串 "0" → 显示为 "禁用" ✅
              </Paragraph>
            </Card>
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default StatusDemo;
