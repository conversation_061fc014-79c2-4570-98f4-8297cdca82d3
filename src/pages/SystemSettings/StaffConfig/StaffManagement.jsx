import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  Card,
  Table,
  Button,
  Space,
  Select,
  Input,
  DatePicker,
  Typography,
  Row,
  Col,
  message,
  Tag,
  Popconfirm,
  Spin,
} from "antd";
import {
  DeleteOutlined,
  ExclamationCircleOutlined,
  LoadingOutlined,
} from "@ant-design/icons";
import { Modal } from "antd";
import dayjs from "dayjs";
import {
  fetchStaffList,
  fetchRoleList,
  removeStaff,
  removeBatchStaff,
  updateBatchStatus,
  saveStaff,
  setFilters,
  setPagination,
  fetchStaffDetail,
  resetCurrentStaff,
} from "../../../redux/staffManagementPage/staffManagementPageSlice";
import PageTabs from '@/components/PageTabs/PageTabs';
import AddStaffComponent from './AddStaffComponent';
import { usePageTabs } from '@/hooks/usePageTabs';
import { useGlobalConstants } from '@/hooks/useGlobalConstants';

const { Title } = Typography;
const { Option } = Select;

function StaffManagementPage() {
  const dispatch = useDispatch();
  const {
    staffList,
    roleList,
    loading,
    pagination,
    filters,
    submitLoading,
    currentStaff,
    detailLoading,
    batchDeleteLoading,
    deleteData,
    batchDeleteData,
  } = useSelector((state) => state.staffManagementPage);

  // 计算是否有任何删除操作正在进行，用于覆盖整个表格的加载状态
  const isDeleting = deleteData.loading || batchDeleteData.loading;

  // 获取全局枚举配置
  const { getSelectOptionsByKey, getEnumName } = useGlobalConstants();

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  // Modal状态管理
  const [batchStatusModal, setBatchStatusModal] = useState({
    visible: false,
    loading: false,
  });

  // 获取员工状态选项（Manage.Status枚举显示和传值都使用key字段）
  const staffStatusOptions = [
    { label: "全部状态", value: "全部状态" },
    ...getSelectOptionsByKey('Manage.Status')
  ];

  // 使用PageTabs管理标签页状态
  const {
    activeTab, editingRecord, tabPanes,
    handleAdd, handleEdit, handleTabChange, handleTabEdit,
    handleSaveSuccess, handleCancel, isListTab,
    saveTabFormData, getTabFormData, clearTabFormData,
  } = usePageTabs({
    listTabLabel: '员工管理',
    tabTypes: {
      add: {
        label: '添加员工',
        prefix: 'add'
      },
      edit: {
        label: '编辑',
        prefix: 'edit',
        getLabelFn: (record) => `编辑员工 - ${record.用户名}`
      }
    },
    dataList: staffList,
    onSaveSuccess: () => {
      dispatch(fetchStaffList({
        page: 1,
        pageSize: 20
      }));
    },
  });

  // 初始化加载数据
  useEffect(() => {
    dispatch(fetchStaffList({
      page: 1,
      pageSize: 20
    }));
    dispatch(fetchRoleList({
      page: 1,
      pageSize: 20
    }));
  }, [dispatch]);



  // 获取状态标签颜色
  const getStatusColor = (status) => {
    switch (status) {
      case "在职":
      case "1":
        return "green";
      case "离职":
      case "0":
        return "red";
      default:
        return "default";
    }
  };

  // 将API返回的数据转换为表格所需的格式
  const formatStaffData = (staff) => {
    return {
      key: staff.id.toString(),
      id: staff.id,
      状态: staff.status === "1" ? "在职" : "离职",
      角色: staff.roleName || "-",
      角色ID: staff.roleId,
      用户名: staff.username,
      姓名: `${staff.firstName || ""} ${staff.middleName || ""} ${staff.lastName || ""
        }`.trim(),
      邮箱: staff.email,
      电话: staff.phone,
      入职时间: staff.joinTime
        ? dayjs.unix(staff.joinTime).format("YYYY-MM-DD")
        : "-",
      // 原始数据保留，用于编辑
      rawData: staff,
    };
  };

  // 表格列定义
  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 60,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "状态",
      dataIndex: "状态",
      key: "状态",
      width: 100,
      render: (status) => {
        // 使用全局枚举显示状态名称
        const statusName = getEnumName('Manage.Status', status) || status;
        return <Tag color={getStatusColor(status)}>{statusName}</Tag>;
      },
    },
    {
      title: "角色",
      dataIndex: "角色",
      key: "角色",
      width: 120,
    },
    {
      title: "用户名",
      dataIndex: "用户名",
      key: "用户名",
      width: 120,
    },
    {
      title: "姓名",
      dataIndex: "姓名",
      key: "姓名",
      width: 150,
    },
    {
      title: "邮箱",
      dataIndex: "邮箱",
      key: "邮箱",
      width: 200,
      ellipsis: true,
    },
    {
      title: "电话",
      dataIndex: "电话",
      key: "电话",
      width: 140,
    },
    {
      title: "入职时间",
      dataIndex: "入职时间",
      key: "入职时间",
      width: 120,
      sorter: (a, b) => new Date(a.入职时间) - new Date(b.入职时间),
    },
    {
      title: "操作",
      key: "action",
      width: 150,
      fixed: "right",
      render: (_, record) => (
        <Space size="small">
          <Button
            size="small"
            onClick={() => handleEdit(record)}
            style={{
              backgroundColor: "#ff7a00",
              borderColor: "#ff7a00",
              color: "white",
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除此员工吗？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button size="small" danger loading={deleteData.loading}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys) => {
      setSelectedRowKeys(selectedKeys);
    },
  };



  // 处理批量删除
  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要删除的员工");
      return;
    }
  };

  // 执行批量删除
  const confirmBatchDelete = () => {
    // 将字符串ID转换为数字ID
    const ids = selectedRowKeys.map((key) => parseInt(key, 10));

    // 调用批量删除action
    dispatch(removeBatchStaff(ids))
      .unwrap()
      .then(() => {
        // 成功后清空选择
        setSelectedRowKeys([]);
      })
      .catch((err) => {
        console.error("批量删除失败:", err);
      });
  };

  // 处理批量修改状态
  const handleBatchModifyStatus = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要修改状态的员工");
      return;
    }

    setBatchStatusModal({ visible: true, loading: false });
  };

  // 处理批量状态修改操作
  const handleBatchStatusUpdate = async (status) => {
    try {
      setBatchStatusModal(prev => ({ ...prev, loading: true }));
      const ids = selectedRowKeys.map((key) => parseInt(key, 10));
      await dispatch(updateBatchStatus({ ids, status })).unwrap();
      setSelectedRowKeys([]);
      setBatchStatusModal({ visible: false, loading: false });
      const statusName = getEnumName('Manage.Status', status) || status;
      message.success(`批量设为${statusName}成功`);
    } catch (error) {
      setBatchStatusModal(prev => ({ ...prev, loading: false }));
      const statusName = getEnumName('Manage.Status', status) || status;
      message.error(`批量设为${statusName}失败`);
    }
  };

  // 关闭批量状态修改Modal
  const handleBatchStatusCancel = () => {
    setBatchStatusModal({ visible: false, loading: false });
  };



  // 处理删除
  const handleDelete = (record) => {
    dispatch(removeStaff(record.id));
  };

  // 处理查询
  const handleSearch = () => {
    dispatch(fetchStaffList({ page: 1 }));
  };



  // 处理表格分页、排序、过滤
  const handleTableChange = (pagination, filters, sorter) => {
    dispatch(
      setPagination({
        current: pagination.current,
        pageSize: pagination.pageSize,
      })
    );

    dispatch(
      fetchStaffList({
        page: pagination.current,
        pageSize: pagination.pageSize,
      })
    );
  };

  // 处理过滤条件变更
  const handleFilterChange = (field, value) => {
    dispatch(setFilters({ [field]: value }));
  };

  return (
    <Card style={{ backgroundColor: '#fff' }}>
      {/* 页面标签栏 */}
      <div className="page-tabs-wrapper">
        <PageTabs
          activeKey={activeTab}
          onChange={handleTabChange}
          onEdit={handleTabEdit}
          items={tabPanes}
          type="editable-card"
        />
      </div>

      {/* 条件渲染 */}
      {isListTab ? (
        <>
          {/* 筛选条件 */}
          <Card style={{ marginBottom: 16 }}>
            <Row gutter={[16, 16]} align="middle">
              <Col>
                <Select
                  value={filters.status}
                  onChange={(value) => handleFilterChange("status", value)}
                  style={{ width: 120 }}
                  options={staffStatusOptions}
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                  }
                />
              </Col>
              <Col>
                <Select
                  value={filters.roleId}
                  onChange={(value) => handleFilterChange("roleId", value)}
                  style={{ width: 140 }}
                  allowClear
                  placeholder="选择角色"
                >
                  {roleList.map((role) => (
                    <Option key={role.id} value={role.id}>
                      {role.name}
                    </Option>
                  ))}
                </Select>
              </Col>
              <Col>
                <Input
                  placeholder="搜索用户名"
                  value={filters.username}
                  onChange={(e) => handleFilterChange("username", e.target.value)}
                  style={{ width: 120 }}
                />
              </Col>
              <Col>
                <Input
                  placeholder="搜索邮箱"
                  value={filters.email}
                  onChange={(e) => handleFilterChange("email", e.target.value)}
                  style={{ width: 120 }}
                />
              </Col>
              <Col>
                <span>入职日期</span>
              </Col>
              <Col>
                <DatePicker
                  placeholder="年/月/日"
                  style={{ width: 120 }}
                  value={filters.joinTime ? dayjs(filters.joinTime) : null}
                  onChange={(date) => handleFilterChange("joinTime", date)}
                />
              </Col>
              <Col>
                <Button type="primary" onClick={handleSearch}>
                  查询
                </Button>
              </Col>
            </Row>
          </Card>

          {/* 批量操作按钮 */}
          <Card style={{ marginBottom: 16 }}>
            <Space wrap>
              <Button
                onClick={handleAdd}
                style={{
                  backgroundColor: "#ff7a00",
                  borderColor: "#ff7a00",
                  color: "white",
                }}
              >
                添加
              </Button>
              <Popconfirm
                title="确定要删除选中的员工吗？"
                onConfirm={confirmBatchDelete}
                okText="确定"
                cancelText="取消"
                disabled={selectedRowKeys.length === 0}
              >
                <Button
                  danger
                  icon={
                    batchDeleteLoading ? <LoadingOutlined /> : <DeleteOutlined />
                  }
                  onClick={handleBatchDelete}
                  disabled={selectedRowKeys.length === 0}
                  loading={batchDeleteLoading || batchDeleteData.loading}
                >
                  批量删除
                </Button>
              </Popconfirm>
              <Button
                onClick={handleBatchModifyStatus}
                disabled={selectedRowKeys.length === 0}
                style={{
                  backgroundColor: "#1890ff",
                  borderColor: "#1890ff",
                  color: "white",
                }}
              >
                批量修改状态
              </Button>
              {selectedRowKeys.length > 0 && (
                <span style={{ marginLeft: 8 }}>
                  已选择 <a style={{ fontWeight: 600 }}>{selectedRowKeys.length}</a>{" "}
                  项
                </span>
              )}
            </Space>
          </Card>

          {/* 数据表格 */}
          <Card>
            <Table
              columns={columns}
              dataSource={staffList.map(formatStaffData)}
              loading={loading || isDeleting}
              rowSelection={rowSelection}
              pagination={{
                ...pagination,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) =>
                  `第 ${pagination.current} 页 / 共 ${Math.ceil(
                    total / pagination.pageSize
                  )} 页`,
                pageSizeOptions: ["10", "20", "50"],
                size: "small",
              }}
              onChange={handleTableChange}
              scroll={{ x: 1400 }}
              size="middle"
              bordered
            />
          </Card>
        </>
      ) : (
        <AddStaffComponent
          editingRecord={activeTab.startsWith('add-') ? null : editingRecord}
          onSave={handleSaveSuccess}
          onCancel={handleCancel}
          tabKey={activeTab}
          saveTabFormData={saveTabFormData}
          getTabFormData={getTabFormData}
          clearTabFormData={clearTabFormData}
        />
      )}

      {/* 批量状态修改Modal */}
      <Modal
        title="批量修改员工状态"
        open={batchStatusModal.visible}
        onCancel={handleBatchStatusCancel}
        footer={null}
        width={400}
      >
        <div style={{ textAlign: 'center', padding: '20px 0' }}>
          <ExclamationCircleOutlined style={{ fontSize: '22px', color: '#faad14', marginBottom: '16px' }} />
          <p style={{ marginBottom: '24px', fontSize: '14px' }}>
            已选择 {selectedRowKeys.length} 个员工，请选择要修改的状态：
          </p>
          <Space size="middle">
            <Button
              danger
              loading={batchStatusModal.loading}
              onClick={() => handleBatchStatusUpdate("INACTIVE")}
              disabled={batchStatusModal.loading}
            >
              设为{getEnumName('Manage.Status', 'INACTIVE')}
            </Button>
            <Button
              type="primary"
              loading={batchStatusModal.loading}
              onClick={() => handleBatchStatusUpdate("ACTIVE")}
              disabled={batchStatusModal.loading}
            >
              设为{getEnumName('Manage.Status', 'ACTIVE')}
            </Button>
            <Button
              onClick={handleBatchStatusCancel}
              disabled={batchStatusModal.loading}
            >
              取消
            </Button>
          </Space>
        </div>
      </Modal>
    </Card>

  );
}

export default StaffManagementPage;
