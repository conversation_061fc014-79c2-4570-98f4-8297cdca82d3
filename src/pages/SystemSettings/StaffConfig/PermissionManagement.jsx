import React, { useState, useEffect } from "react";
import {
  Card,
  Table,
  Button,
  Space,
  Select,
  Typography,
  Row,
  Col,
  message,
  Tag,
  Popconfirm,
} from "antd";
import { DeleteOutlined } from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchPermissionList,
  deletePermission,
  batchDeletePermissions,
  fetchAllStaffList,
  fetchAllResourcesList,
  setFilters,
  setPagination,
  setSelectedRowKeys,
  clearSelectedRowKeys,
} from "../../../redux/permissionManagementPage/permissionManagementPageSlice";
import PageTabs from '@/components/PageTabs/PageTabs';
import AddPermissionComponent from './AddPermissionComponent';
import { usePageTabs } from '@/hooks/usePageTabs';

const { Title } = Typography;
const { Option } = Select;

function PermissionManagementPage() {
  const dispatch = useDispatch();
  const { permissionList, loading, pagination, filters, selectedRowKeys, deleteData, batchDeleteData, filterOptions } =
    useSelector((state) => state.permissionManagementPage);

  // 计算是否有任何删除操作正在进行，用于覆盖整个表格的加载状态
  const isDeleting = deleteData.loading || batchDeleteData.loading;
  const { staffFilter, resourceFilter } = filters;

  // 使用PageTabs Hook
  const {
    activeTab, editingRecord, tabPanes,
    handleAdd, handleEdit, handleTabChange, handleTabEdit,
    handleSaveSuccess, handleCancel, isListTab,
    saveTabFormData, getTabFormData, clearTabFormData,
  } = usePageTabs({
    listTabLabel: '权限管理',
    tabTypes: {
      add: {
        label: '添加权限',
        prefix: 'add'
      },
      edit: {
        label: '编辑',
        prefix: 'edit',
        getLabelFn: (record) => `编辑权限 - ${record.username}`
      }
    },
    dataList: permissionList,
    onSaveSuccess: () => {
      dispatch(fetchPermissionList({
        page: 1,
        pageSize: 20
      }));
    },
  });

  // 初始化加载数据
  useEffect(() => {
    dispatch(fetchPermissionList({
      page: 1,
      pageSize: 20
    }));
    // 获取筛选选项数据
    dispatch(fetchAllStaffList());
    dispatch(fetchAllResourcesList());
  }, [dispatch]);

  // 表格列定义
  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 60,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "员工",
      dataIndex: "username",
      key: "username",
      width: 100,
    },
    {
      title: "资源",
      dataIndex: "resourceMenuPath",
      key: "resourceMenuPath",
      width: 200,
      ellipsis: true,
      render: (text, record) => (
        <span>
          {record.resourceMenuPath} &gt; {record.resourceMenuName}
        </span>
      ),
    },
    {
      title: "新增按钮",
      dataIndex: "addButtons",
      key: "addButtons",
      width: 100,
      render: (text) => <Tag color="green">{text}</Tag>,
    },
    {
      title: "新增字段",
      dataIndex: "addFields",
      key: "addFields",
      width: 100,
      render: (text) => <Tag color="blue">{text}</Tag>,
    },
    {
      title: "新增URL",
      dataIndex: "addUrls",
      key: "addUrls",
      width: 100,
      render: (text) => <Tag color="orange">{text}</Tag>,
    },
    {
      title: "移除按钮",
      dataIndex: "removeButtons",
      key: "removeButtons",
      width: 100,
      render: (text) => <Tag color="red">{text}</Tag>,
    },
    {
      title: "移除字段",
      dataIndex: "removeFields",
      key: "removeFields",
      width: 100,
      render: (text) => <Tag color="purple">{text}</Tag>,
    },
    {
      title: "移除URL",
      dataIndex: "removeUrls",
      key: "removeUrls",
      width: 100,
      render: (text) => <Tag color="magenta">{text}</Tag>,
    },
    {
      title: "操作",
      key: "action",
      width: 150,
      fixed: "right",
      render: (_, record) => (
        <Space size="small">
          <Button
            size="small"
            onClick={() => handleEdit(record)}
            style={{
              backgroundColor: "#ff7a00",
              borderColor: "#ff7a00",
              color: "white",
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定删除此权限配置？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button size="small" danger loading={deleteData.loading}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys) => {
      dispatch(setSelectedRowKeys(selectedKeys));
    },
  };

  // 处理批量删除
  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要删除的权限配置");
      return;
    }
    dispatch(batchDeletePermissions(selectedRowKeys));
  };

  // 处理删除
  const handleDelete = (record) => {
    dispatch(deletePermission(record.id));
  };

  // 处理查询
  const handleSearch = () => {
    const params = {
      page: 1,
      pageSize: 20,
      username: staffFilter !== "全部员工" ? staffFilter : undefined,
      resourceMenuPath:
        resourceFilter !== "全部资源" ? resourceFilter : undefined,
    };
    dispatch(fetchPermissionList(params));
  };

  // 处理表格分页、排序、筛选变化
  const handleTableChange = (pagination, filters, sorter) => {
    dispatch(
      setPagination({
        current: pagination.current,
        pageSize: pagination.pageSize,
      })
    );

    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      username: staffFilter !== "全部员工" ? staffFilter : undefined,
      resourceMenuPath:
        resourceFilter !== "全部资源" ? resourceFilter : undefined,
    };

    if (sorter.field) {
      params.sortField = sorter.field;
      params.sortOrder = sorter.order === "ascend" ? "asc" : "desc";
    }

    dispatch(fetchPermissionList(params));
  };

  // 处理员工筛选变化
  const handleStaffFilterChange = (value) => {
    dispatch(setFilters({ staffFilter: value }));
  };

  // 处理资源筛选变化
  const handleResourceFilterChange = (value) => {
    dispatch(setFilters({ resourceFilter: value }));
  };

  return (
    <Card style={{ backgroundColor: '#fff' }}>
      {/* 页面标签栏 */}
      <div className="page-tabs-wrapper">
        <PageTabs
          activeKey={activeTab}
          onChange={handleTabChange}
          onEdit={handleTabEdit}
          items={tabPanes}
          type="editable-card"
        />
      </div>

      {/* 条件渲染 */}
      {isListTab ? (
        <>
          {/* 筛选条件 */}
          <Card style={{ marginBottom: 16 }}>
            <Row gutter={[16, 16]} align="middle">
              <Col>
                <Select
                  value={staffFilter}
                  onChange={handleStaffFilterChange}
                  style={{ width: 120 }}
                  loading={filterOptions.loading}
                >
                  <Option value="全部员工">全部员工</Option>
                  {filterOptions.staffList.map((staff) => (
                    <Option key={staff.id || staff.username} value={staff.username}>
                      {staff.username}
                    </Option>
                  ))}
                </Select>
              </Col>
              <Col>
                <Select
                  value={resourceFilter}
                  onChange={handleResourceFilterChange}
                  style={{ width: 160 }}
                  loading={filterOptions.loading}
                >
                  <Option value="全部资源">全部资源</Option>
                  {filterOptions.resourceList.map((resource) => (
                    <Option key={resource.id || resource.name} value={resource.name}>
                      {resource.name}
                    </Option>
                  ))}
                </Select>
              </Col>
              <Col>
                <Button type="primary" onClick={handleSearch}>
                  查询
                </Button>
              </Col>
            </Row>
          </Card>

          {/* 批量操作按钮 */}
          <Card style={{ marginBottom: 16 }}>
            <Space wrap>
              <Button
                onClick={handleAdd}
                style={{
                  backgroundColor: "#ff7a00",
                  borderColor: "#ff7a00",
                  color: "white",
                }}
              >
                添加
              </Button>
              <Popconfirm
                title={`确定删除选中的 ${selectedRowKeys.length} 个权限配置？`}
                onConfirm={handleBatchDelete}
                okText="确定"
                cancelText="取消"
                disabled={selectedRowKeys.length === 0}
              >
                <Button
                  danger
                  icon={<DeleteOutlined />}
                  disabled={selectedRowKeys.length === 0}
                  loading={batchDeleteData.loading}
                >
                  批量删除
                </Button>
              </Popconfirm>
            </Space>
          </Card>

          {/* 数据表格 */}
          <Card>
            <Table
              columns={columns}
              dataSource={permissionList.map((item) => ({ ...item, key: item.id }))}
              loading={loading || isDeleting}
              rowSelection={rowSelection}
              pagination={{
                ...pagination,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
                pageSizeOptions: ["10", "20", "50"],
                size: "small",
              }}
              onChange={handleTableChange}
              scroll={{ x: 1600 }}
              size="middle"
              bordered
            />
          </Card>
        </>
      ) : (
        /* 新的添加/编辑组件 */
        <AddPermissionComponent
          editingRecord={activeTab.startsWith('add-') ? null : editingRecord}
          onSave={handleSaveSuccess}
          onCancel={handleCancel}
          tabKey={activeTab}
          saveTabFormData={saveTabFormData}
          getTabFormData={getTabFormData}
          clearTabFormData={clearTabFormData}
        />
      )}
    </Card>
  );
}

export default PermissionManagementPage;
