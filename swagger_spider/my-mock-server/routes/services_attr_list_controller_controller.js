
// services-attr-list-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /services/attrs/{id}
   * 操作ID: getServiceAttrList
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/services/attrs/:id', (req, res) => {
    console.log(`[GET] /services/attrs/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseServicesAttrListResponse } = require('../models/responseservicesattrlistresponse');
    res.json(generateResponseServicesAttrListResponse());
  });

  /**
   * 
   * 原始路径: /services/attrs/{id}
   * 操作ID: updateServiceAttrList
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (ServicesAttrListRequest)} body参数 - 必填
   */
  app.put('/services/attrs/:id', (req, res) => {
    console.log(`[PUT] /services/attrs/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /services/attrs/{id}
   * 操作ID: deleteServiceAttrList
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/services/attrs/:id', (req, res) => {
    console.log(`[DELETE] /services/attrs/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /services/attrs/batch/services
   * 操作ID: batchUpdateServicesId
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 servicesId - 必填
   */
  app.put('/services/attrs/batch/services', (req, res) => {
    console.log(`[PUT] /services/attrs/batch/services 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /services/attrs/batch/attr
   * 操作ID: batchUpdateAttrId
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 attrId - 必填
   */
  app.put('/services/attrs/batch/attr', (req, res) => {
    console.log(`[PUT] /services/attrs/batch/attr 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /services/attrs
   * 操作ID: addServicesAttrList
 * @param {对象 (ServicesAttrListRequest)} body参数 - 必填
   */
  app.post('/services/attrs', (req, res) => {
    console.log(`[POST] /services/attrs 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /services/attrs/batch
   * 操作ID: batchAddServiceAttrList
 * @param {对象 (BatchServicesAttrListRequest)} body参数 - 必填
   */
  app.post('/services/attrs/batch', (req, res) => {
    console.log(`[POST] /services/attrs/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /services/attrs/batch
   * 操作ID: batchDeleteServiceAttributeList
 * @param {array} query参数 ids - 必填
   */
  app.delete('/services/attrs/batch', (req, res) => {
    console.log(`[DELETE] /services/attrs/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /services/attrs/list
   * 操作ID: getServiceAttrListList
 * @param {integer (int32)} query参数 servicesId - 可选
 * @param {integer (int32)} query参数 attrId - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/services/attrs/list', (req, res) => {
    console.log(`[GET] /services/attrs/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListServicesAttrListSimpleResponse } = require('../models/responsedatalistservicesattrlistsimpleresponse');
    res.json(generateResponseDataListServicesAttrListSimpleResponse());
  });
};
