
// posts-paid-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /posts/paid/{id}
   * 操作ID: getPostsPaid
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/posts/paid/:id', (req, res) => {
    console.log(`[GET] /posts/paid/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponsePostsPaidResponse } = require('../models/responsepostspaidresponse');
    res.json(generateResponsePostsPaidResponse());
  });

  /**
   * 
   * 原始路径: /posts/paid/{id}
   * 操作ID: updatePostsPaid
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (PostsPaidRequest)} body参数 - 必填
   */
  app.put('/posts/paid/:id', (req, res) => {
    console.log(`[PUT] /posts/paid/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /posts/paid/{id}
   * 操作ID: deletePostsPaid
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/posts/paid/:id', (req, res) => {
    console.log(`[DELETE] /posts/paid/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /posts/paid/batch/status
   * 操作ID: batchUpdatePostsPaidStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/posts/paid/batch/status', (req, res) => {
    console.log(`[PUT] /posts/paid/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /posts/paid
   * 操作ID: addPostsPaid
 * @param {对象 (PostsPaidRequest)} body参数 - 必填
   */
  app.post('/posts/paid', (req, res) => {
    console.log(`[POST] /posts/paid 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /posts/paid/list
   * 操作ID: listPostsPaid
 * @param {integer (int64)} query参数 postsId - 可选
 * @param {integer (int32)} query参数 userId - 可选
 * @param {string} query参数 status - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/posts/paid/list', (req, res) => {
    console.log(`[GET] /posts/paid/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListPostsPaidResponse } = require('../models/responsedatalistpostspaidresponse');
    res.json(generateResponseDataListPostsPaidResponse());
  });

  /**
   * 
   * 原始路径: /posts/paid/batch
   * 操作ID: batchDeletePostsPaid
 * @param {array} query参数 ids - 必填
   */
  app.delete('/posts/paid/batch', (req, res) => {
    console.log(`[DELETE] /posts/paid/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
