
// item-aftersales-stat-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /item/aftersales/stat/{id}
   * 操作ID: getItemAftersalesStat
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/item/aftersales/stat/:id', (req, res) => {
    console.log(`[GET] /item/aftersales/stat/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseItemAftersalesStatResponse } = require('../models/responseitemaftersalesstatresponse');
    res.json(generateResponseItemAftersalesStatResponse());
  });

  /**
   * 
   * 原始路径: /item/aftersales/stat/{id}
   * 操作ID: updateItemAftersalesStat
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (ItemAftersalesStatRequest)} body参数 - 必填
   */
  app.put('/item/aftersales/stat/:id', (req, res) => {
    console.log(`[PUT] /item/aftersales/stat/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /item/aftersales/stat/{id}
   * 操作ID: deleteItemAftersalesStat
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/item/aftersales/stat/:id', (req, res) => {
    console.log(`[DELETE] /item/aftersales/stat/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /item/aftersales/stat/batch/store
   * 操作ID: batchUpdateItemAftersalesStatStore
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 storeId - 必填
   */
  app.put('/item/aftersales/stat/batch/store', (req, res) => {
    console.log(`[PUT] /item/aftersales/stat/batch/store 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /item/aftersales/stat/batch/item
   * 操作ID: batchUpdateItemAftersalesStatItem
 * @param {array} query参数 ids - 必填
 * @param {integer (int64)} query参数 itemId - 必填
   */
  app.put('/item/aftersales/stat/batch/item', (req, res) => {
    console.log(`[PUT] /item/aftersales/stat/batch/item 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /item/aftersales/stat
   * 操作ID: addItemAftersalesStat
 * @param {对象 (ItemAftersalesStatRequest)} body参数 - 必填
   */
  app.post('/item/aftersales/stat', (req, res) => {
    console.log(`[POST] /item/aftersales/stat 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /item/aftersales/stat/list
   * 操作ID: listItemAftersalesStat
 * @param {integer (int32)} query参数 aftersalesId - 可选
 * @param {integer (int32)} query参数 storeId - 可选
 * @param {integer (int64)} query参数 itemId - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/item/aftersales/stat/list', (req, res) => {
    console.log(`[GET] /item/aftersales/stat/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListItemAftersalesStatResponse } = require('../models/responsedatalistitemaftersalesstatresponse');
    res.json(generateResponseDataListItemAftersalesStatResponse());
  });

  /**
   * 
   * 原始路径: /item/aftersales/stat/batch
   * 操作ID: batchDeleteItemAftersalesStat
 * @param {array} query参数 ids - 必填
   */
  app.delete('/item/aftersales/stat/batch', (req, res) => {
    console.log(`[DELETE] /item/aftersales/stat/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
