
// risk-control-words-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /risk/words/{id}
   * 操作ID: getRiskControlWords
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/risk/words/:id', (req, res) => {
    console.log(`[GET] /risk/words/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseRiskControlWordsResponse } = require('../models/responseriskcontrolwordsresponse');
    res.json(generateResponseRiskControlWordsResponse());
  });

  /**
   * 
   * 原始路径: /risk/words/{id}
   * 操作ID: updateRiskControlWords
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (RiskControlWordsRequest)} body参数 - 必填
   */
  app.put('/risk/words/:id', (req, res) => {
    console.log(`[PUT] /risk/words/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /risk/words/{id}
   * 操作ID: deleteRiskControlWords
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/risk/words/:id', (req, res) => {
    console.log(`[DELETE] /risk/words/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /risk/words
   * 操作ID: addRiskControlWords
 * @param {对象 (RiskControlWordsRequest)} body参数 - 必填
   */
  app.post('/risk/words', (req, res) => {
    console.log(`[POST] /risk/words 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /risk/words/list
   * 操作ID: listRiskControlWords
 * @param {string} query参数 language - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/risk/words/list', (req, res) => {
    console.log(`[GET] /risk/words/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListRiskControlWordsResponse } = require('../models/responsedatalistriskcontrolwordsresponse');
    res.json(generateResponseDataListRiskControlWordsResponse());
  });

  /**
   * 
   * 原始路径: /risk/words/batch
   * 操作ID: batchDeleteRiskControlWords
 * @param {array} query参数 ids - 必填
   */
  app.delete('/risk/words/batch', (req, res) => {
    console.log(`[DELETE] /risk/words/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });
};
