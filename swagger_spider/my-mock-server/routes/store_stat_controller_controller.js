
// store-stat-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /store/stat/{id}
   * 操作ID: getStoreStat
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/store/stat/:id', (req, res) => {
    console.log(`[GET] /store/stat/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseStoreStatResponse } = require('../models/responsestorestatresponse');
    res.json(generateResponseStoreStatResponse());
  });

  /**
   * 
   * 原始路径: /store/stat/{id}
   * 操作ID: updateStoreStat
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (StoreStatRequest)} body参数 - 必填
   */
  app.put('/store/stat/:id', (req, res) => {
    console.log(`[PUT] /store/stat/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /store/stat/{id}
   * 操作ID: deleteStoreStat
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/store/stat/:id', (req, res) => {
    console.log(`[DELETE] /store/stat/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /store/stat
   * 操作ID: addStoreStat
 * @param {对象 (StoreStatRequest)} body参数 - 必填
   */
  app.post('/store/stat', (req, res) => {
    console.log(`[POST] /store/stat 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /store/stat/list
   * 操作ID: listStoreStat
 * @param {integer (int32)} query参数 storeId - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/store/stat/list', (req, res) => {
    console.log(`[GET] /store/stat/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListStoreStatResponse } = require('../models/responsedataliststorestatresponse');
    res.json(generateResponseDataListStoreStatResponse());
  });

  /**
   * 
   * 原始路径: /store/stat/batch
   * 操作ID: batchDeleteStoreStat
 * @param {array} query参数 ids - 必填
   */
  app.delete('/store/stat/batch', (req, res) => {
    console.log(`[DELETE] /store/stat/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
