
// user-save-list-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /user/saves/{id}
   * 操作ID: getUserSaveList
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/user/saves/:id', (req, res) => {
    console.log(`[GET] /user/saves/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseUserSaveListResponse } = require('../models/responseusersavelistresponse');
    res.json(generateResponseUserSaveListResponse());
  });

  /**
   * 
   * 原始路径: /user/saves/{id}
   * 操作ID: updateUserSaveList
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (UserSaveListRequest)} body参数 - 必填
   */
  app.put('/user/saves/:id', (req, res) => {
    console.log(`[PUT] /user/saves/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /user/saves/{id}
   * 操作ID: deleteUserSaveList
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/user/saves/:id', (req, res) => {
    console.log(`[DELETE] /user/saves/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /user/saves/list
   * 操作ID: listUserSaveList
 * @param {integer (int32)} query参数 userId - 可选
 * @param {integer (int64)} query参数 relationId - 可选
 * @param {string} query参数 saveType - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/user/saves/list', (req, res) => {
    console.log(`[GET] /user/saves/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListUserSaveListResponse } = require('../models/responsedatalistusersavelistresponse');
    res.json(generateResponseDataListUserSaveListResponse());
  });

  /**
   * 
   * 原始路径: /user/saves/batch
   * 操作ID: batchDeleteUserSaveLists
 * @param {array} query参数 ids - 必填
   */
  app.delete('/user/saves/batch', (req, res) => {
    console.log(`[DELETE] /user/saves/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
