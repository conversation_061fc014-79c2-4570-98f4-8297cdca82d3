
// schedule-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /schedule/{id}
   * 操作ID: getScheduleById
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/schedule/:id', (req, res) => {
    console.log(`[GET] /schedule/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseScheduleResponse } = require('../models/responsescheduleresponse');
    res.json(generateResponseScheduleResponse());
  });

  /**
   * 
   * 原始路径: /schedule/{id}
   * 操作ID: updateSchedule
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (ScheduleUpdateRequest)} body参数 - 必填
   */
  app.put('/schedule/:id', (req, res) => {
    console.log(`[PUT] /schedule/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseBoolean } = require('../models/responseboolean');
    res.json(generateResponseBoolean());
  });

  /**
   * 
   * 原始路径: /schedule/{id}
   * 操作ID: deleteSchedule
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/schedule/:id', (req, res) => {
    console.log(`[DELETE] /schedule/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseBoolean } = require('../models/responseboolean');
    res.json(generateResponseBoolean());
  });

  /**
   * 
   * 原始路径: /schedule/batch/to
   * 操作ID: batchUpdateScheduleTo
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 to - 必填
   */
  app.put('/schedule/batch/to', (req, res) => {
    console.log(`[PUT] /schedule/batch/to 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseBoolean } = require('../models/responseboolean');
    res.json(generateResponseBoolean());
  });

  /**
   * 
   * 原始路径: /schedule/batch/status
   * 操作ID: batchUpdateScheduleStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/schedule/batch/status', (req, res) => {
    console.log(`[PUT] /schedule/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseBoolean } = require('../models/responseboolean');
    res.json(generateResponseBoolean());
  });

  /**
   * 
   * 原始路径: /schedule/batch/manage
   * 操作ID: batchUpdateManageId
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 manageId - 必填
   */
  app.put('/schedule/batch/manage', (req, res) => {
    console.log(`[PUT] /schedule/batch/manage 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseBoolean } = require('../models/responseboolean');
    res.json(generateResponseBoolean());
  });

  /**
   * 
   * 原始路径: /schedule/batch/from
   * 操作ID: batchUpdateScheduleFrom
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 from - 必填
   */
  app.put('/schedule/batch/from', (req, res) => {
    console.log(`[PUT] /schedule/batch/from 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseBoolean } = require('../models/responseboolean');
    res.json(generateResponseBoolean());
  });

  /**
   * 
   * 原始路径: /schedule
   * 操作ID: addSchedule
 * @param {对象 (ScheduleAddRequest)} body参数 - 必填
   */
  app.post('/schedule', (req, res) => {
    console.log(`[POST] /schedule 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /schedule/my-schedule
   * 操作ID: getMySchedule
 * @param {string} query参数 month - 必填
   */
  app.get('/schedule/my-schedule', (req, res) => {
    console.log(`[GET] /schedule/my-schedule 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListScheduleCalendarReportDTO } = require('../models/responsedatalistschedulecalendarreportdto');
    res.json(generateResponseDataListScheduleCalendarReportDTO());
  });

  /**
   * 
   * 原始路径: /schedule/list
   * 操作ID: getScheduleList
 * @param {string} query参数 status - 可选
 * @param {string} query参数 date - 可选
 * @param {string} query参数 from - 可选
 * @param {string} query参数 to - 可选
 * @param {integer (int32)} query参数 manageId - 可选
 * @param {integer (int32)} query参数 trueManageId - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/schedule/list', (req, res) => {
    console.log(`[GET] /schedule/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListScheduleResponse } = require('../models/responsedatalistscheduleresponse');
    res.json(generateResponseDataListScheduleResponse());
  });

  /**
   * 
   * 原始路径: /schedule/calendar-report
   * 操作ID: getCalendarReport
 * @param {string} query参数 month - 必填
 * @param {string} query参数 day - 可选
   */
  app.get('/schedule/calendar-report', (req, res) => {
    console.log(`[GET] /schedule/calendar-report 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListScheduleCalendarReportDTO } = require('../models/responsedatalistschedulecalendarreportdto');
    res.json(generateResponseDataListScheduleCalendarReportDTO());
  });

  /**
   * 
   * 原始路径: /schedule/attendance
   * 操作ID: getAttendanceReport
 * @param {string} query参数 month - 必填
   */
  app.get('/schedule/attendance', (req, res) => {
    console.log(`[GET] /schedule/attendance 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListScheduleAttendanceReportDTO } = require('../models/responsedatalistscheduleattendancereportdto');
    res.json(generateResponseDataListScheduleAttendanceReportDTO());
  });

  /**
   * 
   * 原始路径: /schedule/batch
   * 操作ID: batchScheduleDelete
 * @param {array} query参数 ids - 必填
   */
  app.delete('/schedule/batch', (req, res) => {
    console.log(`[DELETE] /schedule/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseBoolean } = require('../models/responseboolean');
    res.json(generateResponseBoolean());
  });
};
