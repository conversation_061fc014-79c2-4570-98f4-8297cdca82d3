
// user-logs-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /user/logs/batch/method
   * 操作ID: batchUpdateUserLogsMethod
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 method - 必填
   */
  app.put('/user/logs/batch/method', (req, res) => {
    console.log(`[PUT] /user/logs/batch/method 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /user/logs/{id}
   * 操作ID: getUserLogs
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/user/logs/:id', (req, res) => {
    console.log(`[GET] /user/logs/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseUserLogsResponse } = require('../models/responseuserlogsresponse');
    res.json(generateResponseUserLogsResponse());
  });

  /**
   * 
   * 原始路径: /user/logs/{id}
   * 操作ID: deleteUserLogs
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/user/logs/:id', (req, res) => {
    console.log(`[DELETE] /user/logs/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /user/logs/list
   * 操作ID: listUserLogs
 * @param {integer (int32)} query参数 userId - 可选
 * @param {string} query参数 method - 可选
 * @param {string} query参数 module - 可选
 * @param {string} query参数 action - 可选
 * @param {integer (int32)} query参数 code - 可选
 * @param {integer (int32)} query参数 ip - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/user/logs/list', (req, res) => {
    console.log(`[GET] /user/logs/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListUserLogsSimpleResponse } = require('../models/responsedatalistuserlogssimpleresponse');
    res.json(generateResponseDataListUserLogsSimpleResponse());
  });

  /**
   * 
   * 原始路径: /user/logs/batch
   * 操作ID: batchDeleteUserLogs
 * @param {array} query参数 ids - 必填
   */
  app.delete('/user/logs/batch', (req, res) => {
    console.log(`[DELETE] /user/logs/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
