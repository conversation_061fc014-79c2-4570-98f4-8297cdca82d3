
// api-resource-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /swagger-resources
   * 操作ID: swaggerResources

   */
  app.get('/swagger-resources', (req, res) => {
    console.log(`[GET] /swagger-resources 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateDynamicswaggerResourcesResponse } = require('../models/dynamicswaggerresourcesresponse');
    res.json(generateDynamicswaggerResourcesResponse());
  });

  /**
   * 
   * 原始路径: /swagger-resources
   * 操作ID: swaggerResources_3

   */
  app.put('/swagger-resources', (req, res) => {
    console.log(`[PUT] /swagger-resources 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateDynamicswaggerResources_3Response } = require('../models/dynamicswaggerresources_3response');
    res.json(generateDynamicswaggerResources_3Response());
  });

  /**
   * 
   * 原始路径: /swagger-resources
   * 操作ID: swaggerResources_2

   */
  app.post('/swagger-resources', (req, res) => {
    console.log(`[POST] /swagger-resources 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateDynamicswaggerResources_2Response } = require('../models/dynamicswaggerresources_2response');
    res.json(generateDynamicswaggerResources_2Response());
  });

  /**
   * 
   * 原始路径: /swagger-resources
   * 操作ID: swaggerResources_5

   */
  app.delete('/swagger-resources', (req, res) => {
    console.log(`[DELETE] /swagger-resources 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateDynamicswaggerResources_5Response } = require('../models/dynamicswaggerresources_5response');
    res.json(generateDynamicswaggerResources_5Response());
  });

  /**
   * 
   * 原始路径: /swagger-resources
   * 操作ID: swaggerResources_4

   */
  app.patch('/swagger-resources', (req, res) => {
    console.log(`[PATCH] /swagger-resources 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateDynamicswaggerResources_4Response } = require('../models/dynamicswaggerresources_4response');
    res.json(generateDynamicswaggerResources_4Response());
  });

  /**
   * 
   * 原始路径: /swagger-resources/configuration/ui
   * 操作ID: uiConfiguration

   */
  app.get('/swagger-resources/configuration/ui', (req, res) => {
    console.log(`[GET] /swagger-resources/configuration/ui 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateUiConfiguration } = require('../models/uiconfiguration');
    res.json(generateUiConfiguration());
  });

  /**
   * 
   * 原始路径: /swagger-resources/configuration/ui
   * 操作ID: uiConfiguration_3

   */
  app.put('/swagger-resources/configuration/ui', (req, res) => {
    console.log(`[PUT] /swagger-resources/configuration/ui 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateUiConfiguration } = require('../models/uiconfiguration');
    res.json(generateUiConfiguration());
  });

  /**
   * 
   * 原始路径: /swagger-resources/configuration/ui
   * 操作ID: uiConfiguration_2

   */
  app.post('/swagger-resources/configuration/ui', (req, res) => {
    console.log(`[POST] /swagger-resources/configuration/ui 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateUiConfiguration } = require('../models/uiconfiguration');
    res.json(generateUiConfiguration());
  });

  /**
   * 
   * 原始路径: /swagger-resources/configuration/ui
   * 操作ID: uiConfiguration_5

   */
  app.delete('/swagger-resources/configuration/ui', (req, res) => {
    console.log(`[DELETE] /swagger-resources/configuration/ui 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateUiConfiguration } = require('../models/uiconfiguration');
    res.json(generateUiConfiguration());
  });

  /**
   * 
   * 原始路径: /swagger-resources/configuration/ui
   * 操作ID: uiConfiguration_4

   */
  app.patch('/swagger-resources/configuration/ui', (req, res) => {
    console.log(`[PATCH] /swagger-resources/configuration/ui 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateUiConfiguration } = require('../models/uiconfiguration');
    res.json(generateUiConfiguration());
  });

  /**
   * 
   * 原始路径: /swagger-resources/configuration/security
   * 操作ID: securityConfiguration

   */
  app.get('/swagger-resources/configuration/security', (req, res) => {
    console.log(`[GET] /swagger-resources/configuration/security 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateSecurityConfiguration } = require('../models/securityconfiguration');
    res.json(generateSecurityConfiguration());
  });

  /**
   * 
   * 原始路径: /swagger-resources/configuration/security
   * 操作ID: securityConfiguration_3

   */
  app.put('/swagger-resources/configuration/security', (req, res) => {
    console.log(`[PUT] /swagger-resources/configuration/security 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateSecurityConfiguration } = require('../models/securityconfiguration');
    res.json(generateSecurityConfiguration());
  });

  /**
   * 
   * 原始路径: /swagger-resources/configuration/security
   * 操作ID: securityConfiguration_2

   */
  app.post('/swagger-resources/configuration/security', (req, res) => {
    console.log(`[POST] /swagger-resources/configuration/security 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateSecurityConfiguration } = require('../models/securityconfiguration');
    res.json(generateSecurityConfiguration());
  });

  /**
   * 
   * 原始路径: /swagger-resources/configuration/security
   * 操作ID: securityConfiguration_5

   */
  app.delete('/swagger-resources/configuration/security', (req, res) => {
    console.log(`[DELETE] /swagger-resources/configuration/security 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateSecurityConfiguration } = require('../models/securityconfiguration');
    res.json(generateSecurityConfiguration());
  });

  /**
   * 
   * 原始路径: /swagger-resources/configuration/security
   * 操作ID: securityConfiguration_4

   */
  app.patch('/swagger-resources/configuration/security', (req, res) => {
    console.log(`[PATCH] /swagger-resources/configuration/security 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateSecurityConfiguration } = require('../models/securityconfiguration');
    res.json(generateSecurityConfiguration());
  });
};
