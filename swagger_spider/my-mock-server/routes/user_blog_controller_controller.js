
// user-blog-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /user/blog/{id}
   * 操作ID: getUserBlog
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/user/blog/:id', (req, res) => {
    console.log(`[GET] /user/blog/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseUserBlogResponse } = require('../models/responseuserblogresponse');
    res.json(generateResponseUserBlogResponse());
  });

  /**
   * 
   * 原始路径: /user/blog/{id}
   * 操作ID: updateUserBlog
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (UserBlogRequest)} body参数 - 必填
   */
  app.put('/user/blog/:id', (req, res) => {
    console.log(`[PUT] /user/blog/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /user/blog/{id}
   * 操作ID: deleteUserBlog
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/user/blog/:id', (req, res) => {
    console.log(`[DELETE] /user/blog/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /user/blog/batch/status
   * 操作ID: batchUpdateUserBlogStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/user/blog/batch/status', (req, res) => {
    console.log(`[PUT] /user/blog/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /user/blog
   * 操作ID: addUserBlog
 * @param {对象 (UserBlogRequest)} body参数 - 必填
   */
  app.post('/user/blog', (req, res) => {
    console.log(`[POST] /user/blog 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /user/blog/list
   * 操作ID: listUserBlog
 * @param {integer (int32)} query参数 userId - 可选
 * @param {integer (int32)} query参数 storeId - 可选
 * @param {string} query参数 status - 可选
 * @param {string} query参数 authorName - 可选
 * @param {string} query参数 customUrl - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/user/blog/list', (req, res) => {
    console.log(`[GET] /user/blog/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListUserBlogSimpleResponse } = require('../models/responsedatalistuserblogsimpleresponse');
    res.json(generateResponseDataListUserBlogSimpleResponse());
  });

  /**
   * 
   * 原始路径: /user/blog/batch
   * 操作ID: batchDeleteUserBlogs
 * @param {array} query参数 ids - 必填
   */
  app.delete('/user/blog/batch', (req, res) => {
    console.log(`[DELETE] /user/blog/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
