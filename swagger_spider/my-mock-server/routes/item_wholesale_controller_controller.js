
// item-wholesale-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /item/wholesale/{id}
   * 操作ID: getItemWholesale
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/item/wholesale/:id', (req, res) => {
    console.log(`[GET] /item/wholesale/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseItemWholesaleResponse } = require('../models/responseitemwholesaleresponse');
    res.json(generateResponseItemWholesaleResponse());
  });

  /**
   * 
   * 原始路径: /item/wholesale/{id}
   * 操作ID: updateItemWholesale
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (ItemWholesaleRequest)} body参数 - 必填
   */
  app.put('/item/wholesale/:id', (req, res) => {
    console.log(`[PUT] /item/wholesale/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /item/wholesale/{id}
   * 操作ID: deleteItemWholesale
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/item/wholesale/:id', (req, res) => {
    console.log(`[DELETE] /item/wholesale/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /item/wholesale/batch/status
   * 操作ID: batchUpdateItemWholesaleStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/item/wholesale/batch/status', (req, res) => {
    console.log(`[PUT] /item/wholesale/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /item/wholesale
   * 操作ID: addItemWholesale
 * @param {对象 (ItemWholesaleRequest)} body参数 - 必填
   */
  app.post('/item/wholesale', (req, res) => {
    console.log(`[POST] /item/wholesale 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /item/wholesale/list
   * 操作ID: listItemWholesale
 * @param {integer (int64)} query参数 itemId - 可选
 * @param {string} query参数 status - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/item/wholesale/list', (req, res) => {
    console.log(`[GET] /item/wholesale/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListItemWholesaleResponse } = require('../models/responsedatalistitemwholesaleresponse');
    res.json(generateResponseDataListItemWholesaleResponse());
  });

  /**
   * 
   * 原始路径: /item/wholesale/batch
   * 操作ID: batchDeleteItemWholesale
 * @param {array} query参数 ids - 必填
   */
  app.delete('/item/wholesale/batch', (req, res) => {
    console.log(`[DELETE] /item/wholesale/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
