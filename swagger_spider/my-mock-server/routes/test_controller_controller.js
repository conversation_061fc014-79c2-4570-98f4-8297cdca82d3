
// test-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /test/protobuf
   * 操作ID: protobufAdd

   */
  app.put('/test/protobuf', (req, res) => {
    console.log(`[PUT] /test/protobuf 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /test/verify_captcha
   * 操作ID: verify_captcha
 * @param {string} header参数 key - 必填
 * @param {string} query参数 code - 必填
   */
  app.get('/test/verify_captcha', (req, res) => {
    console.log(`[GET] /test/verify_captcha 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /test/test_lang
   * 操作ID: testLang

   */
  app.get('/test/test_lang', (req, res) => {
    console.log(`[GET] /test/test_lang 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /test/test_error_code
   * 操作ID: testErrorCode

   */
  app.get('/test/test_error_code', (req, res) => {
    console.log(`[GET] /test/test_error_code 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /test/test_date
   * 操作ID: testDate
 * @param {string (date-time)} query参数 date - 可选
 * @param {string (date-time)} query参数 time - 可选
   */
  app.get('/test/test_date', (req, res) => {
    console.log(`[GET] /test/test_date 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /test/test_2fa
   * 操作ID: test2FA
 * @param {string} query参数 key - 可选
   */
  app.get('/test/test_2fa', (req, res) => {
    console.log(`[GET] /test/test_2fa 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /test/protobuf/{id}
   * 操作ID: protobuf
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/test/protobuf/:id', (req, res) => {
    console.log(`[GET] /test/protobuf/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateWsResponse } = require('../models/wsresponse');
    res.json(generateWsResponse());
  });

  /**
   * 
   * 原始路径: /test/permission/data
   * 操作ID: getAllPermission

   */
  app.get('/test/permission/data', (req, res) => {
    console.log(`[GET] /test/permission/data 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseMapStringMapStringListString } = require('../models/responsemapstringmapstringliststring');
    res.json(generateResponseMapStringMapStringListString());
  });

  /**
   * 
   * 原始路径: /test/miax_call
   * 操作ID: pathTest

   */
  app.get('/test/miax_call', (req, res) => {
    console.log(`[GET] /test/miax_call 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /test/manage_data_show
   * 操作ID: testManageDataShow
 * @param {integer (int32)} query参数 manageId - 可选
   */
  app.get('/test/manage_data_show', (req, res) => {
    console.log(`[GET] /test/manage_data_show 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseManageResponse } = require('../models/responsemanageresponse');
    res.json(generateResponseManageResponse());
  });

  /**
   * 
   * 原始路径: /test/index
   * 操作ID: index

   */
  app.get('/test/index', (req, res) => {
    console.log(`[GET] /test/index 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /test/download
   * 操作ID: download
 * @param {string} query参数 type - 必填
 * @param {integer (int32)} query参数 count - 必填
   */
  app.get('/test/download', (req, res) => {
    console.log(`[GET] /test/download 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateDynamicdownloadResponse } = require('../models/dynamicdownloadresponse');
    res.json(generateDynamicdownloadResponse());
  });

  /**
   * 
   * 原始路径: /test/check_ip
   * 操作ID: checkIp
 * @param {string} query参数 ip - 必填
   */
  app.get('/test/check_ip', (req, res) => {
    console.log(`[GET] /test/check_ip 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /test/captcha
   * 操作ID: captchaTest

   */
  app.get('/test/captcha', (req, res) => {
    console.log(`[GET] /test/captcha 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateDynamiccaptchaTestResponse } = require('../models/dynamiccaptchatestresponse');
    res.json(generateDynamiccaptchaTestResponse());
  });
};
