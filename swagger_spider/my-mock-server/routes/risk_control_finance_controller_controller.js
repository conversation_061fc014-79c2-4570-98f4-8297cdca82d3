
// risk-control-finance-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /risk/finance/{id}
   * 操作ID: getRiskControlFinance
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/risk/finance/:id', (req, res) => {
    console.log(`[GET] /risk/finance/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseRiskControlFinanceResponse } = require('../models/responseriskcontrolfinanceresponse');
    res.json(generateResponseRiskControlFinanceResponse());
  });

  /**
   * 
   * 原始路径: /risk/finance/{id}
   * 操作ID: updateRiskControlFinance
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (RiskControlFinanceRequest)} body参数 - 必填
   */
  app.put('/risk/finance/:id', (req, res) => {
    console.log(`[PUT] /risk/finance/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /risk/finance/{id}
   * 操作ID: deleteRiskControlFinance
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/risk/finance/:id', (req, res) => {
    console.log(`[DELETE] /risk/finance/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /risk/finance
   * 操作ID: addRiskControlFinance
 * @param {对象 (RiskControlFinanceRequest)} body参数 - 必填
   */
  app.post('/risk/finance', (req, res) => {
    console.log(`[POST] /risk/finance 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /risk/finance/list
   * 操作ID: listRiskControlFinance
 * @param {string} query参数 riskName - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/risk/finance/list', (req, res) => {
    console.log(`[GET] /risk/finance/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListRiskControlFinanceResponse } = require('../models/responsedatalistriskcontrolfinanceresponse');
    res.json(generateResponseDataListRiskControlFinanceResponse());
  });

  /**
   * 
   * 原始路径: /risk/finance/batch
   * 操作ID: batchDeleteRiskControlFinance
 * @param {array} query参数 ids - 必填
   */
  app.delete('/risk/finance/batch', (req, res) => {
    console.log(`[DELETE] /risk/finance/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });
};
