
// brand-services-list-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /brand/services/{id}
   * 操作ID: getBrandServicesList
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/brand/services/:id', (req, res) => {
    console.log(`[GET] /brand/services/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseBrandServicesListResponse } = require('../models/responsebrandserviceslistresponse');
    res.json(generateResponseBrandServicesListResponse());
  });

  /**
   * 
   * 原始路径: /brand/services/{id}
   * 操作ID: updateBrandServicesList
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (BrandServicesListRequest)} body参数 - 必填
   */
  app.put('/brand/services/:id', (req, res) => {
    console.log(`[PUT] /brand/services/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /brand/services/{id}
   * 操作ID: deleteBrandServicesList
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/brand/services/:id', (req, res) => {
    console.log(`[DELETE] /brand/services/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /brand/services/batch/services
   * 操作ID: batchUpdateBrandServicesListServices
 * @param {array} query参数 brandServicesListIds - 必填
 * @param {integer (int32)} query参数 servicesId - 必填
   */
  app.put('/brand/services/batch/services', (req, res) => {
    console.log(`[PUT] /brand/services/batch/services 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /brand/services/batch/brand
   * 操作ID: batchUpdateBrandServicesListBrand
 * @param {array} query参数 brandServicesListIds - 必填
 * @param {integer (int32)} query参数 brandId - 必填
   */
  app.put('/brand/services/batch/brand', (req, res) => {
    console.log(`[PUT] /brand/services/batch/brand 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /brand/services
   * 操作ID: addBrandServicesList
 * @param {对象 (BrandServicesListRequest)} body参数 - 必填
   */
  app.post('/brand/services', (req, res) => {
    console.log(`[POST] /brand/services 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /brand/services/list
   * 操作ID: getBrandServicesListList
 * @param {integer (int32)} query参数 brandId - 可选
 * @param {integer (int32)} query参数 servicesId - 可选
 * @param {string} query参数 required - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/brand/services/list', (req, res) => {
    console.log(`[GET] /brand/services/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListBrandServicesListSimpleResponse } = require('../models/responsedatalistbrandserviceslistsimpleresponse');
    res.json(generateResponseDataListBrandServicesListSimpleResponse());
  });

  /**
   * 
   * 原始路径: /brand/services/batch
   * 操作ID: batchDeleteBrandServicesList
 * @param {array} query参数 brandServicesListIds - 必填
   */
  app.delete('/brand/services/batch', (req, res) => {
    console.log(`[DELETE] /brand/services/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });
};
