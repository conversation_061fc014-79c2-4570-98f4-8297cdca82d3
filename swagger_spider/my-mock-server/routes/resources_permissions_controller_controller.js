
// resources-permissions-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /resources/permissions/{id}
   * 操作ID: getResourcesPermissions
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/resources/permissions/:id', (req, res) => {
    console.log(`[GET] /resources/permissions/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseResourcesPermissionsResponse } = require('../models/responseresourcespermissionsresponse');
    res.json(generateResponseResourcesPermissionsResponse());
  });

  /**
   * 
   * 原始路径: /resources/permissions/{id}
   * 操作ID: updateResourcesPermissions
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (ResourcesPermissionsRequest)} body参数 - 必填
   */
  app.put('/resources/permissions/:id', (req, res) => {
    console.log(`[PUT] /resources/permissions/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /resources/permissions/{id}
   * 操作ID: deleteResourcesPermissions
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/resources/permissions/:id', (req, res) => {
    console.log(`[DELETE] /resources/permissions/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /resources/permissions
   * 操作ID: addResourcesPermissions
 * @param {对象 (ResourcesPermissionsRequest)} body参数 - 必填
   */
  app.post('/resources/permissions', (req, res) => {
    console.log(`[POST] /resources/permissions 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /resources/permissions/list
   * 操作ID: listResourcesPermissions
 * @param {string} query参数 permissionName - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/resources/permissions/list', (req, res) => {
    console.log(`[GET] /resources/permissions/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListResourcesPermissionsSimpleResponse } = require('../models/responsedatalistresourcespermissionssimpleresponse');
    res.json(generateResponseDataListResourcesPermissionsSimpleResponse());
  });

  /**
   * 
   * 原始路径: /resources/permissions/batch
   * 操作ID: batchDeleteResourcesPermissions
 * @param {array} query参数 resourcesPermissionsIds - 必填
   */
  app.delete('/resources/permissions/batch', (req, res) => {
    console.log(`[DELETE] /resources/permissions/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });
};
