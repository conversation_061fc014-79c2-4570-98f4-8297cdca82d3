
// index-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /
   * 操作ID: manageIndex

   */
  app.get('/', (req, res) => {
    console.log(`[GET] / 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateDynamicmanageIndexResponse } = require('../models/dynamicmanageindexresponse');
    res.json(generateDynamicmanageIndexResponse());
  });
};
