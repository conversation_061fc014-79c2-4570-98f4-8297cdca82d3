
// rating-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /rating/{id}
   * 操作ID: getRating
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/rating/:id', (req, res) => {
    console.log(`[GET] /rating/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseRatingResponse } = require('../models/responseratingresponse');
    res.json(generateResponseRatingResponse());
  });

  /**
   * 
   * 原始路径: /rating/{id}
   * 操作ID: updateRating
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (RatingRequest)} body参数 - 必填
   */
  app.put('/rating/:id', (req, res) => {
    console.log(`[PUT] /rating/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /rating/{id}
   * 操作ID: deleteRating
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/rating/:id', (req, res) => {
    console.log(`[DELETE] /rating/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /rating/batch/status
   * 操作ID: batchUpdateRatingStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/rating/batch/status', (req, res) => {
    console.log(`[PUT] /rating/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /rating
   * 操作ID: addRating
 * @param {对象 (RatingRequest)} body参数 - 必填
   */
  app.post('/rating', (req, res) => {
    console.log(`[POST] /rating 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /rating/batch
   * 操作ID: batchAddRatings
 * @param {对象 (RatingBatchRequest)} body参数 - 必填
   */
  app.post('/rating/batch', (req, res) => {
    console.log(`[POST] /rating/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /rating/batch
   * 操作ID: batchDeleteRatings
 * @param {array} query参数 ids - 必填
   */
  app.delete('/rating/batch', (req, res) => {
    console.log(`[DELETE] /rating/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /rating/list
   * 操作ID: getRatingList
 * @param {string} query参数 status - 可选
 * @param {string} query参数 ratingType - 可选
 * @param {string} query参数 language - 可选
 * @param {string} query参数 langStatus - 可选
 * @param {string} query参数 ratingName - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/rating/list', (req, res) => {
    console.log(`[GET] /rating/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListRatingResponse } = require('../models/responsedatalistratingresponse');
    res.json(generateResponseDataListRatingResponse());
  });
};
