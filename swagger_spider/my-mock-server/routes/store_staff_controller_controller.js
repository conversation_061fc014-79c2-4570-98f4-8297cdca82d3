
// store-staff-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /store/staff/{id}
   * 操作ID: getStoreStaff
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/store/staff/:id', (req, res) => {
    console.log(`[GET] /store/staff/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseStoreStaffResponse } = require('../models/responsestorestaffresponse');
    res.json(generateResponseStoreStaffResponse());
  });

  /**
   * 
   * 原始路径: /store/staff/{id}
   * 操作ID: updateStoreStaff
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (StoreStaffUpdateRequest)} body参数 - 必填
   */
  app.put('/store/staff/:id', (req, res) => {
    console.log(`[PUT] /store/staff/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /store/staff/{id}
   * 操作ID: deleteStoreStaff
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/store/staff/:id', (req, res) => {
    console.log(`[DELETE] /store/staff/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /store/staff/batch/store
   * 操作ID: batchUpdateStoreStaffStore
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 storeId - 必填
   */
  app.put('/store/staff/batch/store', (req, res) => {
    console.log(`[PUT] /store/staff/batch/store 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /store/staff/batch/status
   * 操作ID: batchUpdateStoreStaffStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/store/staff/batch/status', (req, res) => {
    console.log(`[PUT] /store/staff/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /store/staff
   * 操作ID: addStoreStaff
 * @param {对象 (StoreStaffAddRequest)} body参数 - 必填
   */
  app.post('/store/staff', (req, res) => {
    console.log(`[POST] /store/staff 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /store/staff/permissions
   * 操作ID: getStaffPermissions

   */
  app.get('/store/staff/permissions', (req, res) => {
    console.log(`[GET] /store/staff/permissions 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseListStoreStaffPermissionResponse } = require('../models/responseliststorestaffpermissionresponse');
    res.json(generateResponseListStoreStaffPermissionResponse());
  });

  /**
   * 
   * 原始路径: /store/staff/list
   * 操作ID: listStoreStaff
 * @param {integer (int32)} query参数 storeId - 可选
 * @param {string} query参数 status - 可选
 * @param {string} query参数 title - 可选
 * @param {string} query参数 nickName - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/store/staff/list', (req, res) => {
    console.log(`[GET] /store/staff/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListStoreStaffSimpleResponse } = require('../models/responsedataliststorestaffsimpleresponse');
    res.json(generateResponseDataListStoreStaffSimpleResponse());
  });

  /**
   * 
   * 原始路径: /store/staff/batch
   * 操作ID: batchDeleteStoreStaff
 * @param {array} query参数 ids - 必填
   */
  app.delete('/store/staff/batch', (req, res) => {
    console.log(`[DELETE] /store/staff/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
