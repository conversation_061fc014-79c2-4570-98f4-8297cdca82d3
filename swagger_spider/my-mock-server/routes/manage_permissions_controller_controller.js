
// manage-permissions-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /manage/permissions/{id}
   * 操作ID: getManagePermissions
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/manage/permissions/:id', (req, res) => {
    console.log(`[GET] /manage/permissions/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseManagePermissionsResponse } = require('../models/responsemanagepermissionsresponse');
    res.json(generateResponseManagePermissionsResponse());
  });

  /**
   * 
   * 原始路径: /manage/permissions/{id}
   * 操作ID: updateManagePermissions
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (ManagePermissionsRequest)} body参数 - 必填
   */
  app.put('/manage/permissions/:id', (req, res) => {
    console.log(`[PUT] /manage/permissions/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /manage/permissions/{id}
   * 操作ID: deleteManagePermissions
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/manage/permissions/:id', (req, res) => {
    console.log(`[DELETE] /manage/permissions/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /manage/permissions
   * 操作ID: addManagePermissions
 * @param {对象 (ManagePermissionsRequest)} body参数 - 必填
   */
  app.post('/manage/permissions', (req, res) => {
    console.log(`[POST] /manage/permissions 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /manage/permissions/list
   * 操作ID: listManagePermissions
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
 * @param {对象 (ManagePermissionsQueryRequest)} body参数 - 可选
   */
  app.post('/manage/permissions/list', (req, res) => {
    console.log(`[POST] /manage/permissions/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListManagePermissionsSimpleResponse } = require('../models/responsedatalistmanagepermissionssimpleresponse');
    res.json(generateResponseDataListManagePermissionsSimpleResponse());
  });

  /**
   * 
   * 原始路径: /manage/permissions/batch
   * 操作ID: batchDeleteManagePermissions
 * @param {array} query参数 permissionIds - 必填
   */
  app.delete('/manage/permissions/batch', (req, res) => {
    console.log(`[DELETE] /manage/permissions/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });
};
