
// user-bind-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /user/bind/{id}
   * 操作ID: getUserBind
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/user/bind/:id', (req, res) => {
    console.log(`[GET] /user/bind/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseUserBindResponse } = require('../models/responseuserbindresponse');
    res.json(generateResponseUserBindResponse());
  });

  /**
   * 
   * 原始路径: /user/bind/{id}
   * 操作ID: updateUserBind
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (UserBindRequest)} body参数 - 必填
   */
  app.put('/user/bind/:id', (req, res) => {
    console.log(`[PUT] /user/bind/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /user/bind/{id}
   * 操作ID: deleteUserBind
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/user/bind/:id', (req, res) => {
    console.log(`[DELETE] /user/bind/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /user/bind
   * 操作ID: addUserBind
 * @param {对象 (UserBindRequest)} body参数 - 必填
   */
  app.post('/user/bind', (req, res) => {
    console.log(`[POST] /user/bind 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /user/bind/list
   * 操作ID: listUserBind
 * @param {integer (int32)} query参数 userId - 可选
 * @param {string (byte)} query参数 platform - 可选
 * @param {string} query参数 platformId - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/user/bind/list', (req, res) => {
    console.log(`[GET] /user/bind/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListUserBindResponse } = require('../models/responsedatalistuserbindresponse');
    res.json(generateResponseDataListUserBindResponse());
  });

  /**
   * 
   * 原始路径: /user/bind/batch
   * 操作ID: batchDeleteUserBinds
 * @param {array} query参数 ids - 必填
   */
  app.delete('/user/bind/batch', (req, res) => {
    console.log(`[DELETE] /user/bind/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
