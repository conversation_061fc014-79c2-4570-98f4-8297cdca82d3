
// schedule-handover-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /schedule/handover/{id}
   * 操作ID: getScheduleHandover
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/schedule/handover/:id', (req, res) => {
    console.log(`[GET] /schedule/handover/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseScheduleHandoverResponse } = require('../models/responseschedulehandoverresponse');
    res.json(generateResponseScheduleHandoverResponse());
  });

  /**
   * 
   * 原始路径: /schedule/handover/{id}
   * 操作ID: updateScheduleHandover
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (ScheduleHandoverRequest)} body参数 - 必填
   */
  app.put('/schedule/handover/:id', (req, res) => {
    console.log(`[PUT] /schedule/handover/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /schedule/handover/{id}
   * 操作ID: deleteScheduleHandover
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/schedule/handover/:id', (req, res) => {
    console.log(`[DELETE] /schedule/handover/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /schedule/handover/batch/status
   * 操作ID: batchUpdateScheduleHandoverStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/schedule/handover/batch/status', (req, res) => {
    console.log(`[PUT] /schedule/handover/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /schedule/handover
   * 操作ID: addScheduleHandover
 * @param {对象 (ScheduleHandoverRequest)} body参数 - 必填
   */
  app.post('/schedule/handover', (req, res) => {
    console.log(`[POST] /schedule/handover 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /schedule/handover/list
   * 操作ID: listScheduleHandover
 * @param {string} query参数 status - 可选
 * @param {integer (int32)} query参数 workSort - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.post('/schedule/handover/list', (req, res) => {
    console.log(`[POST] /schedule/handover/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListScheduleHandoverResponse } = require('../models/responsedatalistschedulehandoverresponse');
    res.json(generateResponseDataListScheduleHandoverResponse());
  });

  /**
   * 
   * 原始路径: /schedule/handover/batch
   * 操作ID: batchDeleteScheduleHandover
 * @param {array} query参数 scheduleHandoverIds - 必填
   */
  app.delete('/schedule/handover/batch', (req, res) => {
    console.log(`[DELETE] /schedule/handover/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });
};
