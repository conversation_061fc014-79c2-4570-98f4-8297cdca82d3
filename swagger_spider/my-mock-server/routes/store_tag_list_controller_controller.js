
// store-tag-list-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /store/tags/{id}
   * 操作ID: getStoreTagList
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/store/tags/:id', (req, res) => {
    console.log(`[GET] /store/tags/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseStoreTagListResponse } = require('../models/responsestoretaglistresponse');
    res.json(generateResponseStoreTagListResponse());
  });

  /**
   * 
   * 原始路径: /store/tags/{id}
   * 操作ID: updateStoreTagList
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (StoreTagListRequest)} body参数 - 必填
   */
  app.put('/store/tags/:id', (req, res) => {
    console.log(`[PUT] /store/tags/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /store/tags/{id}
   * 操作ID: deleteStoreTagList
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/store/tags/:id', (req, res) => {
    console.log(`[DELETE] /store/tags/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /store/tags/batch/tag
   * 操作ID: batchUpdateStoreTagListTag
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 tagId - 必填
   */
  app.put('/store/tags/batch/tag', (req, res) => {
    console.log(`[PUT] /store/tags/batch/tag 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /store/tags/batch/store
   * 操作ID: batchUpdateStoreTagListStore
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 storeId - 必填
   */
  app.put('/store/tags/batch/store', (req, res) => {
    console.log(`[PUT] /store/tags/batch/store 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /store/tags
   * 操作ID: addStoreTagList
 * @param {对象 (StoreTagListRequest)} body参数 - 必填
   */
  app.post('/store/tags', (req, res) => {
    console.log(`[POST] /store/tags 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /store/tags/list
   * 操作ID: listStoreTagList
 * @param {integer (int32)} query参数 storeId - 可选
 * @param {integer (int32)} query参数 tagId - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/store/tags/list', (req, res) => {
    console.log(`[GET] /store/tags/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListStoreTagListResponse } = require('../models/responsedataliststoretaglistresponse');
    res.json(generateResponseDataListStoreTagListResponse());
  });

  /**
   * 
   * 原始路径: /store/tags/batch
   * 操作ID: batchDeleteStoreTagList
 * @param {array} query参数 ids - 必填
   */
  app.delete('/store/tags/batch', (req, res) => {
    console.log(`[DELETE] /store/tags/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
