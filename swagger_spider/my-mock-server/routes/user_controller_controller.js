
// user-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /user/{id}
   * 操作ID: getUser
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/user/:id', (req, res) => {
    console.log(`[GET] /user/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseUserResponse } = require('../models/responseuserresponse');
    res.json(generateResponseUserResponse());
  });

  /**
   * 
   * 原始路径: /user/{id}
   * 操作ID: updateUser
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (UserRequest)} body参数 - 必填
   */
  app.put('/user/:id', (req, res) => {
    console.log(`[PUT] /user/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /user/{id}
   * 操作ID: deleteUser
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/user/:id', (req, res) => {
    console.log(`[DELETE] /user/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /user/batch/verified
   * 操作ID: batchUpdateUserVerified
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 verified - 必填
   */
  app.put('/user/batch/verified', (req, res) => {
    console.log(`[PUT] /user/batch/verified 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /user/batch/status
   * 操作ID: batchUpdateUserStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/user/batch/status', (req, res) => {
    console.log(`[PUT] /user/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /user/batch/level
   * 操作ID: batchUpdateUserLevel
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 level - 必填
   */
  app.put('/user/batch/level', (req, res) => {
    console.log(`[PUT] /user/batch/level 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /user
   * 操作ID: addUser
 * @param {对象 (UserRequest)} body参数 - 必填
   */
  app.post('/user', (req, res) => {
    console.log(`[POST] /user 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /user/list
   * 操作ID: listUser
 * @param {string} query参数 status - 可选
 * @param {string} query参数 verified - 可选
 * @param {string (byte)} query参数 level - 可选
 * @param {string} query参数 username - 可选
 * @param {string} query参数 email - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/user/list', (req, res) => {
    console.log(`[GET] /user/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListUserSimpleResponse } = require('../models/responsedatalistusersimpleresponse');
    res.json(generateResponseDataListUserSimpleResponse());
  });

  /**
   * 
   * 原始路径: /user/batch
   * 操作ID: batchDeleteUsers
 * @param {array} query参数 ids - 必填
   */
  app.delete('/user/batch', (req, res) => {
    console.log(`[DELETE] /user/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
