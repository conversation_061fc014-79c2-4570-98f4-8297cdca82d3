
// payment-record-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /payment/record/{id}
   * 操作ID: getPaymentRecord
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/payment/record/:id', (req, res) => {
    console.log(`[GET] /payment/record/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponsePaymentRecordResponse } = require('../models/responsepaymentrecordresponse');
    res.json(generateResponsePaymentRecordResponse());
  });

  /**
   * 
   * 原始路径: /payment/record/{id}
   * 操作ID: updatePaymentRecord
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (PaymentRecordRequest)} body参数 - 必填
   */
  app.put('/payment/record/:id', (req, res) => {
    console.log(`[PUT] /payment/record/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /payment/record/{id}
   * 操作ID: deletePaymentRecord
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/payment/record/:id', (req, res) => {
    console.log(`[DELETE] /payment/record/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /payment/record/batch/status
   * 操作ID: batchUpdatePaymentRecordStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/payment/record/batch/status', (req, res) => {
    console.log(`[PUT] /payment/record/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /payment/record
   * 操作ID: addPaymentRecord
 * @param {对象 (PaymentRecordRequest)} body参数 - 必填
   */
  app.post('/payment/record', (req, res) => {
    console.log(`[POST] /payment/record 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /payment/record/list
   * 操作ID: listPaymentRecord
 * @param {integer (int32)} query参数 userId - 可选
 * @param {integer (int32)} query参数 paymentId - 可选
 * @param {string} query参数 status - 可选
 * @param {string} query参数 transactionId - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/payment/record/list', (req, res) => {
    console.log(`[GET] /payment/record/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListPaymentRecordSimpleResponse } = require('../models/responsedatalistpaymentrecordsimpleresponse');
    res.json(generateResponseDataListPaymentRecordSimpleResponse());
  });

  /**
   * 
   * 原始路径: /payment/record/batch
   * 操作ID: batchDeletePaymentRecord
 * @param {array} query参数 ids - 必填
   */
  app.delete('/payment/record/batch', (req, res) => {
    console.log(`[DELETE] /payment/record/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
