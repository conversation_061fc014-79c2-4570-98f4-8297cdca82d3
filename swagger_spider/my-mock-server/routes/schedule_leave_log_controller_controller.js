
// schedule-leave-log-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /schedule/leave/log/{id}
   * 操作ID: getScheduleLeaveLogById
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/schedule/leave/log/:id', (req, res) => {
    console.log(`[GET] /schedule/leave/log/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseScheduleLeaveLogResponse } = require('../models/responsescheduleleavelogresponse');
    res.json(generateResponseScheduleLeaveLogResponse());
  });

  /**
   * 
   * 原始路径: /schedule/leave/log/{id}
   * 操作ID: updateScheduleLeaveLog
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (ScheduleLeaveLogRequest)} body参数 - 必填
   */
  app.put('/schedule/leave/log/:id', (req, res) => {
    console.log(`[PUT] /schedule/leave/log/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseBoolean } = require('../models/responseboolean');
    res.json(generateResponseBoolean());
  });

  /**
   * 
   * 原始路径: /schedule/leave/log/{id}
   * 操作ID: deleteScheduleLeaveLog
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/schedule/leave/log/:id', (req, res) => {
    console.log(`[DELETE] /schedule/leave/log/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseBoolean } = require('../models/responseboolean');
    res.json(generateResponseBoolean());
  });

  /**
   * 
   * 原始路径: /schedule/leave/log/batch/status
   * 操作ID: batchUpdateScheduleLeaveLogStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/schedule/leave/log/batch/status', (req, res) => {
    console.log(`[PUT] /schedule/leave/log/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseBoolean } = require('../models/responseboolean');
    res.json(generateResponseBoolean());
  });

  /**
   * 
   * 原始路径: /schedule/leave/log/batch/schedule
   * 操作ID: batchUpdateScheduleLeaveLogSchedule
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 scheduleId - 必填
   */
  app.put('/schedule/leave/log/batch/schedule', (req, res) => {
    console.log(`[PUT] /schedule/leave/log/batch/schedule 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseBoolean } = require('../models/responseboolean');
    res.json(generateResponseBoolean());
  });

  /**
   * 
   * 原始路径: /schedule/leave/log/batch/manage
   * 操作ID: batchUpdateScheduleLeaveLogManage
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 manageId - 必填
   */
  app.put('/schedule/leave/log/batch/manage', (req, res) => {
    console.log(`[PUT] /schedule/leave/log/batch/manage 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseBoolean } = require('../models/responseboolean');
    res.json(generateResponseBoolean());
  });

  /**
   * 
   * 原始路径: /schedule/leave/log/approve/{id}
   * 操作ID: approveScheduleLeaveLog
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (ScheduleLeaveLogApproveRequest)} body参数 - 必填
   */
  app.put('/schedule/leave/log/approve/:id', (req, res) => {
    console.log(`[PUT] /schedule/leave/log/approve/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseBoolean } = require('../models/responseboolean');
    res.json(generateResponseBoolean());
  });

  /**
   * 
   * 原始路径: /schedule/leave/log/apply
   * 操作ID: applyScheduleLeaveLog
 * @param {对象 (ScheduleLeaveLogApplyRequest)} body参数 - 必填
   */
  app.put('/schedule/leave/log/apply', (req, res) => {
    console.log(`[PUT] /schedule/leave/log/apply 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /schedule/leave/log
   * 操作ID: addScheduleLaveLog
 * @param {对象 (ScheduleLeaveLogRequest)} body参数 - 必填
   */
  app.post('/schedule/leave/log', (req, res) => {
    console.log(`[POST] /schedule/leave/log 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseBoolean } = require('../models/responseboolean');
    res.json(generateResponseBoolean());
  });

  /**
   * 
   * 原始路径: /schedule/leave/log/list
   * 操作ID: getScheduleLeaveLogList
 * @param {integer (int32)} query参数 scheduleId - 可选
 * @param {integer (int32)} query参数 manageId - 可选
 * @param {string} query参数 status - 可选
 * @param {string} query参数 leaveType - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/schedule/leave/log/list', (req, res) => {
    console.log(`[GET] /schedule/leave/log/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListScheduleLeaveLogResponse } = require('../models/responsedatalistscheduleleavelogresponse');
    res.json(generateResponseDataListScheduleLeaveLogResponse());
  });

  /**
   * 
   * 原始路径: /schedule/leave/log/batch
   * 操作ID: batchDeleteScheduleLeaveLogs
 * @param {array} query参数 ids - 必填
   */
  app.delete('/schedule/leave/log/batch', (req, res) => {
    console.log(`[DELETE] /schedule/leave/log/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseBoolean } = require('../models/responseboolean');
    res.json(generateResponseBoolean());
  });
};
