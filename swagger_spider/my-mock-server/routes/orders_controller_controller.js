
// orders-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /orders/{id}
   * 操作ID: getOrder
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/orders/:id', (req, res) => {
    console.log(`[GET] /orders/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseOrdersResponse } = require('../models/responseordersresponse');
    res.json(generateResponseOrdersResponse());
  });

  /**
   * 
   * 原始路径: /orders/{id}
   * 操作ID: updateOrder
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (OrdersRequest)} body参数 - 必填
   */
  app.put('/orders/:id', (req, res) => {
    console.log(`[PUT] /orders/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /orders/{id}
   * 操作ID: deleteOrder
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/orders/:id', (req, res) => {
    console.log(`[DELETE] /orders/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /orders/batch/type
   * 操作ID: batchUpdateOrderType
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 ordersType - 必填
   */
  app.put('/orders/batch/type', (req, res) => {
    console.log(`[PUT] /orders/batch/type 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /orders/batch/status
   * 操作ID: batchUpdateOrderStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/orders/batch/status', (req, res) => {
    console.log(`[PUT] /orders/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /orders
   * 操作ID: addOrder
 * @param {对象 (OrdersRequest)} body参数 - 必填
   */
  app.post('/orders', (req, res) => {
    console.log(`[POST] /orders 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /orders/{ordersId}/factor
   * 操作ID: getOrdersServicesFieldsList
 * @param {integer (int64)} path参数 ordersId - 必填
   */
  app.get('/orders/:ordersId/factor', (req, res) => {
    console.log(`[GET] /orders/{ordersId}/factor 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseListFieldResponse } = require('../models/responselistfieldresponse');
    res.json(generateResponseListFieldResponse());
  });

  /**
   * 
   * 原始路径: /orders/list
   * 操作ID: listOrders
 * @param {integer (int32)} query参数 userId - 可选
 * @param {integer (int64)} query参数 itemId - 可选
 * @param {integer (int32)} query参数 storeId - 可选
 * @param {string} query参数 ordersType - 可选
 * @param {string} query参数 status - 可选
 * @param {string} query参数 coupon - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/orders/list', (req, res) => {
    console.log(`[GET] /orders/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListOrdersSimpleResponse } = require('../models/responsedatalistorderssimpleresponse');
    res.json(generateResponseDataListOrdersSimpleResponse());
  });

  /**
   * 
   * 原始路径: /orders/batch
   * 操作ID: batchDeleteOrders
 * @param {array} query参数 ids - 必填
   */
  app.delete('/orders/batch', (req, res) => {
    console.log(`[DELETE] /orders/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
