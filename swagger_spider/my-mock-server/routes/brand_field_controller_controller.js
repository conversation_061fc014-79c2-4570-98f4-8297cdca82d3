
// brand-field-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /brand/field/{id}
   * 操作ID: getBrandField
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/brand/field/:id', (req, res) => {
    console.log(`[GET] /brand/field/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseBrandFieldResponse } = require('../models/responsebrandfieldresponse');
    res.json(generateResponseBrandFieldResponse());
  });

  /**
   * 
   * 原始路径: /brand/field/{id}
   * 操作ID: updateBrandField
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (BrandFieldRequest)} body参数 - 必填
   */
  app.put('/brand/field/:id', (req, res) => {
    console.log(`[PUT] /brand/field/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /brand/field/{id}
   * 操作ID: deleteBrandField
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/brand/field/:id', (req, res) => {
    console.log(`[DELETE] /brand/field/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /brand/field/batch/required
   * 操作ID: batchUpdateBrandFieldRequired
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 required - 必填
   */
  app.put('/brand/field/batch/required', (req, res) => {
    console.log(`[PUT] /brand/field/batch/required 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /brand/field
   * 操作ID: addBrandField
 * @param {对象 (BrandFieldRequest)} body参数 - 必填
   */
  app.post('/brand/field', (req, res) => {
    console.log(`[POST] /brand/field 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /brand/field/{brandId}/names
   * 操作ID: getSimpleBrandFieldList
 * @param {integer (int32)} path参数 brandId - 必填
   */
  app.get('/brand/field/:brandId/names', (req, res) => {
    console.log(`[GET] /brand/field/{brandId}/names 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseListNameResponse } = require('../models/responselistnameresponse');
    res.json(generateResponseListNameResponse());
  });

  /**
   * 
   * 原始路径: /brand/field/list
   * 操作ID: getBrandFieldList
 * @param {integer (int32)} query参数 brandId - 可选
 * @param {string} query参数 filedNameOrLabel - 可选
 * @param {string} query参数 required - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/brand/field/list', (req, res) => {
    console.log(`[GET] /brand/field/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListBrandFieldSimpleResponse } = require('../models/responsedatalistbrandfieldsimpleresponse');
    res.json(generateResponseDataListBrandFieldSimpleResponse());
  });

  /**
   * 
   * 原始路径: /brand/field/batch
   * 操作ID: batchDeleteBrandField
 * @param {array} query参数 ids - 必填
   */
  app.delete('/brand/field/batch', (req, res) => {
    console.log(`[DELETE] /brand/field/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });
};
