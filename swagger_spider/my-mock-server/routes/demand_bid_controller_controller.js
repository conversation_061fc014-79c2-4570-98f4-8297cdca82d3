
// demand-bid-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /demand/bid/{id}
   * 操作ID: getDemandBid
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/demand/bid/:id', (req, res) => {
    console.log(`[GET] /demand/bid/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDemandBidResponse } = require('../models/responsedemandbidresponse');
    res.json(generateResponseDemandBidResponse());
  });

  /**
   * 
   * 原始路径: /demand/bid/{id}
   * 操作ID: updateDemandBid
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (DemandBidRequest)} body参数 - 必填
   */
  app.put('/demand/bid/:id', (req, res) => {
    console.log(`[PUT] /demand/bid/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /demand/bid/{id}
   * 操作ID: deleteDemandBid
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/demand/bid/:id', (req, res) => {
    console.log(`[DELETE] /demand/bid/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /demand/bid/batch/userId
   * 操作ID: batchUpdateDemandBidUserId
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 userId - 必填
   */
  app.put('/demand/bid/batch/userId', (req, res) => {
    console.log(`[PUT] /demand/bid/batch/userId 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /demand/bid/batch/storeId
   * 操作ID: batchUpdateDemandBidStoreId
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 storeId - 必填
   */
  app.put('/demand/bid/batch/storeId', (req, res) => {
    console.log(`[PUT] /demand/bid/batch/storeId 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /demand/bid/batch/status
   * 操作ID: batchUpdateDemandBidStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/demand/bid/batch/status', (req, res) => {
    console.log(`[PUT] /demand/bid/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /demand/bid
   * 操作ID: addDemandBid
 * @param {对象 (DemandBidRequest)} body参数 - 必填
   */
  app.post('/demand/bid', (req, res) => {
    console.log(`[POST] /demand/bid 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /demand/bid/list
   * 操作ID: listDemandBid
 * @param {string} query参数 status - 可选
 * @param {integer (int32)} query参数 userId - 可选
 * @param {integer (int32)} query参数 storeId - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/demand/bid/list', (req, res) => {
    console.log(`[GET] /demand/bid/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListDemandBidSimpleResponse } = require('../models/responsedatalistdemandbidsimpleresponse');
    res.json(generateResponseDataListDemandBidSimpleResponse());
  });

  /**
   * 
   * 原始路径: /demand/bid/batch
   * 操作ID: batchDeleteDemandBids
 * @param {array} query参数 ids - 必填
   */
  app.delete('/demand/bid/batch', (req, res) => {
    console.log(`[DELETE] /demand/bid/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
