
// campaign-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /campaign/{id}
   * 操作ID: getCampaign
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/campaign/:id', (req, res) => {
    console.log(`[GET] /campaign/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseCampaignResponse } = require('../models/responsecampaignresponse');
    res.json(generateResponseCampaignResponse());
  });

  /**
   * 
   * 原始路径: /campaign/{id}
   * 操作ID: updateCampaign
 * @param {integer (int64)} path参数 id - 必填
 * @param {integer (int64)} query参数 langId - 必填
 * @param {对象 (CampaignRequest)} body参数 - 必填
   */
  app.put('/campaign/:id', (req, res) => {
    console.log(`[PUT] /campaign/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /campaign/{id}
   * 操作ID: deleteCampaign
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/campaign/:id', (req, res) => {
    console.log(`[DELETE] /campaign/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /campaign/batch/tags
   * 操作ID: batchUpdateTags
 * @param {array} query参数 ids - 必填
 * @param {array} query参数 tagIds - 必填
   */
  app.put('/campaign/batch/tags', (req, res) => {
    console.log(`[PUT] /campaign/batch/tags 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /campaign/batch/status
   * 操作ID: batchUpdateCampaignStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/campaign/batch/status', (req, res) => {
    console.log(`[PUT] /campaign/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /campaign
   * 操作ID: addCampaign
 * @param {对象 (CampaignRequest)} body参数 - 必填
   */
  app.post('/campaign', (req, res) => {
    console.log(`[POST] /campaign 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /campaign/list
   * 操作ID: getCampaignList
 * @param {string} query参数 language - 可选
 * @param {string} query参数 customUrl - 可选
 * @param {string} query参数 title - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/campaign/list', (req, res) => {
    console.log(`[GET] /campaign/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListCampaignSimpleResponse } = require('../models/responsedatalistcampaignsimpleresponse');
    res.json(generateResponseDataListCampaignSimpleResponse());
  });

  /**
   * 
   * 原始路径: /campaign/batch
   * 操作ID: batchDeleteCampaign
 * @param {array} query参数 ids - 必填
   */
  app.delete('/campaign/batch', (req, res) => {
    console.log(`[DELETE] /campaign/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });
};
