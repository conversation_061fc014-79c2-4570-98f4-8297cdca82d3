
// role-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /role/{id}
   * 操作ID: getRole
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/role/:id', (req, res) => {
    console.log(`[GET] /role/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseRoleResponse } = require('../models/responseroleresponse');
    res.json(generateResponseRoleResponse());
  });

  /**
   * 
   * 原始路径: /role/{id}
   * 操作ID: updateRole
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (RoleRequest)} body参数 - 必填
   */
  app.put('/role/:id', (req, res) => {
    console.log(`[PUT] /role/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /role/{id}
   * 操作ID: deleteRole
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/role/:id', (req, res) => {
    console.log(`[DELETE] /role/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /role
   * 操作ID: addRole
 * @param {对象 (RoleRequest)} body参数 - 必填
   */
  app.post('/role', (req, res) => {
    console.log(`[POST] /role 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /role/list
   * 操作ID: listRole
 * @param {string} query参数 roleName - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/role/list', (req, res) => {
    console.log(`[GET] /role/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListRoleSimpleResponse } = require('../models/responsedatalistrolesimpleresponse');
    res.json(generateResponseDataListRoleSimpleResponse());
  });

  /**
   * 
   * 原始路径: /role/batch
   * 操作ID: batchDeleteRole
 * @param {array} query参数 roleIds - 必填
   */
  app.delete('/role/batch', (req, res) => {
    console.log(`[DELETE] /role/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });
};
