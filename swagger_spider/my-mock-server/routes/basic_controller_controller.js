
// basic-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /basic/constant
   * 操作ID: constant

   */
  app.get('/basic/constant', (req, res) => {
    console.log(`[GET] /basic/constant 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseMapStringListNameResponse } = require('../models/responsemapstringlistnameresponse');
    res.json(generateResponseMapStringListNameResponse());
  });
};
