
// rules-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /rules/{rulesId}
   * 操作ID: updateRules
 * @param {integer (int32)} path参数 rulesId - 必填
 * @param {integer (int32)} query参数 rulesLangId - 必填
 * @param {integer (int32)} query参数 rulesSanctionsId - 必填
 * @param {对象 (RulesRequest)} body参数 - 必填
   */
  app.put('/rules/:rulesId', (req, res) => {
    console.log(`[PUT] /rules/{rulesId} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /rules/batch/status
   * 操作ID: batchUpdateRulesStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/rules/batch/status', (req, res) => {
    console.log(`[PUT] /rules/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /rules
   * 操作ID: addRules
 * @param {对象 (RulesRequest)} body参数 - 必填
   */
  app.post('/rules', (req, res) => {
    console.log(`[POST] /rules 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /rules/{id}
   * 操作ID: getRules
 * @param {integer (int32)} path参数 id - 必填
 * @param {string} query参数 language - 必填
   */
  app.get('/rules/:id', (req, res) => {
    console.log(`[GET] /rules/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseRulesResponse } = require('../models/responserulesresponse');
    res.json(generateResponseRulesResponse());
  });

  /**
   * 
   * 原始路径: /rules/{id}
   * 操作ID: deleteRules
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/rules/:id', (req, res) => {
    console.log(`[DELETE] /rules/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /rules/names
   * 操作ID: getAllRulesList

   */
  app.get('/rules/names', (req, res) => {
    console.log(`[GET] /rules/names 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseListNameResponse } = require('../models/responselistnameresponse');
    res.json(generateResponseListNameResponse());
  });

  /**
   * 
   * 原始路径: /rules/list
   * 操作ID: getRulesList
 * @param {string} query参数 indexNumber - 可选
 * @param {string} query参数 language - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/rules/list', (req, res) => {
    console.log(`[GET] /rules/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListRulesSimpleResponse } = require('../models/responsedatalistrulessimpleresponse');
    res.json(generateResponseDataListRulesSimpleResponse());
  });

  /**
   * 
   * 原始路径: /rules/batch
   * 操作ID: batchDeleteRules
 * @param {array} query参数 ids - 必填
   */
  app.delete('/rules/batch', (req, res) => {
    console.log(`[DELETE] /rules/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });
};
