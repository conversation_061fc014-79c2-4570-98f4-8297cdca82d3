
// demand-attr-list-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /demand/attrs/{id}
   * 操作ID: getDemandAttrList
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/demand/attrs/:id', (req, res) => {
    console.log(`[GET] /demand/attrs/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDemandAttrListResponse } = require('../models/responsedemandattrlistresponse');
    res.json(generateResponseDemandAttrListResponse());
  });

  /**
   * 
   * 原始路径: /demand/attrs/{id}
   * 操作ID: updateDemandAttrList
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (DemandAttrListRequest)} body参数 - 必填
   */
  app.put('/demand/attrs/:id', (req, res) => {
    console.log(`[PUT] /demand/attrs/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /demand/attrs/{id}
   * 操作ID: deleteDemandAttrList
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/demand/attrs/:id', (req, res) => {
    console.log(`[DELETE] /demand/attrs/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /demand/attrs
   * 操作ID: addDemandAttrList
 * @param {对象 (DemandAttrListRequest)} body参数 - 必填
   */
  app.post('/demand/attrs', (req, res) => {
    console.log(`[POST] /demand/attrs 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /demand/attrs/list
   * 操作ID: listDemandAttrList
 * @param {integer (int64)} query参数 demandId - 可选
 * @param {integer (int32)} query参数 attrId - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/demand/attrs/list', (req, res) => {
    console.log(`[GET] /demand/attrs/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListDemandAttrListResponse } = require('../models/responsedatalistdemandattrlistresponse');
    res.json(generateResponseDataListDemandAttrListResponse());
  });

  /**
   * 
   * 原始路径: /demand/attrs/batch
   * 操作ID: batchDeleteDemandAttrLists
 * @param {array} query参数 ids - 必填
   */
  app.delete('/demand/attrs/batch', (req, res) => {
    console.log(`[DELETE] /demand/attrs/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
