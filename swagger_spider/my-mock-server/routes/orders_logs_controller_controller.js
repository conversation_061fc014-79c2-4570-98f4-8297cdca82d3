
// orders-logs-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /orders/logs/{id}
   * 操作ID: getOrderLog
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/orders/logs/:id', (req, res) => {
    console.log(`[GET] /orders/logs/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseOrdersLogsResponse } = require('../models/responseorderslogsresponse');
    res.json(generateResponseOrdersLogsResponse());
  });

  /**
   * 
   * 原始路径: /orders/logs/{id}
   * 操作ID: updateOrderLog
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (OrdersLogsRequest)} body参数 - 必填
   */
  app.put('/orders/logs/:id', (req, res) => {
    console.log(`[PUT] /orders/logs/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /orders/logs/{id}
   * 操作ID: deleteOrderLog
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/orders/logs/:id', (req, res) => {
    console.log(`[DELETE] /orders/logs/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /orders/logs/batch/source
   * 操作ID: batchUpdateOrderLogSource
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 sourceType - 必填
   */
  app.put('/orders/logs/batch/source', (req, res) => {
    console.log(`[PUT] /orders/logs/batch/source 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /orders/logs
   * 操作ID: addOrderLog
 * @param {对象 (OrdersLogsRequest)} body参数 - 必填
   */
  app.post('/orders/logs', (req, res) => {
    console.log(`[POST] /orders/logs 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /orders/logs/list
   * 操作ID: listOrdersLogs
 * @param {integer (int64)} query参数 ordersId - 可选
 * @param {integer (int32)} query参数 storeId - 可选
 * @param {string} query参数 logType - 可选
 * @param {string} query参数 sourceType - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.post('/orders/logs/list', (req, res) => {
    console.log(`[POST] /orders/logs/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListOrdersLogsResponse } = require('../models/responsedatalistorderslogsresponse');
    res.json(generateResponseDataListOrdersLogsResponse());
  });

  /**
   * 
   * 原始路径: /orders/logs/batch
   * 操作ID: batchDeleteOrderLogs
 * @param {array} query参数 ids - 必填
   */
  app.delete('/orders/logs/batch', (req, res) => {
    console.log(`[DELETE] /orders/logs/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
