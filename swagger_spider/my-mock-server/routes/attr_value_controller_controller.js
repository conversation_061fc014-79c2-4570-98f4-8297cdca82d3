
// attr-value-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /attr/value/{id}
   * 操作ID: getAttrValue
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/attr/value/:id', (req, res) => {
    console.log(`[GET] /attr/value/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseAttrValueResponse } = require('../models/responseattrvalueresponse');
    res.json(generateResponseAttrValueResponse());
  });

  /**
   * 
   * 原始路径: /attr/value/{id}
   * 操作ID: updateAttrValue
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (AttrValueRequest)} body参数 - 必填
   */
  app.put('/attr/value/:id', (req, res) => {
    console.log(`[PUT] /attr/value/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /attr/value/{id}
   * 操作ID: deleteAttrValue
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/attr/value/:id', (req, res) => {
    console.log(`[DELETE] /attr/value/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /attr/value/batch/status
   * 操作ID: batchUpdateAttributeValueStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/attr/value/batch/status', (req, res) => {
    console.log(`[PUT] /attr/value/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /attr/value/batch/attr
   * 操作ID: batchUpdateAttrValueId
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 attrId - 必填
   */
  app.put('/attr/value/batch/attr', (req, res) => {
    console.log(`[PUT] /attr/value/batch/attr 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /attr/value
   * 操作ID: addAttrValue
 * @param {对象 (AttrValueRequest)} body参数 - 必填
   */
  app.post('/attr/value', (req, res) => {
    console.log(`[POST] /attr/value 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /attr/value/batch
   * 操作ID: batchAddAttrValue
 * @param {对象 (BatchAttrValueRequest)} body参数 - 必填
   */
  app.post('/attr/value/batch', (req, res) => {
    console.log(`[POST] /attr/value/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /attr/value/batch
   * 操作ID: batchDeleteAttributeValue
 * @param {array} query参数 ids - 必填
   */
  app.delete('/attr/value/batch', (req, res) => {
    console.log(`[DELETE] /attr/value/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /attr/value/list
   * 操作ID: getAttrValueList
 * @param {integer (int32)} query参数 attrId - 可选
 * @param {string} query参数 status - 可选
 * @param {string} query参数 language - 可选
 * @param {string} query参数 customUrl - 可选
 * @param {string} query参数 attrValue - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/attr/value/list', (req, res) => {
    console.log(`[GET] /attr/value/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListAttrValueSimpleResponse } = require('../models/responsedatalistattrvaluesimpleresponse');
    res.json(generateResponseDataListAttrValueSimpleResponse());
  });
};
