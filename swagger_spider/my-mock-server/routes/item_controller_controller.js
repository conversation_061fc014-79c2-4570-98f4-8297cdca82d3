
// item-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /item/{id}
   * 操作ID: getItem
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/item/:id', (req, res) => {
    console.log(`[GET] /item/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseItemResponse } = require('../models/responseitemresponse');
    res.json(generateResponseItemResponse());
  });

  /**
   * 
   * 原始路径: /item/{id}
   * 操作ID: updateItem
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (ItemRequest)} body参数 - 必填
   */
  app.put('/item/:id', (req, res) => {
    console.log(`[PUT] /item/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /item/{id}
   * 操作ID: deleteItem
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/item/:id', (req, res) => {
    console.log(`[DELETE] /item/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /item/batch/user
   * 操作ID: batchUpdateItemUser
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 userId - 必填
   */
  app.put('/item/batch/user', (req, res) => {
    console.log(`[PUT] /item/batch/user 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /item/batch/store
   * 操作ID: batchUpdateItemStore
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 storeId - 必填
   */
  app.put('/item/batch/store', (req, res) => {
    console.log(`[PUT] /item/batch/store 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /item/batch/status
   * 操作ID: batchUpdateItemStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/item/batch/status', (req, res) => {
    console.log(`[PUT] /item/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /item/batch/service
   * 操作ID: batchUpdateItemService
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 servicesId - 必填
   */
  app.put('/item/batch/service', (req, res) => {
    console.log(`[PUT] /item/batch/service 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /item/batch/brand
   * 操作ID: batchUpdateItemBrand
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 brandId - 必填
   */
  app.put('/item/batch/brand', (req, res) => {
    console.log(`[PUT] /item/batch/brand 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /item
   * 操作ID: addItem
 * @param {对象 (ItemRequest)} body参数 - 必填
   */
  app.post('/item', (req, res) => {
    console.log(`[POST] /item 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /item/list
   * 操作ID: listItem
 * @param {integer (int32)} query参数 storeId - 可选
 * @param {integer (int32)} query参数 userId - 可选
 * @param {integer (int32)} query参数 brandId - 可选
 * @param {integer (int32)} query参数 servicesId - 可选
 * @param {string} query参数 status - 可选
 * @param {string} query参数 language - 可选
 * @param {string} query参数 itemName - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/item/list', (req, res) => {
    console.log(`[GET] /item/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListItemSimpleResponse } = require('../models/responsedatalistitemsimpleresponse');
    res.json(generateResponseDataListItemSimpleResponse());
  });

  /**
   * 
   * 原始路径: /item/batch
   * 操作ID: batchDeleteItem
 * @param {array} query参数 ids - 必填
   */
  app.delete('/item/batch', (req, res) => {
    console.log(`[DELETE] /item/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
