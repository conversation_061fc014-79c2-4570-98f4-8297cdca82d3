
// system-config-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /system/config/{id}
   * 操作ID: getSystemConfig
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/system/config/:id', (req, res) => {
    console.log(`[GET] /system/config/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseSystemConfigResponse } = require('../models/responsesystemconfigresponse');
    res.json(generateResponseSystemConfigResponse());
  });

  /**
   * 
   * 原始路径: /system/config/{id}
   * 操作ID: updateSystemConfig
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (SystemConfigRequest)} body参数 - 必填
   */
  app.put('/system/config/:id', (req, res) => {
    console.log(`[PUT] /system/config/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /system/config/{id}
   * 操作ID: deleteSystemConfig
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/system/config/:id', (req, res) => {
    console.log(`[DELETE] /system/config/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /system/config
   * 操作ID: addSystemConfig
 * @param {对象 (SystemConfigRequest)} body参数 - 必填
   */
  app.post('/system/config', (req, res) => {
    console.log(`[POST] /system/config 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /system/config/list
   * 操作ID: listSystemConfigs
 * @param {string} query参数 configKey - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/system/config/list', (req, res) => {
    console.log(`[GET] /system/config/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListSystemConfigResponse } = require('../models/responsedatalistsystemconfigresponse');
    res.json(generateResponseDataListSystemConfigResponse());
  });

  /**
   * 
   * 原始路径: /system/config/key/{configKey}
   * 操作ID: getByConfigKeyUniqueKey
 * @param {string} path参数 configKey - 必填
   */
  app.get('/system/config/key/:configKey', (req, res) => {
    console.log(`[GET] /system/config/key/{configKey} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseSystemConfigResponse } = require('../models/responsesystemconfigresponse');
    res.json(generateResponseSystemConfigResponse());
  });

  /**
   * 
   * 原始路径: /system/config/batch
   * 操作ID: deleteSystemConfigMulti
 * @param {array} query参数 ids - 必填
   */
  app.delete('/system/config/batch', (req, res) => {
    console.log(`[DELETE] /system/config/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
