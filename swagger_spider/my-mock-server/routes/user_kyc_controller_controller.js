
// user-kyc-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /user/kyc/{id}
   * 操作ID: getUserKyc
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/user/kyc/:id', (req, res) => {
    console.log(`[GET] /user/kyc/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseUserKycResponse } = require('../models/responseuserkycresponse');
    res.json(generateResponseUserKycResponse());
  });

  /**
   * 
   * 原始路径: /user/kyc/{id}
   * 操作ID: updateUserKyc
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (UserKycRequest)} body参数 - 必填
   */
  app.put('/user/kyc/:id', (req, res) => {
    console.log(`[PUT] /user/kyc/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /user/kyc/{id}
   * 操作ID: deleteUserKyc
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/user/kyc/:id', (req, res) => {
    console.log(`[DELETE] /user/kyc/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /user/kyc/batch/type
   * 操作ID: batchUpdateUserKycType
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 kycType - 必填
   */
  app.put('/user/kyc/batch/type', (req, res) => {
    console.log(`[PUT] /user/kyc/batch/type 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /user/kyc/batch/status
   * 操作ID: batchUpdateUserKycStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/user/kyc/batch/status', (req, res) => {
    console.log(`[PUT] /user/kyc/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /user/kyc
   * 操作ID: addUserKyc
 * @param {对象 (UserKycRequest)} body参数 - 必填
   */
  app.post('/user/kyc', (req, res) => {
    console.log(`[POST] /user/kyc 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /user/kyc/list
   * 操作ID: listUserKyc
 * @param {string} query参数 status - 可选
 * @param {string} query参数 kycType - 可选
 * @param {integer (int32)} query参数 userId - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/user/kyc/list', (req, res) => {
    console.log(`[GET] /user/kyc/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListUserKycSimpleResponse } = require('../models/responsedatalistuserkycsimpleresponse');
    res.json(generateResponseDataListUserKycSimpleResponse());
  });

  /**
   * 
   * 原始路径: /user/kyc/batch
   * 操作ID: batchDeleteUserKyc
 * @param {array} query参数 ids - 必填
   */
  app.delete('/user/kyc/batch', (req, res) => {
    console.log(`[DELETE] /user/kyc/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
