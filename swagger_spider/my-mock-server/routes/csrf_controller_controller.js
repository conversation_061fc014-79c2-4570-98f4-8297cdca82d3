
// csrf-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /csrf
   * 操作ID: getCsrfToken
 * @param {对象 (CsrfToken)} query参数 csrfToken - 必填
   */
  app.get('/csrf', (req, res) => {
    console.log(`[GET] /csrf 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateCsrfToken } = require('../models/csrftoken');
    res.json(generateCsrfToken());
  });
};
