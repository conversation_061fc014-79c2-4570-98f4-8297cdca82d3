
// coupon-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /coupon/{id}
   * 操作ID: getCoupon
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/coupon/:id', (req, res) => {
    console.log(`[GET] /coupon/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseCouponResponse } = require('../models/responsecouponresponse');
    res.json(generateResponseCouponResponse());
  });

  /**
   * 
   * 原始路径: /coupon/{id}
   * 操作ID: updateCoupon
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (CouponRequest)} body参数 - 必填
   */
  app.put('/coupon/:id', (req, res) => {
    console.log(`[PUT] /coupon/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /coupon/{id}
   * 操作ID: deleteCoupon
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/coupon/:id', (req, res) => {
    console.log(`[DELETE] /coupon/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /coupon/batch/status
   * 操作ID: batchUpdateCouponStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 couponStatus - 必填
   */
  app.put('/coupon/batch/status', (req, res) => {
    console.log(`[PUT] /coupon/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /coupon
   * 操作ID: addCoupon
 * @param {对象 (CouponRequest)} body参数 - 必填
   */
  app.post('/coupon', (req, res) => {
    console.log(`[POST] /coupon 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /coupon/list
   * 操作ID: getCouponList
 * @param {string} query参数 actName - 可选
 * @param {string} query参数 couponStatus - 可选
 * @param {integer (int32)} query参数 brandId - 可选
 * @param {integer (int32)} query参数 storeId - 可选
 * @param {integer (int32)} query参数 servicesId - 可选
 * @param {integer (int64)} query参数 itemId - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/coupon/list', (req, res) => {
    console.log(`[GET] /coupon/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListCouponSimpleResponse } = require('../models/responsedatalistcouponsimpleresponse');
    res.json(generateResponseDataListCouponSimpleResponse());
  });

  /**
   * 
   * 原始路径: /coupon/batch
   * 操作ID: batchDeleteCoupon
 * @param {array} query参数 ids - 必填
   */
  app.delete('/coupon/batch', (req, res) => {
    console.log(`[DELETE] /coupon/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });
};
