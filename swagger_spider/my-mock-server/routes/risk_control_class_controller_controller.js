
// risk-control-class-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /risk/class/{id}
   * 操作ID: getRiskControlClass
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/risk/class/:id', (req, res) => {
    console.log(`[GET] /risk/class/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseRiskControlClassResponse } = require('../models/responseriskcontrolclassresponse');
    res.json(generateResponseRiskControlClassResponse());
  });

  /**
   * 
   * 原始路径: /risk/class/{id}
   * 操作ID: updateRiskControlClass
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (RiskControlClassRequest)} body参数 - 必填
   */
  app.put('/risk/class/:id', (req, res) => {
    console.log(`[PUT] /risk/class/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /risk/class/{id}
   * 操作ID: deleteRiskControlClass
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/risk/class/:id', (req, res) => {
    console.log(`[DELETE] /risk/class/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /risk/class/batch/status
   * 操作ID: batchUpdateRiskControlClassStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/risk/class/batch/status', (req, res) => {
    console.log(`[PUT] /risk/class/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /risk/class
   * 操作ID: addRiskControlClass
 * @param {对象 (RiskControlClassRequest)} body参数 - 必填
   */
  app.post('/risk/class', (req, res) => {
    console.log(`[POST] /risk/class 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /risk/class/list
   * 操作ID: listRiskControlClass
 * @param {string} query参数 status - 可选
 * @param {string} query参数 className - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/risk/class/list', (req, res) => {
    console.log(`[GET] /risk/class/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListRiskControlClassResponse } = require('../models/responsedatalistriskcontrolclassresponse');
    res.json(generateResponseDataListRiskControlClassResponse());
  });

  /**
   * 
   * 原始路径: /risk/class/batch
   * 操作ID: batchDeleteRiskControlClass
 * @param {array} query参数 ids - 必填
   */
  app.delete('/risk/class/batch', (req, res) => {
    console.log(`[DELETE] /risk/class/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });
};
