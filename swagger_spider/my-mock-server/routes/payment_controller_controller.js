
// payment-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /payment/{id}
   * 操作ID: getPayment
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/payment/:id', (req, res) => {
    console.log(`[GET] /payment/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponsePaymentResponse } = require('../models/responsepaymentresponse');
    res.json(generateResponsePaymentResponse());
  });

  /**
   * 
   * 原始路径: /payment/{id}
   * 操作ID: updatePayment
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (PaymentRequest)} body参数 - 必填
   */
  app.put('/payment/:id', (req, res) => {
    console.log(`[PUT] /payment/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /payment/{id}
   * 操作ID: deletePayment
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/payment/:id', (req, res) => {
    console.log(`[DELETE] /payment/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /payment/batch/status
   * 操作ID: batchUpdatePaymentStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/payment/batch/status', (req, res) => {
    console.log(`[PUT] /payment/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /payment/batch/paymentType
   * 操作ID: batchUpdatePaymentType
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 paymentType - 必填
   */
  app.put('/payment/batch/paymentType', (req, res) => {
    console.log(`[PUT] /payment/batch/paymentType 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /payment/batch/paymentName
   * 操作ID: batchUpdatePaymentName
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 paymentName - 必填
   */
  app.put('/payment/batch/paymentName', (req, res) => {
    console.log(`[PUT] /payment/batch/paymentName 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /payment
   * 操作ID: addPayment
 * @param {对象 (PaymentRequest)} body参数 - 必填
   */
  app.post('/payment', (req, res) => {
    console.log(`[POST] /payment 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /payment/list
   * 操作ID: listPayment
 * @param {string} query参数 status - 可选
 * @param {string} query参数 paymentType - 可选
 * @param {string} query参数 paymentName - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/payment/list', (req, res) => {
    console.log(`[GET] /payment/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListPaymentSimpleResponse } = require('../models/responsedatalistpaymentsimpleresponse');
    res.json(generateResponseDataListPaymentSimpleResponse());
  });

  /**
   * 
   * 原始路径: /payment/batch
   * 操作ID: batchDeletePayment
 * @param {array} query参数 ids - 必填
   */
  app.delete('/payment/batch', (req, res) => {
    console.log(`[DELETE] /payment/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
