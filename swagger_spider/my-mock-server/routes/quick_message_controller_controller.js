
// quick-message-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /quick/message/{id}
   * 操作ID: getQuickMessage
 * @param {integer (int32)} path参数 id - 必填
 * @param {string} query参数 languageCode - 可选
   */
  app.get('/quick/message/:id', (req, res) => {
    console.log(`[GET] /quick/message/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseQuickMessageResponse } = require('../models/responsequickmessageresponse');
    res.json(generateResponseQuickMessageResponse());
  });

  /**
   * 
   * 原始路径: /quick/message/{id}
   * 操作ID: updateQuickMessage
 * @param {integer (int32)} path参数 id - 必填
 * @param {integer (int32)} query参数 quickMessageLangId - 必填
 * @param {对象 (QuickMessageRequest)} body参数 - 必填
   */
  app.put('/quick/message/:id', (req, res) => {
    console.log(`[PUT] /quick/message/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /quick/message/{id}
   * 操作ID: deleteQuickMessage
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/quick/message/:id', (req, res) => {
    console.log(`[DELETE] /quick/message/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /quick/message
   * 操作ID: addQuickMessage
 * @param {对象 (QuickMessageRequest)} body参数 - 必填
   */
  app.post('/quick/message', (req, res) => {
    console.log(`[POST] /quick/message 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /quick/message/batch
   * 操作ID: batchAddQuickMessage
 * @param {对象 (BatchQuickMessageRequest)} body参数 - 必填
   */
  app.post('/quick/message/batch', (req, res) => {
    console.log(`[POST] /quick/message/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /quick/message/batch
   * 操作ID: batchDeleteQuickMessage
 * @param {array} query参数 ids - 必填
   */
  app.delete('/quick/message/batch', (req, res) => {
    console.log(`[DELETE] /quick/message/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /quick/message/list
   * 操作ID: listQuickMessage
 * @param {integer (int32)} query参数 quickMessageTypeId - 可选
 * @param {integer (int32)} query参数 manageId - 可选
 * @param {string} query参数 language - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/quick/message/list', (req, res) => {
    console.log(`[GET] /quick/message/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListQuickMessageSimpleResponse } = require('../models/responsedatalistquickmessagesimpleresponse');
    res.json(generateResponseDataListQuickMessageSimpleResponse());
  });
};
