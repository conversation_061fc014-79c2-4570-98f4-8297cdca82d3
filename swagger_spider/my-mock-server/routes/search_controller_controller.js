
// search-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /search/{id}
   * 操作ID: getSearch
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/search/:id', (req, res) => {
    console.log(`[GET] /search/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseSearchResponse } = require('../models/responsesearchresponse');
    res.json(generateResponseSearchResponse());
  });

  /**
   * 
   * 原始路径: /search/{id}
   * 操作ID: updateSearch
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (SearchRequest)} body参数 - 必填
   */
  app.put('/search/:id', (req, res) => {
    console.log(`[PUT] /search/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /search/{id}
   * 操作ID: deleteSearch
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/search/:id', (req, res) => {
    console.log(`[DELETE] /search/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /search/batch/status
   * 操作ID: batchUpdateSearchStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/search/batch/status', (req, res) => {
    console.log(`[PUT] /search/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /search/batch/brand
   * 操作ID: batchUpdateSearchBrand
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 brandId - 必填
   */
  app.put('/search/batch/brand', (req, res) => {
    console.log(`[PUT] /search/batch/brand 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /search
   * 操作ID: addSearch
 * @param {对象 (SearchRequest)} body参数 - 必填
   */
  app.post('/search', (req, res) => {
    console.log(`[POST] /search 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /search/list
   * 操作ID: getSearchList
 * @param {string} query参数 language - 可选
 * @param {integer (int32)} query参数 brandId - 可选
 * @param {string} query参数 keywords - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/search/list', (req, res) => {
    console.log(`[GET] /search/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListSearchResponse } = require('../models/responsedatalistsearchresponse');
    res.json(generateResponseDataListSearchResponse());
  });

  /**
   * 
   * 原始路径: /search/batch
   * 操作ID: batchDeleteSearches
 * @param {array} query参数 ids - 必填
   */
  app.delete('/search/batch', (req, res) => {
    console.log(`[DELETE] /search/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });
};
