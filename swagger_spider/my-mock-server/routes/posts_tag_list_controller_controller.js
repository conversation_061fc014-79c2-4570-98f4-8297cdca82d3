
// posts-tag-list-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /posts/tags/{id}
   * 操作ID: getPostsTagList
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/posts/tags/:id', (req, res) => {
    console.log(`[GET] /posts/tags/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponsePostsTagListResponse } = require('../models/responsepoststaglistresponse');
    res.json(generateResponsePostsTagListResponse());
  });

  /**
   * 
   * 原始路径: /posts/tags/{id}
   * 操作ID: updatePostsTagList
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (PostsTagListRequest)} body参数 - 必填
   */
  app.put('/posts/tags/:id', (req, res) => {
    console.log(`[PUT] /posts/tags/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /posts/tags/{id}
   * 操作ID: deletePostsTagList
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/posts/tags/:id', (req, res) => {
    console.log(`[DELETE] /posts/tags/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /posts/tags
   * 操作ID: addPostsTagList
 * @param {对象 (PostsTagListRequest)} body参数 - 必填
   */
  app.post('/posts/tags', (req, res) => {
    console.log(`[POST] /posts/tags 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /posts/tags/list
   * 操作ID: listPostsTagList
 * @param {integer (int64)} query参数 postsId - 可选
 * @param {integer (int32)} query参数 tagId - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.post('/posts/tags/list', (req, res) => {
    console.log(`[POST] /posts/tags/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListPostsTagListResponse } = require('../models/responsedatalistpoststaglistresponse');
    res.json(generateResponseDataListPostsTagListResponse());
  });

  /**
   * 
   * 原始路径: /posts/tags/batch
   * 操作ID: batchDeletePostsTagLists
 * @param {array} query参数 ids - 必填
   */
  app.delete('/posts/tags/batch', (req, res) => {
    console.log(`[DELETE] /posts/tags/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
