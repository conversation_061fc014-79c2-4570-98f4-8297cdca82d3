
// dashboard-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /dashboard/trend-statistics
   * 操作ID: getTrendStatistics
 * @param {integer (int32)} query参数 brandId - 可选
 * @param {integer (int32)} query参数 servicesId - 可选
 * @param {integer (int32)} query参数 startDate - 必填
 * @param {integer (int32)} query参数 endDate - 必填
   */
  app.get('/dashboard/trend-statistics', (req, res) => {
    console.log(`[GET] /dashboard/trend-statistics 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseTrendStatisticsResponse } = require('../models/responsetrendstatisticsresponse');
    res.json(generateResponseTrendStatisticsResponse());
  });

  /**
   * 
   * 原始路径: /dashboard/overview
   * 操作ID: getTodayDashboardOverview

   */
  app.get('/dashboard/overview', (req, res) => {
    console.log(`[GET] /dashboard/overview 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseWebsiteDashboardOverviewResponse } = require('../models/responsewebsitedashboardoverviewresponse');
    res.json(generateResponseWebsiteDashboardOverviewResponse());
  });

  /**
   * 
   * 原始路径: /dashboard/hot-data
   * 操作ID: getPlatformHotData
 * @param {integer (int32)} query参数 topNumber - 必填
 * @param {integer (int32)} query参数 startDate - 必填
 * @param {integer (int32)} query参数 endDate - 必填
   */
  app.get('/dashboard/hot-data', (req, res) => {
    console.log(`[GET] /dashboard/hot-data 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponsePlatformHotDataResponse } = require('../models/responseplatformhotdataresponse');
    res.json(generateResponsePlatformHotDataResponse());
  });
};
