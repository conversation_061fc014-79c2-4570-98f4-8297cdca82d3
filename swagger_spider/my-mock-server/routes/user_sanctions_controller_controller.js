
// user-sanctions-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /user/sanctions/{id}
   * 操作ID: getUserSanctions
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/user/sanctions/:id', (req, res) => {
    console.log(`[GET] /user/sanctions/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseUserSanctionsResponse } = require('../models/responseusersanctionsresponse');
    res.json(generateResponseUserSanctionsResponse());
  });

  /**
   * 
   * 原始路径: /user/sanctions/{id}
   * 操作ID: updateUserSanctions
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (UserSanctionsRequest)} body参数 - 必填
   */
  app.put('/user/sanctions/:id', (req, res) => {
    console.log(`[PUT] /user/sanctions/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /user/sanctions/{id}
   * 操作ID: deleteUserSanctions
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/user/sanctions/:id', (req, res) => {
    console.log(`[DELETE] /user/sanctions/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /user/sanctions/batch/status
   * 操作ID: batchUpdateUserSanctionsStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/user/sanctions/batch/status', (req, res) => {
    console.log(`[PUT] /user/sanctions/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /user/sanctions
   * 操作ID: addUserSanctions
 * @param {对象 (UserSanctionsRequest)} body参数 - 必填
   */
  app.post('/user/sanctions', (req, res) => {
    console.log(`[POST] /user/sanctions 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /user/sanctions/list
   * 操作ID: listUserSanctions
 * @param {integer (int32)} query参数 violationsEventId - 可选
 * @param {integer (int32)} query参数 userId - 可选
 * @param {string} query参数 status - 可选
 * @param {string} query参数 sanctionType - 可选
 * @param {integer (int32)} query参数 sanctionStart - 可选
 * @param {integer (int32)} query参数 sanctionEnd - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/user/sanctions/list', (req, res) => {
    console.log(`[GET] /user/sanctions/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListUserSanctionsResponse } = require('../models/responsedatalistusersanctionsresponse');
    res.json(generateResponseDataListUserSanctionsResponse());
  });

  /**
   * 
   * 原始路径: /user/sanctions/batch
   * 操作ID: batchDeleteUserSanctions
 * @param {array} query参数 ids - 必填
   */
  app.delete('/user/sanctions/batch', (req, res) => {
    console.log(`[DELETE] /user/sanctions/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
