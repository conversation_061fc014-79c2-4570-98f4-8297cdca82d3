
// posts-type-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /posts/type/{id}
   * 操作ID: getPostsType
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/posts/type/:id', (req, res) => {
    console.log(`[GET] /posts/type/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponsePostsTypeResponse } = require('../models/responsepoststyperesponse');
    res.json(generateResponsePostsTypeResponse());
  });

  /**
   * 
   * 原始路径: /posts/type/{id}
   * 操作ID: updatePostsType
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (PostsTypeRequest)} body参数 - 必填
   */
  app.put('/posts/type/:id', (req, res) => {
    console.log(`[PUT] /posts/type/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /posts/type/{id}
   * 操作ID: deletePostsType
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/posts/type/:id', (req, res) => {
    console.log(`[DELETE] /posts/type/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /posts/type/batch/status
   * 操作ID: batchUpdatePostsTypeStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/posts/type/batch/status', (req, res) => {
    console.log(`[PUT] /posts/type/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /posts/type
   * 操作ID: addPostsType
 * @param {对象 (PostsTypeRequest)} body参数 - 必填
   */
  app.post('/posts/type', (req, res) => {
    console.log(`[POST] /posts/type 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /posts/type/list
   * 操作ID: listPostsType
 * @param {string} query参数 status - 可选
 * @param {string} query参数 language - 可选
 * @param {string} query参数 typeName - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/posts/type/list', (req, res) => {
    console.log(`[GET] /posts/type/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListPostsTypeSimpleResponse } = require('../models/responsedatalistpoststypesimpleresponse');
    res.json(generateResponseDataListPostsTypeSimpleResponse());
  });

  /**
   * 
   * 原始路径: /posts/type/batch
   * 操作ID: batchDeletePostsTypes
 * @param {array} query参数 ids - 必填
   */
  app.delete('/posts/type/batch', (req, res) => {
    console.log(`[DELETE] /posts/type/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
