
// posts-brand-list-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /posts/brands/{id}
   * 操作ID: getPostsBrandList
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/posts/brands/:id', (req, res) => {
    console.log(`[GET] /posts/brands/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponsePostsBrandListResponse } = require('../models/responsepostsbrandlistresponse');
    res.json(generateResponsePostsBrandListResponse());
  });

  /**
   * 
   * 原始路径: /posts/brands/{id}
   * 操作ID: updatePostsBrandList
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (PostsBrandListRequest)} body参数 - 必填
   */
  app.put('/posts/brands/:id', (req, res) => {
    console.log(`[PUT] /posts/brands/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /posts/brands/{id}
   * 操作ID: deletePostsBrandList
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/posts/brands/:id', (req, res) => {
    console.log(`[DELETE] /posts/brands/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /posts/brands
   * 操作ID: addPostsBrandList
 * @param {对象 (PostsBrandListRequest)} body参数 - 必填
   */
  app.post('/posts/brands', (req, res) => {
    console.log(`[POST] /posts/brands 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /posts/brands/list
   * 操作ID: listPostsBrandList
 * @param {integer (int64)} query参数 postsId - 可选
 * @param {integer (int32)} query参数 brandId - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/posts/brands/list', (req, res) => {
    console.log(`[GET] /posts/brands/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListPostsBrandListResponse } = require('../models/responsedatalistpostsbrandlistresponse');
    res.json(generateResponseDataListPostsBrandListResponse());
  });

  /**
   * 
   * 原始路径: /posts/brands/batch
   * 操作ID: batchDeletePostsBrandLists
 * @param {array} query参数 ids - 必填
   */
  app.delete('/posts/brands/batch', (req, res) => {
    console.log(`[DELETE] /posts/brands/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
