
// quick-message-type-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /quick/message/type/{id}
   * 操作ID: getQuickMessageType
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/quick/message/type/:id', (req, res) => {
    console.log(`[GET] /quick/message/type/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseQuickMessageTypeResponse } = require('../models/responsequickmessagetyperesponse');
    res.json(generateResponseQuickMessageTypeResponse());
  });

  /**
   * 
   * 原始路径: /quick/message/type/{id}
   * 操作ID: updateQuickMessageType
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (QuickMessageTypeRequest)} body参数 - 必填
   */
  app.put('/quick/message/type/:id', (req, res) => {
    console.log(`[PUT] /quick/message/type/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /quick/message/type/{id}
   * 操作ID: deleteQuickMessageType
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/quick/message/type/:id', (req, res) => {
    console.log(`[DELETE] /quick/message/type/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /quick/message/type/batch/status
   * 操作ID: batchUpdateQuickMessageTypeStatus
 * @param {string} query参数 status - 必填
 * @param {array} query参数 ids - 必填
   */
  app.put('/quick/message/type/batch/status', (req, res) => {
    console.log(`[PUT] /quick/message/type/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /quick/message/type
   * 操作ID: addQuickMessageType
 * @param {对象 (QuickMessageTypeRequest)} body参数 - 必填
   */
  app.post('/quick/message/type', (req, res) => {
    console.log(`[POST] /quick/message/type 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /quick/message/type/list
   * 操作ID: listQuickMessageType
 * @param {string} query参数 status - 可选
 * @param {string} query参数 typeName - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/quick/message/type/list', (req, res) => {
    console.log(`[GET] /quick/message/type/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListQuickMessageTypeResponse } = require('../models/responsedatalistquickmessagetyperesponse');
    res.json(generateResponseDataListQuickMessageTypeResponse());
  });

  /**
   * 
   * 原始路径: /quick/message/type/batch
   * 操作ID: batchDeleteQuickMessageType
 * @param {array} query参数 ids - 必填
   */
  app.delete('/quick/message/type/batch', (req, res) => {
    console.log(`[DELETE] /quick/message/type/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });
};
