
// promotion-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /promotion/{id}
   * 操作ID: getPromotion
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/promotion/:id', (req, res) => {
    console.log(`[GET] /promotion/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponsePromotionResponse } = require('../models/responsepromotionresponse');
    res.json(generateResponsePromotionResponse());
  });

  /**
   * 
   * 原始路径: /promotion/{id}
   * 操作ID: updatePromotion
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (PromotionRequest)} body参数 - 必填
   */
  app.put('/promotion/:id', (req, res) => {
    console.log(`[PUT] /promotion/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /promotion/{id}
   * 操作ID: deletePromotion
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/promotion/:id', (req, res) => {
    console.log(`[DELETE] /promotion/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /promotion/batch/type
   * 操作ID: batchUpdatePromotionType
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 promotionType - 必填
   */
  app.put('/promotion/batch/type', (req, res) => {
    console.log(`[PUT] /promotion/batch/type 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /promotion/batch/status
   * 操作ID: batchUpdatePromotionStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/promotion/batch/status', (req, res) => {
    console.log(`[PUT] /promotion/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /promotion
   * 操作ID: addPromotion
 * @param {对象 (PromotionRequest)} body参数 - 必填
   */
  app.post('/promotion', (req, res) => {
    console.log(`[POST] /promotion 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /promotion/list
   * 操作ID: listPromotion
 * @param {string} query参数 promotionType - 可选
 * @param {integer (int32)} query参数 brandId - 可选
 * @param {integer (int32)} query参数 userId - 可选
 * @param {integer (int32)} query参数 storeId - 可选
 * @param {integer (int64)} query参数 itemId - 可选
 * @param {string} query参数 status - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/promotion/list', (req, res) => {
    console.log(`[GET] /promotion/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListPromotionResponse } = require('../models/responsedatalistpromotionresponse');
    res.json(generateResponseDataListPromotionResponse());
  });

  /**
   * 
   * 原始路径: /promotion/batch
   * 操作ID: batchDeletePromotion
 * @param {array} query参数 ids - 必填
   */
  app.delete('/promotion/batch', (req, res) => {
    console.log(`[DELETE] /promotion/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
