
// es-test-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /test/es/document/{indexName}/{id}
   * 操作ID: getDocument
 * @param {string} path参数 indexName - 必填
 * @param {string} path参数 id - 必填
   */
  app.get('/test/es/document/:indexName/:id', (req, res) => {
    console.log(`[GET] /test/es/document/{indexName}/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateDynamicDocumentResponse } = require('../models/dynamicdocumentresponse');
    res.json(generateDynamicDocumentResponse());
  });

  /**
   * 
   * 原始路径: /test/es/document/{indexName}/{id}
   * 操作ID: updateDocument
 * @param {string} path参数 indexName - 必填
 * @param {string} path参数 id - 必填
   */
  app.put('/test/es/document/:indexName/:id', (req, res) => {
    console.log(`[PUT] /test/es/document/{indexName}/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateDynamicDocumentResponse } = require('../models/dynamicdocumentresponse');
    res.json(generateDynamicDocumentResponse());
  });

  /**
   * 
   * 原始路径: /test/es/document/{indexName}/{id}
   * 操作ID: indexDocument
 * @param {string} path参数 indexName - 必填
 * @param {string} path参数 id - 必填
   */
  app.post('/test/es/document/:indexName/:id', (req, res) => {
    console.log(`[POST] /test/es/document/{indexName}/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateDynamicindexDocumentResponse } = require('../models/dynamicindexdocumentresponse');
    res.json(generateDynamicindexDocumentResponse());
  });

  /**
   * 
   * 原始路径: /test/es/init/{indexName}
   * 操作ID: initializeTestData
 * @param {string} path参数 indexName - 必填
 * @param {string} query参数 language - 必填
 * @param {integer (int32)} query参数 dataSize - 可选
   */
  app.post('/test/es/init/:indexName', (req, res) => {
    console.log(`[POST] /test/es/init/{indexName} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateDynamicinitializeTestResponse } = require('../models/dynamicinitializetestresponse');
    res.json(generateDynamicinitializeTestResponse());
  });

  /**
   * 
   * 原始路径: /test/es/index/{indexName}
   * 操作ID: createIndex
 * @param {string} path参数 indexName - 必填
 * @param {string} query参数 language - 必填
   */
  app.post('/test/es/index/:indexName', (req, res) => {
    console.log(`[POST] /test/es/index/{indexName} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateDynamiccreateIndexResponse } = require('../models/dynamiccreateindexresponse');
    res.json(generateDynamiccreateIndexResponse());
  });

  /**
   * 
   * 原始路径: /test/es/test
   * 操作ID: testConnection

   */
  app.get('/test/es/test', (req, res) => {
    console.log(`[GET] /test/es/test 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseMainResponse } = require('../models/responsemainresponse');
    res.json(generateResponseMainResponse());
  });

  /**
   * 
   * 原始路径: /test/es/search/{indexName}
   * 操作ID: search
 * @param {string} path参数 indexName - 必填
 * @param {string} query参数 language - 必填
 * @param {string} query参数 query - 必填
 * @param {integer (int32)} query参数 page - 可选
 * @param {integer (int32)} query参数 pageSize - 可选
   */
  app.get('/test/es/search/:indexName', (req, res) => {
    console.log(`[GET] /test/es/search/{indexName} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListObject } = require('../models/responsedatalistobject');
    res.json(generateResponseDataListObject());
  });

  /**
   * 
   * 原始路径: /test/es/search/store
   * 操作ID: searchStore
 * @param {string} query参数 language - 必填
 * @param {string} query参数 query - 必填
 * @param {array} query参数 tagIdList - 可选
 * @param {string} query参数 name - 可选
 * @param {number} query参数 minRating - 可选
 * @param {number} query参数 maxRating - 可选
 * @param {array} query参数 brandIdList - 可选
 * @param {array} query参数 servicesIdList - 可选
 * @param {integer (int32)} query参数 level - 可选
 * @param {integer (int32)} query参数 page - 可选
 * @param {integer (int32)} query参数 pageSize - 可选
   */
  app.get('/test/es/search/store', (req, res) => {
    console.log(`[GET] /test/es/search/store 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListObject } = require('../models/responsedatalistobject');
    res.json(generateResponseDataListObject());
  });

  /**
   * 
   * 原始路径: /test/es/search/posts
   * 操作ID: searchPosts
 * @param {string} query参数 language - 必填
 * @param {string} query参数 query - 必填
 * @param {array} query参数 brandIdList - 可选
 * @param {string} query参数 title - 可选
 * @param {number} query参数 minRating - 可选
 * @param {number} query参数 maxRating - 可选
 * @param {array} query参数 tagIdList - 可选
 * @param {boolean} query参数 isFree - 可选
 * @param {integer (int32)} query参数 page - 可选
 * @param {integer (int32)} query参数 pageSize - 可选
   */
  app.get('/test/es/search/posts', (req, res) => {
    console.log(`[GET] /test/es/search/posts 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListObject } = require('../models/responsedatalistobject');
    res.json(generateResponseDataListObject());
  });

  /**
   * 
   * 原始路径: /test/es/search/item
   * 操作ID: searchItem
 * @param {string} query参数 language - 必填
 * @param {string} query参数 query - 必填
 * @param {integer (int32)} query参数 servicesId - 可选
 * @param {array} query参数 attrValueId - 可选
 * @param {array} query参数 tagId - 可选
 * @param {string} query参数 name - 可选
 * @param {number} query参数 minPrice - 可选
 * @param {number} query参数 maxPrice - 可选
 * @param {integer (int32)} query参数 minStockQuantity - 可选
 * @param {integer (int32)} query参数 maxStockQuantity - 可选
 * @param {string} query参数 sortColumn - 可选
 * @param {string} query参数 sortOrder - 可选
 * @param {integer (int32)} query参数 page - 可选
 * @param {integer (int32)} query参数 pageSize - 可选
   */
  app.get('/test/es/search/item', (req, res) => {
    console.log(`[GET] /test/es/search/item 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListObject } = require('../models/responsedatalistobject');
    res.json(generateResponseDataListObject());
  });

  /**
   * 
   * 原始路径: /test/es/search/demand
   * 操作ID: searchDemand
 * @param {string} query参数 language - 必填
 * @param {string} query参数 query - 必填
 * @param {string} query参数 demandName - 可选
 * @param {string (byte)} query参数 status - 可选
 * @param {integer (int32)} query参数 brandId - 可选
 * @param {integer (int32)} query参数 servicesId - 可选
 * @param {number} query参数 minAmount - 可选
 * @param {number} query参数 maxAmount - 可选
 * @param {array} query参数 attrValueIdList - 可选
 * @param {integer (int32)} query参数 page - 可选
 * @param {integer (int32)} query参数 pageSize - 可选
   */
  app.get('/test/es/search/demand', (req, res) => {
    console.log(`[GET] /test/es/search/demand 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListObject } = require('../models/responsedatalistobject');
    res.json(generateResponseDataListObject());
  });

  /**
   * 
   * 原始路径: /test/es/search/campaign
   * 操作ID: searchCampaign
 * @param {string} query参数 language - 必填
 * @param {string} query参数 query - 必填
 * @param {string} query参数 title - 可选
 * @param {array} query参数 tagIdList - 可选
 * @param {integer (int32)} query参数 page - 可选
 * @param {integer (int32)} query参数 pageSize - 可选
   */
  app.get('/test/es/search/campaign', (req, res) => {
    console.log(`[GET] /test/es/search/campaign 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListObject } = require('../models/responsedatalistobject');
    res.json(generateResponseDataListObject());
  });
};
