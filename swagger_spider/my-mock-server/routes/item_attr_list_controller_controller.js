
// item-attr-list-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /item/attrs/{id}
   * 操作ID: getItemAttrList
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/item/attrs/:id', (req, res) => {
    console.log(`[GET] /item/attrs/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseItemAttrListResponse } = require('../models/responseitemattrlistresponse');
    res.json(generateResponseItemAttrListResponse());
  });

  /**
   * 
   * 原始路径: /item/attrs/{id}
   * 操作ID: updateItemAttrList
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (ItemAttrListRequest)} body参数 - 必填
   */
  app.put('/item/attrs/:id', (req, res) => {
    console.log(`[PUT] /item/attrs/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /item/attrs/{id}
   * 操作ID: deleteItemAttrList
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/item/attrs/:id', (req, res) => {
    console.log(`[DELETE] /item/attrs/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /item/attrs
   * 操作ID: addItemAttrList
 * @param {对象 (ItemAttrListRequest)} body参数 - 必填
   */
  app.post('/item/attrs', (req, res) => {
    console.log(`[POST] /item/attrs 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /item/attrs/list
   * 操作ID: listItemAttrList
 * @param {integer (int64)} query参数 itemId - 可选
 * @param {integer (int32)} query参数 attrId - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/item/attrs/list', (req, res) => {
    console.log(`[GET] /item/attrs/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListItemAttrListResponse } = require('../models/responsedatalistitemattrlistresponse');
    res.json(generateResponseDataListItemAttrListResponse());
  });

  /**
   * 
   * 原始路径: /item/attrs/batch
   * 操作ID: batchDeleteItemAttrList
 * @param {array} query参数 ids - 必填
   */
  app.delete('/item/attrs/batch', (req, res) => {
    console.log(`[DELETE] /item/attrs/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
