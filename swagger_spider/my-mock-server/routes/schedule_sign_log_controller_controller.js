
// schedule-sign-log-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /schedule/sign/log/sign/{id}
   * 操作ID: signScheduleSignLog
 * @param {integer (int64)} path参数 id - 必填
   */
  app.put('/schedule/sign/log/sign/:id', (req, res) => {
    console.log(`[PUT] /schedule/sign/log/sign/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseBoolean } = require('../models/responseboolean');
    res.json(generateResponseBoolean());
  });

  /**
   * 
   * 原始路径: /schedule/sign/log/batch/status
   * 操作ID: batchUpdateScheduleSignLogStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/schedule/sign/log/batch/status', (req, res) => {
    console.log(`[PUT] /schedule/sign/log/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseBoolean } = require('../models/responseboolean');
    res.json(generateResponseBoolean());
  });

  /**
   * 
   * 原始路径: /schedule/sign/log/batch/schedule
   * 操作ID: batchUpdateScheduleSignLogSchedule
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 scheduleId - 必填
   */
  app.put('/schedule/sign/log/batch/schedule', (req, res) => {
    console.log(`[PUT] /schedule/sign/log/batch/schedule 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseBoolean } = require('../models/responseboolean');
    res.json(generateResponseBoolean());
  });

  /**
   * 
   * 原始路径: /schedule/sign/log/batch/manage
   * 操作ID: batchUpdateScheduleSignLogManage
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 manageId - 必填
   */
  app.put('/schedule/sign/log/batch/manage', (req, res) => {
    console.log(`[PUT] /schedule/sign/log/batch/manage 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseBoolean } = require('../models/responseboolean');
    res.json(generateResponseBoolean());
  });

  /**
   * 
   * 原始路径: /schedule/sign/log/approve/{id}
   * 操作ID: approveScheduleSignLog
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (ScheduleSignLogApproveRequest)} body参数 - 必填
   */
  app.put('/schedule/sign/log/approve/:id', (req, res) => {
    console.log(`[PUT] /schedule/sign/log/approve/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseBoolean } = require('../models/responseboolean');
    res.json(generateResponseBoolean());
  });

  /**
   * 
   * 原始路径: /schedule/sign/log/apply/{id}
   * 操作ID: applyScheduleSignLog
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (ScheduleSignLogApplyRequest)} body参数 - 必填
   */
  app.put('/schedule/sign/log/apply/:id', (req, res) => {
    console.log(`[PUT] /schedule/sign/log/apply/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseBoolean } = require('../models/responseboolean');
    res.json(generateResponseBoolean());
  });

  /**
   * 
   * 原始路径: /schedule/sign/log/{id}
   * 操作ID: getScheduleSignLogById
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/schedule/sign/log/:id', (req, res) => {
    console.log(`[GET] /schedule/sign/log/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseScheduleSignLogResponse } = require('../models/responseschedulesignlogresponse');
    res.json(generateResponseScheduleSignLogResponse());
  });

  /**
   * 
   * 原始路径: /schedule/sign/log/{id}
   * 操作ID: deleteScheduleSignLog
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/schedule/sign/log/:id', (req, res) => {
    console.log(`[DELETE] /schedule/sign/log/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseBoolean } = require('../models/responseboolean');
    res.json(generateResponseBoolean());
  });

  /**
   * 
   * 原始路径: /schedule/sign/log/pop-sign-log
   * 操作ID: getPopScheduleSignLog

   */
  app.get('/schedule/sign/log/pop-sign-log', (req, res) => {
    console.log(`[GET] /schedule/sign/log/pop-sign-log 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseScheduleSignLogResponse } = require('../models/responseschedulesignlogresponse');
    res.json(generateResponseScheduleSignLogResponse());
  });

  /**
   * 
   * 原始路径: /schedule/sign/log/list
   * 操作ID: getScheduleSignLogList
 * @param {string} query参数 status - 可选
 * @param {integer (int32)} query参数 scheduleId - 可选
 * @param {integer (int32)} query参数 manageId - 可选
 * @param {string} query参数 signType - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/schedule/sign/log/list', (req, res) => {
    console.log(`[GET] /schedule/sign/log/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListScheduleSignLogResponse } = require('../models/responsedatalistschedulesignlogresponse');
    res.json(generateResponseDataListScheduleSignLogResponse());
  });

  /**
   * 
   * 原始路径: /schedule/sign/log/batch
   * 操作ID: batchDeleteScheduleSignLogs
 * @param {array} query参数 ids - 必填
   */
  app.delete('/schedule/sign/log/batch', (req, res) => {
    console.log(`[DELETE] /schedule/sign/log/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseBoolean } = require('../models/responseboolean');
    res.json(generateResponseBoolean());
  });
};
