
// orders-data-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /orders/data/{id}
   * 操作ID: getOrderData
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/orders/data/:id', (req, res) => {
    console.log(`[GET] /orders/data/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseOrdersDataResponse } = require('../models/responseordersdataresponse');
    res.json(generateResponseOrdersDataResponse());
  });

  /**
   * 
   * 原始路径: /orders/data/{id}
   * 操作ID: updateOrderData
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (OrdersDataRequest)} body参数 - 必填
   */
  app.put('/orders/data/:id', (req, res) => {
    console.log(`[PUT] /orders/data/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /orders/data/{id}
   * 操作ID: deleteOrderData
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/orders/data/:id', (req, res) => {
    console.log(`[DELETE] /orders/data/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /orders/data/batch/type
   * 操作ID: batchUpdateOrderDataType
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 dataType - 必填
   */
  app.put('/orders/data/batch/type', (req, res) => {
    console.log(`[PUT] /orders/data/batch/type 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /orders/data
   * 操作ID: addOrderData
 * @param {对象 (OrdersDataRequest)} body参数 - 必填
   */
  app.post('/orders/data', (req, res) => {
    console.log(`[POST] /orders/data 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /orders/data/list
   * 操作ID: listOrdersData
 * @param {integer (int64)} query参数 ordersId - 可选
 * @param {string} query参数 dataType - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.post('/orders/data/list', (req, res) => {
    console.log(`[POST] /orders/data/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListOrdersDataResponse } = require('../models/responsedatalistordersdataresponse');
    res.json(generateResponseDataListOrdersDataResponse());
  });

  /**
   * 
   * 原始路径: /orders/data/batch
   * 操作ID: batchDeleteOrderData
 * @param {array} query参数 ids - 必填
   */
  app.delete('/orders/data/batch', (req, res) => {
    console.log(`[DELETE] /orders/data/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
