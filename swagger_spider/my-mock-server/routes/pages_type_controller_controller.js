
// pages-type-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /pages/type/{id}
   * 操作ID: getPageType
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/pages/type/:id', (req, res) => {
    console.log(`[GET] /pages/type/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponsePagesTypeResponse } = require('../models/responsepagestyperesponse');
    res.json(generateResponsePagesTypeResponse());
  });

  /**
   * 
   * 原始路径: /pages/type/{id}
   * 操作ID: updatePageType
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (PagesTypeRequest)} body参数 - 必填
   */
  app.put('/pages/type/:id', (req, res) => {
    console.log(`[PUT] /pages/type/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /pages/type/{id}
   * 操作ID: deletePageType
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/pages/type/:id', (req, res) => {
    console.log(`[DELETE] /pages/type/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /pages/type/batch/status
   * 操作ID: batchUpdatePageTypeStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/pages/type/batch/status', (req, res) => {
    console.log(`[PUT] /pages/type/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /pages/type
   * 操作ID: addPageType
 * @param {对象 (PagesTypeRequest)} body参数 - 必填
   */
  app.post('/pages/type', (req, res) => {
    console.log(`[POST] /pages/type 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /pages/type/list
   * 操作ID: getPageTypeList
 * @param {string} query参数 status - 可选
 * @param {string} query参数 language - 可选
 * @param {string} query参数 typeName - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/pages/type/list', (req, res) => {
    console.log(`[GET] /pages/type/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListPagesTypeResponse } = require('../models/responsedatalistpagestyperesponse');
    res.json(generateResponseDataListPagesTypeResponse());
  });

  /**
   * 
   * 原始路径: /pages/type/batch
   * 操作ID: batchDeletePageTypes
 * @param {array} query参数 ids - 必填
   */
  app.delete('/pages/type/batch', (req, res) => {
    console.log(`[DELETE] /pages/type/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });
};
