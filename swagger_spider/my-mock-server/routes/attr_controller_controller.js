
// attr-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /attr/{id}
   * 操作ID: getAttr
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/attr/:id', (req, res) => {
    console.log(`[GET] /attr/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseAttrResponse } = require('../models/responseattrresponse');
    res.json(generateResponseAttrResponse());
  });

  /**
   * 
   * 原始路径: /attr/{id}
   * 操作ID: updateAttr
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (AttrRequest)} body参数 - 必填
   */
  app.put('/attr/:id', (req, res) => {
    console.log(`[PUT] /attr/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /attr/{id}
   * 操作ID: deleteAttr
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/attr/:id', (req, res) => {
    console.log(`[DELETE] /attr/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /attr/batch/status
   * 操作ID: batchUpdateAttributeStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/attr/batch/status', (req, res) => {
    console.log(`[PUT] /attr/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /attr/batch/parent
   * 操作ID: batchUpdateAttributeParentId
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 parentId - 必填
   */
  app.put('/attr/batch/parent', (req, res) => {
    console.log(`[PUT] /attr/batch/parent 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /attr
   * 操作ID: addAttr
 * @param {对象 (AttrRequest)} body参数 - 必填
   */
  app.post('/attr', (req, res) => {
    console.log(`[POST] /attr 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /attr/list
   * 操作ID: getAttrList
 * @param {integer (int32)} query参数 parentId - 可选
 * @param {string} query参数 status - 可选
 * @param {string (byte)} query参数 attrType - 可选
 * @param {string} query参数 customUrl - 可选
 * @param {string} query参数 language - 可选
 * @param {string} query参数 attrName - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/attr/list', (req, res) => {
    console.log(`[GET] /attr/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListAttrSimpleResponse } = require('../models/responsedatalistattrsimpleresponse');
    res.json(generateResponseDataListAttrSimpleResponse());
  });

  /**
   * 
   * 原始路径: /attr/batch
   * 操作ID: batchDeleteAttribute
 * @param {array} query参数 ids - 必填
   */
  app.delete('/attr/batch', (req, res) => {
    console.log(`[DELETE] /attr/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });
};
