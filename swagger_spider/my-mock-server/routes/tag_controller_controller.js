
// tag-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /tag/{id}
   * 操作ID: getTag
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/tag/:id', (req, res) => {
    console.log(`[GET] /tag/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseTagResponse } = require('../models/responsetagresponse');
    res.json(generateResponseTagResponse());
  });

  /**
   * 
   * 原始路径: /tag/{id}
   * 操作ID: updateTag
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (TagRequest)} body参数 - 必填
   */
  app.put('/tag/:id', (req, res) => {
    console.log(`[PUT] /tag/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /tag/{id}
   * 操作ID: deleteTag
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/tag/:id', (req, res) => {
    console.log(`[DELETE] /tag/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /tag/batch/use-type
   * 操作ID: batchUpdateUseType
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 useType - 必填
   */
  app.put('/tag/batch/use-type', (req, res) => {
    console.log(`[PUT] /tag/batch/use-type 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /tag/batch/status
   * 操作ID: batchUpdateTagStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/tag/batch/status', (req, res) => {
    console.log(`[PUT] /tag/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /tag/batch/sort
   * 操作ID: batchUpdateSortIndex
 * @param {array} query参数 ids - 必填
 * @param {string (byte)} query参数 sortIndex - 必填
   */
  app.put('/tag/batch/sort', (req, res) => {
    console.log(`[PUT] /tag/batch/sort 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /tag
   * 操作ID: addTag
 * @param {对象 (TagRequest)} body参数 - 必填
   */
  app.post('/tag', (req, res) => {
    console.log(`[POST] /tag 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /tag/list
   * 操作ID: getTagList
 * @param {string} query参数 status - 可选
 * @param {string (byte)} query参数 sortIndex - 可选
 * @param {string} query参数 useType - 可选
 * @param {string} query参数 language - 可选
 * @param {string} query参数 tagName - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/tag/list', (req, res) => {
    console.log(`[GET] /tag/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListTagSimpleResponse } = require('../models/responsedatalisttagsimpleresponse');
    res.json(generateResponseDataListTagSimpleResponse());
  });

  /**
   * 
   * 原始路径: /tag/batch
   * 操作ID: batchDeleteTag
 * @param {array} query参数 ids - 必填
   */
  app.delete('/tag/batch', (req, res) => {
    console.log(`[DELETE] /tag/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });
};
