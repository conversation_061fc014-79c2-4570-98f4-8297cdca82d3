
// rate-config-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /rate/config/{id}
   * 操作ID: getRateConfig
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/rate/config/:id', (req, res) => {
    console.log(`[GET] /rate/config/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseRateConfigResponse } = require('../models/responserateconfigresponse');
    res.json(generateResponseRateConfigResponse());
  });

  /**
   * 
   * 原始路径: /rate/config/{id}
   * 操作ID: updateRateConfig
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (RateConfigRequest)} body参数 - 必填
   */
  app.put('/rate/config/:id', (req, res) => {
    console.log(`[PUT] /rate/config/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /rate/config/{id}
   * 操作ID: deleteRateConfig
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/rate/config/:id', (req, res) => {
    console.log(`[DELETE] /rate/config/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /rate/config/batch/currency
   * 操作ID: batchUpdateCurrency
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 currency - 必填
   */
  app.put('/rate/config/batch/currency', (req, res) => {
    console.log(`[PUT] /rate/config/batch/currency 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /rate/config
   * 操作ID: addRateConfig
 * @param {对象 (RateConfigRequest)} body参数 - 必填
   */
  app.post('/rate/config', (req, res) => {
    console.log(`[POST] /rate/config 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /rate/config/list
   * 操作ID: listRateConfig
 * @param {string} query参数 currency - 可选
 * @param {string} query参数 currencyName - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/rate/config/list', (req, res) => {
    console.log(`[GET] /rate/config/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListRateConfigResponse } = require('../models/responsedatalistrateconfigresponse');
    res.json(generateResponseDataListRateConfigResponse());
  });

  /**
   * 
   * 原始路径: /rate/config/batch
   * 操作ID: batchDeleteRateConfig
 * @param {array} query参数 ids - 必填
   */
  app.delete('/rate/config/batch', (req, res) => {
    console.log(`[DELETE] /rate/config/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
