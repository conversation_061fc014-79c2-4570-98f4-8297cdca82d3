
// posts-comment-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /posts/comment/{id}
   * 操作ID: getPostsComment
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/posts/comment/:id', (req, res) => {
    console.log(`[GET] /posts/comment/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponsePostsCommentResponse } = require('../models/responsepostscommentresponse');
    res.json(generateResponsePostsCommentResponse());
  });

  /**
   * 
   * 原始路径: /posts/comment/{id}
   * 操作ID: updatePostsComment
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (PostsCommentRequest)} body参数 - 必填
   */
  app.put('/posts/comment/:id', (req, res) => {
    console.log(`[PUT] /posts/comment/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /posts/comment/{id}
   * 操作ID: deletePostsComment
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/posts/comment/:id', (req, res) => {
    console.log(`[DELETE] /posts/comment/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /posts/comment/batch/status
   * 操作ID: batchUpdatePostsCommentStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/posts/comment/batch/status', (req, res) => {
    console.log(`[PUT] /posts/comment/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /posts/comment
   * 操作ID: addPostsComment
 * @param {对象 (PostsCommentRequest)} body参数 - 必填
   */
  app.post('/posts/comment', (req, res) => {
    console.log(`[POST] /posts/comment 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /posts/comment/list
   * 操作ID: list
 * @param {integer (int64)} query参数 postsId - 可选
 * @param {integer (int32)} query参数 userId - 可选
 * @param {string} query参数 status - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/posts/comment/list', (req, res) => {
    console.log(`[GET] /posts/comment/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListPostsCommentSimpleResponse } = require('../models/responsedatalistpostscommentsimpleresponse');
    res.json(generateResponseDataListPostsCommentSimpleResponse());
  });

  /**
   * 
   * 原始路径: /posts/comment/batch
   * 操作ID: batchDeletePostsComments
 * @param {array} query参数 ids - 必填
   */
  app.delete('/posts/comment/batch', (req, res) => {
    console.log(`[DELETE] /posts/comment/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
