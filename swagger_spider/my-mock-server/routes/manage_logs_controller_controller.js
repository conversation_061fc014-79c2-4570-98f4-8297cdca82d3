
// manage-logs-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /manage/logs/{id}
   * 操作ID: getManageLogs
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/manage/logs/:id', (req, res) => {
    console.log(`[GET] /manage/logs/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseManageLogsResponse } = require('../models/responsemanagelogsresponse');
    res.json(generateResponseManageLogsResponse());
  });

  /**
   * 
   * 原始路径: /manage/logs/{id}
   * 操作ID: updateManageLogs
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (ManageLogsRequest)} body参数 - 必填
   */
  app.put('/manage/logs/:id', (req, res) => {
    console.log(`[PUT] /manage/logs/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /manage/logs/{id}
   * 操作ID: deleteManageLogs
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/manage/logs/:id', (req, res) => {
    console.log(`[DELETE] /manage/logs/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /manage/logs
   * 操作ID: addManageLogs
 * @param {对象 (ManageLogsRequest)} body参数 - 必填
   */
  app.post('/manage/logs', (req, res) => {
    console.log(`[POST] /manage/logs 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /manage/logs/list
   * 操作ID: listManageLogs
 * @param {integer (int32)} query参数 manageId - 可选
 * @param {string} query参数 method - 可选
 * @param {integer (int32)} query参数 code - 可选
 * @param {string} query参数 module - 可选
 * @param {string} query参数 action - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/manage/logs/list', (req, res) => {
    console.log(`[GET] /manage/logs/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListManageLogsSimpleResponse } = require('../models/responsedatalistmanagelogssimpleresponse');
    res.json(generateResponseDataListManageLogsSimpleResponse());
  });

  /**
   * 
   * 原始路径: /manage/logs/batch
   * 操作ID: batchDeleteManageLogs
 * @param {array} query参数 logIds - 必填
   */
  app.delete('/manage/logs/batch', (req, res) => {
    console.log(`[DELETE] /manage/logs/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });
};
