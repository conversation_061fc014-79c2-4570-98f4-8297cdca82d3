
// posts-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /posts/{id}
   * 操作ID: getPost
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/posts/:id', (req, res) => {
    console.log(`[GET] /posts/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponsePostsResponse } = require('../models/responsepostsresponse');
    res.json(generateResponsePostsResponse());
  });

  /**
   * 
   * 原始路径: /posts/{id}
   * 操作ID: updatePost
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (PostsRequest)} body参数 - 必填
   */
  app.put('/posts/:id', (req, res) => {
    console.log(`[PUT] /posts/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /posts/{id}
   * 操作ID: deletePost
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/posts/:id', (req, res) => {
    console.log(`[DELETE] /posts/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /posts/batch/user
   * 操作ID: batchUpdatePostsUser
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 userId - 必填
   */
  app.put('/posts/batch/user', (req, res) => {
    console.log(`[PUT] /posts/batch/user 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /posts/batch/type
   * 操作ID: batchUpdatePostsType
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 postsType - 必填
   */
  app.put('/posts/batch/type', (req, res) => {
    console.log(`[PUT] /posts/batch/type 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /posts/batch/store
   * 操作ID: batchUpdatePostsStore
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 storeId - 必填
   */
  app.put('/posts/batch/store', (req, res) => {
    console.log(`[PUT] /posts/batch/store 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /posts/batch/status
   * 操作ID: batchUpdatePostStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/posts/batch/status', (req, res) => {
    console.log(`[PUT] /posts/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /posts
   * 操作ID: addPosts
 * @param {对象 (PostsRequest)} body参数 - 必填
   */
  app.post('/posts', (req, res) => {
    console.log(`[POST] /posts 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /posts/list
   * 操作ID: listPosts
 * @param {string} query参数 postsType - 可选
 * @param {integer (int32)} query参数 userId - 可选
 * @param {integer (int32)} query参数 storeId - 可选
 * @param {string} query参数 status - 可选
 * @param {string} query参数 language - 可选
 * @param {string} query参数 title - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/posts/list', (req, res) => {
    console.log(`[GET] /posts/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListPostsSimpleResponse } = require('../models/responsedatalistpostssimpleresponse');
    res.json(generateResponseDataListPostsSimpleResponse());
  });

  /**
   * 
   * 原始路径: /posts/batch
   * 操作ID: batchDeletePosts
 * @param {array} query参数 ids - 必填
   */
  app.delete('/posts/batch', (req, res) => {
    console.log(`[DELETE] /posts/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
