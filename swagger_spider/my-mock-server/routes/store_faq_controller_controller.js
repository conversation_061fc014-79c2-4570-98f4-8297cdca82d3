
// store-faq-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /store/faq/{id}
   * 操作ID: getStoreFaq
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/store/faq/:id', (req, res) => {
    console.log(`[GET] /store/faq/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseStoreFaqResponse } = require('../models/responsestorefaqresponse');
    res.json(generateResponseStoreFaqResponse());
  });

  /**
   * 
   * 原始路径: /store/faq/{id}
   * 操作ID: updateStoreFaq
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (StoreFaqRequest)} body参数 - 必填
   */
  app.put('/store/faq/:id', (req, res) => {
    console.log(`[PUT] /store/faq/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /store/faq/{id}
   * 操作ID: deleteStoreFaq
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/store/faq/:id', (req, res) => {
    console.log(`[DELETE] /store/faq/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /store/faq/batch/store
   * 操作ID: batchUpdateStoreFaqStore
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 storeId - 必填
   */
  app.put('/store/faq/batch/store', (req, res) => {
    console.log(`[PUT] /store/faq/batch/store 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /store/faq/batch/status
   * 操作ID: batchUpdateStoreFaqStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/store/faq/batch/status', (req, res) => {
    console.log(`[PUT] /store/faq/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /store/faq/batch/item
   * 操作ID: batchUpdateItemForStoreFaq
 * @param {array} query参数 ids - 必填
 * @param {integer (int64)} query参数 itemId - 必填
   */
  app.put('/store/faq/batch/item', (req, res) => {
    console.log(`[PUT] /store/faq/batch/item 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /store/faq
   * 操作ID: addStoreFaq
 * @param {对象 (StoreFaqRequest)} body参数 - 必填
   */
  app.post('/store/faq', (req, res) => {
    console.log(`[POST] /store/faq 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /store/faq/list
   * 操作ID: listStoreFaq
 * @param {integer (int32)} query参数 storeId - 可选
 * @param {integer (int64)} query参数 itemId - 可选
 * @param {string} query参数 status - 可选
 * @param {string} query参数 language - 可选
 * @param {string} query参数 question - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/store/faq/list', (req, res) => {
    console.log(`[GET] /store/faq/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListStoreFaqSimpleResponse } = require('../models/responsedataliststorefaqsimpleresponse');
    res.json(generateResponseDataListStoreFaqSimpleResponse());
  });

  /**
   * 
   * 原始路径: /store/faq/batch
   * 操作ID: batchDeleteStoreFaq
 * @param {array} query参数 ids - 必填
   */
  app.delete('/store/faq/batch', (req, res) => {
    console.log(`[DELETE] /store/faq/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
