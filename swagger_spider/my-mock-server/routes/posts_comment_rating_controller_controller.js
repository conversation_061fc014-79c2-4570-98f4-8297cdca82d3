
// posts-comment-rating-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /posts/comment/rating/{id}
   * 操作ID: getPostsCommentRating
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/posts/comment/rating/:id', (req, res) => {
    console.log(`[GET] /posts/comment/rating/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponsePostsCommentRatingResponse } = require('../models/responsepostscommentratingresponse');
    res.json(generateResponsePostsCommentRatingResponse());
  });

  /**
   * 
   * 原始路径: /posts/comment/rating/{id}
   * 操作ID: updatePostsCommentRating
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (PostsCommentRatingRequest)} body参数 - 必填
   */
  app.put('/posts/comment/rating/:id', (req, res) => {
    console.log(`[PUT] /posts/comment/rating/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /posts/comment/rating/{id}
   * 操作ID: deletePostsCommentRating
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/posts/comment/rating/:id', (req, res) => {
    console.log(`[DELETE] /posts/comment/rating/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /posts/comment/rating
   * 操作ID: addPostsCommentRating
 * @param {对象 (PostsCommentRatingRequest)} body参数 - 必填
   */
  app.post('/posts/comment/rating', (req, res) => {
    console.log(`[POST] /posts/comment/rating 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /posts/comment/rating/list
   * 操作ID: listPostsCommentRating
 * @param {integer (int64)} query参数 postsId - 可选
 * @param {integer (int32)} query参数 userId - 可选
 * @param {integer (int32)} query参数 ratingId - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/posts/comment/rating/list', (req, res) => {
    console.log(`[GET] /posts/comment/rating/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListPostsCommentRatingResponse } = require('../models/responsedatalistpostscommentratingresponse');
    res.json(generateResponseDataListPostsCommentRatingResponse());
  });

  /**
   * 
   * 原始路径: /posts/comment/rating/batch
   * 操作ID: batchDeletePostsCommentRatings
 * @param {array} query参数 ids - 必填
   */
  app.delete('/posts/comment/rating/batch', (req, res) => {
    console.log(`[DELETE] /posts/comment/rating/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
