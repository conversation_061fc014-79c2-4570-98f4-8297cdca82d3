
// telegram-robot-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /telegram/robot/{id}
   * 操作ID: getTelegramRobot
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/telegram/robot/:id', (req, res) => {
    console.log(`[GET] /telegram/robot/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseTelegramRobotResponse } = require('../models/responsetelegramrobotresponse');
    res.json(generateResponseTelegramRobotResponse());
  });

  /**
   * 
   * 原始路径: /telegram/robot/{id}
   * 操作ID: updateTelegramRobot
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (TelegramRobotRequest)} body参数 - 必填
   */
  app.put('/telegram/robot/:id', (req, res) => {
    console.log(`[PUT] /telegram/robot/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /telegram/robot/{id}
   * 操作ID: deleteTelegramRobot
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/telegram/robot/:id', (req, res) => {
    console.log(`[DELETE] /telegram/robot/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /telegram/robot
   * 操作ID: addTelegramRobot
 * @param {对象 (TelegramRobotRequest)} body参数 - 必填
   */
  app.post('/telegram/robot', (req, res) => {
    console.log(`[POST] /telegram/robot 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /telegram/robot/list
   * 操作ID: listTelegramRobots
 * @param {string} query参数 name - 可选
 * @param {string} query参数 botName - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/telegram/robot/list', (req, res) => {
    console.log(`[GET] /telegram/robot/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListTelegramRobotResponse } = require('../models/responsedatalisttelegramrobotresponse');
    res.json(generateResponseDataListTelegramRobotResponse());
  });

  /**
   * 
   * 原始路径: /telegram/robot/batch
   * 操作ID: batchDeleteTelegramRobots
 * @param {array} query参数 ids - 必填
   */
  app.delete('/telegram/robot/batch', (req, res) => {
    console.log(`[DELETE] /telegram/robot/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
