
// system-language-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /system/language/{id}
   * 操作ID: getSystemLanguage
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/system/language/:id', (req, res) => {
    console.log(`[GET] /system/language/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseSystemLanguageResponse } = require('../models/responsesystemlanguageresponse');
    res.json(generateResponseSystemLanguageResponse());
  });

  /**
   * 
   * 原始路径: /system/language/{id}
   * 操作ID: updateSystemLanguage
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (SystemLanguageRequest)} body参数 - 必填
   */
  app.put('/system/language/:id', (req, res) => {
    console.log(`[PUT] /system/language/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /system/language/{id}
   * 操作ID: deleteSystemLanguage
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/system/language/:id', (req, res) => {
    console.log(`[DELETE] /system/language/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /system/language/batch/language
   * 操作ID: batchUpdateSystemLanguageCode
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 language - 必填
   */
  app.put('/system/language/batch/language', (req, res) => {
    console.log(`[PUT] /system/language/batch/language 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /system/language/batch/entities
   * 操作ID: batchUpdateSystemLanguageEntities
 * @param {对象 (BatchSystemLanguageRequest)} body参数 - 必填
   */
  app.put('/system/language/batch/entities', (req, res) => {
    console.log(`[PUT] /system/language/batch/entities 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /system/language
   * 操作ID: addSystemLanguage
 * @param {对象 (SystemLanguageRequest)} body参数 - 必填
   */
  app.post('/system/language', (req, res) => {
    console.log(`[POST] /system/language 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /system/language/batch
   * 操作ID: addSystemLanguageMulti
 * @param {数组<SystemLanguageRequest>} body参数 - 必填
   */
  app.post('/system/language/batch', (req, res) => {
    console.log(`[POST] /system/language/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /system/language/batch
   * 操作ID: deleteSystemLanguageMulti
 * @param {array} query参数 ids - 必填
   */
  app.delete('/system/language/batch', (req, res) => {
    console.log(`[DELETE] /system/language/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /system/language/list
   * 操作ID: listSystemLanguages
 * @param {string} query参数 language - 可选
 * @param {string} query参数 langKey - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/system/language/list', (req, res) => {
    console.log(`[GET] /system/language/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListSystemLanguageResponse } = require('../models/responsedatalistsystemlanguageresponse');
    res.json(generateResponseDataListSystemLanguageResponse());
  });

  /**
   * 
   * 原始路径: /system/language/export/txt
   * 操作ID: exportSystemLanguagesByLanguageCode
 * @param {string} query参数 languageCode - 必填
   */
  app.get('/system/language/export/txt', (req, res) => {
    console.log(`[GET] /system/language/export/txt 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateDynamicexportSystemLanguagesByLanguageCodeResponse } = require('../models/dynamicexportsystemlanguagesbylanguagecoderesponse');
    res.json(generateDynamicexportSystemLanguagesByLanguageCodeResponse());
  });
};
