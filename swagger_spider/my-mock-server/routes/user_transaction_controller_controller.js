
// user-transaction-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /user/transaction/{id}
   * 操作ID: getUserTransaction
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/user/transaction/:id', (req, res) => {
    console.log(`[GET] /user/transaction/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseUserTransactionResponse } = require('../models/responseusertransactionresponse');
    res.json(generateResponseUserTransactionResponse());
  });

  /**
   * 
   * 原始路径: /user/transaction/{id}
   * 操作ID: updateUserTransactions
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (UserTransactionRequest)} body参数 - 必填
   */
  app.put('/user/transaction/:id', (req, res) => {
    console.log(`[PUT] /user/transaction/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /user/transaction/{id}
   * 操作ID: deleteUserTransaction
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/user/transaction/:id', (req, res) => {
    console.log(`[DELETE] /user/transaction/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /user/transaction/batch/status
   * 操作ID: batchUpdateUserTransactionStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/user/transaction/batch/status', (req, res) => {
    console.log(`[PUT] /user/transaction/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /user/transaction
   * 操作ID: addUserTransactions
 * @param {对象 (UserTransactionRequest)} body参数 - 必填
   */
  app.post('/user/transaction', (req, res) => {
    console.log(`[POST] /user/transaction 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /user/transaction/list
   * 操作ID: listUserTransaction
 * @param {integer (int32)} query参数 userId - 可选
 * @param {integer (int32)} query参数 storeId - 可选
 * @param {string} query参数 status - 可选
 * @param {string} query参数 changeType - 可选
 * @param {integer (int64)} query参数 relationId - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/user/transaction/list', (req, res) => {
    console.log(`[GET] /user/transaction/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListUserTransactionResponse } = require('../models/responsedatalistusertransactionresponse');
    res.json(generateResponseDataListUserTransactionResponse());
  });

  /**
   * 
   * 原始路径: /user/transaction/batch
   * 操作ID: batchDeleteUserTransactions
 * @param {array} query参数 ids - 必填
   */
  app.delete('/user/transaction/batch', (req, res) => {
    console.log(`[DELETE] /user/transaction/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
