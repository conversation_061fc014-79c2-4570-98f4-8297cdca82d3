
// pages-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /pages/{id}
   * 操作ID: getPage
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/pages/:id', (req, res) => {
    console.log(`[GET] /pages/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponsePagesResponse } = require('../models/responsepagesresponse');
    res.json(generateResponsePagesResponse());
  });

  /**
   * 
   * 原始路径: /pages/{id}
   * 操作ID: updatePage
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (PagesRequest)} body参数 - 必填
   */
  app.put('/pages/:id', (req, res) => {
    console.log(`[PUT] /pages/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /pages/{id}
   * 操作ID: deletePage
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/pages/:id', (req, res) => {
    console.log(`[DELETE] /pages/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /pages/batch/status
   * 操作ID: batchUpdatePageStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/pages/batch/status', (req, res) => {
    console.log(`[PUT] /pages/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /pages/batch/pages-type
   * 操作ID: batchUpdatePagesTypeId
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 pagesType - 必填
   */
  app.put('/pages/batch/pages-type', (req, res) => {
    console.log(`[PUT] /pages/batch/pages-type 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /pages/batch/manage
   * 操作ID: batchUpdateManage
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 manageId - 必填
   */
  app.put('/pages/batch/manage', (req, res) => {
    console.log(`[PUT] /pages/batch/manage 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /pages
   * 操作ID: addPage
 * @param {对象 (PagesRequest)} body参数 - 必填
   */
  app.post('/pages', (req, res) => {
    console.log(`[POST] /pages 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /pages/list
   * 操作ID: getPageList
 * @param {integer (int32)} query参数 manageId - 可选
 * @param {integer (int32)} query参数 pageType - 可选
 * @param {string} query参数 status - 可选
 * @param {string} query参数 language - 可选
 * @param {string} query参数 title - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/pages/list', (req, res) => {
    console.log(`[GET] /pages/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListPagesPageResponse } = require('../models/responsedatalistpagespageresponse');
    res.json(generateResponseDataListPagesPageResponse());
  });

  /**
   * 
   * 原始路径: /pages/batch
   * 操作ID: batchDeletePages
 * @param {array} query参数 ids - 必填
   */
  app.delete('/pages/batch', (req, res) => {
    console.log(`[DELETE] /pages/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });
};
