
// vip-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /vip/{id}
   * 操作ID: getVip
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/vip/:id', (req, res) => {
    console.log(`[GET] /vip/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseVipResponse } = require('../models/responsevipresponse');
    res.json(generateResponseVipResponse());
  });

  /**
   * 
   * 原始路径: /vip/{id}
   * 操作ID: updateVip
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (VipRequest)} body参数 - 必填
   */
  app.put('/vip/:id', (req, res) => {
    console.log(`[PUT] /vip/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /vip/{id}
   * 操作ID: deleteVip
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/vip/:id', (req, res) => {
    console.log(`[DELETE] /vip/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /vip
   * 操作ID: addVip
 * @param {对象 (VipRequest)} body参数 - 必填
   */
  app.post('/vip', (req, res) => {
    console.log(`[POST] /vip 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /vip/list
   * 操作ID: getVipList
 * @param {string} query参数 vipName - 可选
 * @param {string (byte)} query参数 level - 可选
 * @param {number} query参数 vipFrom - 可选
 * @param {number} query参数 vipTo - 可选
 * @param {string} query参数 language - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/vip/list', (req, res) => {
    console.log(`[GET] /vip/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListVipSimpleResponse } = require('../models/responsedatalistvipsimpleresponse');
    res.json(generateResponseDataListVipSimpleResponse());
  });

  /**
   * 
   * 原始路径: /vip/batch
   * 操作ID: batchDeleteVIP
 * @param {array} query参数 ids - 必填
   */
  app.delete('/vip/batch', (req, res) => {
    console.log(`[DELETE] /vip/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });
};
