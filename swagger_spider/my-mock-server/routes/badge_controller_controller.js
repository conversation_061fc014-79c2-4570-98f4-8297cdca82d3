
// badge-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /badge/{id}
   * 操作ID: getBadge
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/badge/:id', (req, res) => {
    console.log(`[GET] /badge/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseBadgeResponse } = require('../models/responsebadgeresponse');
    res.json(generateResponseBadgeResponse());
  });

  /**
   * 
   * 原始路径: /badge/{id}
   * 操作ID: updateBadge
 * @param {integer (int32)} path参数 id - 必填
 * @param {integer (int32)} query参数 badgeLangId - 必填
 * @param {对象 (BadgeRequest)} body参数 - 必填
   */
  app.put('/badge/:id', (req, res) => {
    console.log(`[PUT] /badge/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /badge/{id}
   * 操作ID: deleteBadge
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/badge/:id', (req, res) => {
    console.log(`[DELETE] /badge/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /badge/batch/type
   * 操作ID: batchUpdateType
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 badgeType - 必填
   */
  app.put('/badge/batch/type', (req, res) => {
    console.log(`[PUT] /badge/batch/type 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /badge/batch/status
   * 操作ID: batchUpdateBadgeStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/badge/batch/status', (req, res) => {
    console.log(`[PUT] /badge/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /badge/batch/sort
   * 操作ID: batchUpdateSort
 * @param {array} query参数 ids - 必填
 * @param {string (byte)} query参数 sortIndex - 必填
   */
  app.put('/badge/batch/sort', (req, res) => {
    console.log(`[PUT] /badge/batch/sort 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /badge
   * 操作ID: addBadge
 * @param {对象 (BadgeRequest)} body参数 - 必填
   */
  app.post('/badge', (req, res) => {
    console.log(`[POST] /badge 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /badge/list
   * 操作ID: getBadgeList
 * @param {string} query参数 status - 可选
 * @param {string (byte)} query参数 sortIndex - 可选
 * @param {string} query参数 badgeType - 可选
 * @param {string} query参数 language - 可选
 * @param {string} query参数 badgeName - 可选
 * @param {integer (int32)} query参数 startDateTime - 可选
 * @param {integer (int32)} query参数 endDateTime - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/badge/list', (req, res) => {
    console.log(`[GET] /badge/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListBadgeSimpleResponse } = require('../models/responsedatalistbadgesimpleresponse');
    res.json(generateResponseDataListBadgeSimpleResponse());
  });

  /**
   * 
   * 原始路径: /badge/batch
   * 操作ID: batchDeleteBadge
 * @param {array} query参数 ids - 必填
   */
  app.delete('/badge/batch', (req, res) => {
    console.log(`[DELETE] /badge/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });
};
