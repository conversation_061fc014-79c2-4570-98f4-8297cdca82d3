
// resources-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /resources/{id}
   * 操作ID: getResources
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/resources/:id', (req, res) => {
    console.log(`[GET] /resources/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseResourcesResponse } = require('../models/responseresourcesresponse');
    res.json(generateResponseResourcesResponse());
  });

  /**
   * 
   * 原始路径: /resources/{id}
   * 操作ID: updateResources
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (ResourcesRequest)} body参数 - 必填
   */
  app.put('/resources/:id', (req, res) => {
    console.log(`[PUT] /resources/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /resources/{id}
   * 操作ID: deleteResources
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/resources/:id', (req, res) => {
    console.log(`[DELETE] /resources/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /resources/batch/status
   * 操作ID: batchUpdateResourcesStatus
 * @param {array} query参数 resourcesIds - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/resources/batch/status', (req, res) => {
    console.log(`[PUT] /resources/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /resources
   * 操作ID: addResources
 * @param {对象 (ResourcesRequest)} body参数 - 必填
   */
  app.post('/resources', (req, res) => {
    console.log(`[POST] /resources 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /resources/list
   * 操作ID: listResources
 * @param {string} query参数 level - 可选
 * @param {string} query参数 status - 可选
 * @param {string} query参数 menuName - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/resources/list', (req, res) => {
    console.log(`[GET] /resources/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListResourcesSimpleResponse } = require('../models/responsedatalistresourcessimpleresponse');
    res.json(generateResponseDataListResourcesSimpleResponse());
  });

  /**
   * 
   * 原始路径: /resources/list/parent/{parentId}
   * 操作ID: getResourcesTree
 * @param {integer (int32)} path参数 parentId - 必填
   */
  app.get('/resources/list/parent/:parentId', (req, res) => {
    console.log(`[GET] /resources/list/parent/{parentId} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseListResourcesTreeResponse } = require('../models/responselistresourcestreeresponse');
    res.json(generateResponseListResourcesTreeResponse());
  });

  /**
   * 
   * 原始路径: /resources/list/all
   * 操作ID: getAllResources

   */
  app.get('/resources/list/all', (req, res) => {
    console.log(`[GET] /resources/list/all 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseListNameResponse } = require('../models/responselistnameresponse');
    res.json(generateResponseListNameResponse());
  });

  /**
   * 
   * 原始路径: /resources/batch
   * 操作ID: batchDeleteResources
 * @param {array} query参数 resourcesIds - 必填
   */
  app.delete('/resources/batch', (req, res) => {
    console.log(`[DELETE] /resources/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });
};
