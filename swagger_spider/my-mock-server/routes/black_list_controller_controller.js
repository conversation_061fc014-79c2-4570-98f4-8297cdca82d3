
// black-list-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /blacklist/{id}
   * 操作ID: getBlackList
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/blacklist/:id', (req, res) => {
    console.log(`[GET] /blacklist/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseBlackListResponse } = require('../models/responseblacklistresponse');
    res.json(generateResponseBlackListResponse());
  });

  /**
   * 
   * 原始路径: /blacklist/{id}
   * 操作ID: updateBlackList
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (BlackListRequest)} body参数 - 必填
   */
  app.put('/blacklist/:id', (req, res) => {
    console.log(`[PUT] /blacklist/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /blacklist/{id}
   * 操作ID: deleteBlackList
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/blacklist/:id', (req, res) => {
    console.log(`[DELETE] /blacklist/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /blacklist
   * 操作ID: addBlackList
 * @param {对象 (BlackListRequest)} body参数 - 必填
   */
  app.post('/blacklist', (req, res) => {
    console.log(`[POST] /blacklist 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /blacklist/list
   * 操作ID: listBlackList
 * @param {integer (int32)} query参数 userId - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/blacklist/list', (req, res) => {
    console.log(`[GET] /blacklist/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListBlackListResponse } = require('../models/responsedatalistblacklistresponse');
    res.json(generateResponseDataListBlackListResponse());
  });

  /**
   * 
   * 原始路径: /blacklist/batch
   * 操作ID: batchDeleteBlackListEntries
 * @param {array} query参数 ids - 必填
   */
  app.delete('/blacklist/batch', (req, res) => {
    console.log(`[DELETE] /blacklist/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
