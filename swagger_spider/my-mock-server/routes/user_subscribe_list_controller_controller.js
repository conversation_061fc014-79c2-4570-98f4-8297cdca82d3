
// user-subscribe-list-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /user/subscribes/{id}
   * 操作ID: getUserSubscribeList
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/user/subscribes/:id', (req, res) => {
    console.log(`[GET] /user/subscribes/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseUserSubscribeListResponse } = require('../models/responseusersubscribelistresponse');
    res.json(generateResponseUserSubscribeListResponse());
  });

  /**
   * 
   * 原始路径: /user/subscribes/{id}
   * 操作ID: updateUserSubscribeList
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (UserSubscribeListRequest)} body参数 - 必填
   */
  app.put('/user/subscribes/:id', (req, res) => {
    console.log(`[PUT] /user/subscribes/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /user/subscribes/{id}
   * 操作ID: deleteUserSubscribeList
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/user/subscribes/:id', (req, res) => {
    console.log(`[DELETE] /user/subscribes/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /user/subscribes
   * 操作ID: addUserSubscribeList
 * @param {对象 (UserSubscribeListRequest)} body参数 - 必填
   */
  app.post('/user/subscribes', (req, res) => {
    console.log(`[POST] /user/subscribes 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /user/subscribes/list
   * 操作ID: listUserSubscribeList
 * @param {integer (int32)} query参数 userId - 可选
 * @param {integer (int32)} query参数 storeId - 可选
 * @param {integer (int64)} query参数 itemId - 可选
 * @param {integer (int32)} query参数 noticeStockNum - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/user/subscribes/list', (req, res) => {
    console.log(`[GET] /user/subscribes/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListUserSubscribeListResponse } = require('../models/responsedatalistusersubscribelistresponse');
    res.json(generateResponseDataListUserSubscribeListResponse());
  });

  /**
   * 
   * 原始路径: /user/subscribes/batch
   * 操作ID: batchDeleteUserSubscribeLists
 * @param {array} query参数 ids - 必填
   */
  app.delete('/user/subscribes/batch', (req, res) => {
    console.log(`[DELETE] /user/subscribes/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
