
// campaign-stat-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /campaign/stat/{id}
   * 操作ID: getCampaignStat
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/campaign/stat/:id', (req, res) => {
    console.log(`[GET] /campaign/stat/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseCampaignStatResponse } = require('../models/responsecampaignstatresponse');
    res.json(generateResponseCampaignStatResponse());
  });

  /**
   * 
   * 原始路径: /campaign/stat/{id}
   * 操作ID: deleteCampaignStat
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/campaign/stat/:id', (req, res) => {
    console.log(`[DELETE] /campaign/stat/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /campaign/stat/list
   * 操作ID: getCampaignStatList
 * @param {integer (int64)} query参数 campaignId - 可选
 * @param {integer (int32)} query参数 updateTimeStart - 可选
 * @param {integer (int32)} query参数 updateTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/campaign/stat/list', (req, res) => {
    console.log(`[GET] /campaign/stat/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListCampaignStatSimpleResponse } = require('../models/responsedatalistcampaignstatsimpleresponse');
    res.json(generateResponseDataListCampaignStatSimpleResponse());
  });

  /**
   * 
   * 原始路径: /campaign/stat/batch
   * 操作ID: batchDeleteCampaignStat
 * @param {array} query参数 ids - 必填
   */
  app.delete('/campaign/stat/batch', (req, res) => {
    console.log(`[DELETE] /campaign/stat/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });
};
