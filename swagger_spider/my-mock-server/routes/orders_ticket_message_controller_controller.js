
// orders-ticket-message-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /orders/ticket/message/{id}
   * 操作ID: getOrderTicketMessage
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/orders/ticket/message/:id', (req, res) => {
    console.log(`[GET] /orders/ticket/message/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseOrdersTicketMessageResponse } = require('../models/responseordersticketmessageresponse');
    res.json(generateResponseOrdersTicketMessageResponse());
  });

  /**
   * 
   * 原始路径: /orders/ticket/message/{id}
   * 操作ID: updateOrderTicketMessage
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (OrdersTicketMessageRequest)} body参数 - 必填
   */
  app.put('/orders/ticket/message/:id', (req, res) => {
    console.log(`[PUT] /orders/ticket/message/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /orders/ticket/message/{id}
   * 操作ID: deleteOrderTicketMessage
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/orders/ticket/message/:id', (req, res) => {
    console.log(`[DELETE] /orders/ticket/message/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /orders/ticket/message
   * 操作ID: addOrderTicketMessage
 * @param {对象 (OrdersTicketMessageRequest)} body参数 - 必填
   */
  app.post('/orders/ticket/message', (req, res) => {
    console.log(`[POST] /orders/ticket/message 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /orders/ticket/message/list
   * 操作ID: listOrdersTicketMessage
 * @param {integer (int64)} query参数 ordersTicketId - 可选
 * @param {integer (int32)} query参数 userId - 可选
 * @param {integer (int32)} query参数 storeId - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/orders/ticket/message/list', (req, res) => {
    console.log(`[GET] /orders/ticket/message/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListOrdersTicketMessageResponse } = require('../models/responsedatalistordersticketmessageresponse');
    res.json(generateResponseDataListOrdersTicketMessageResponse());
  });

  /**
   * 
   * 原始路径: /orders/ticket/message/batch
   * 操作ID: batchDeleteOrderTicketMessage
 * @param {array} query参数 ids - 必填
   */
  app.delete('/orders/ticket/message/batch', (req, res) => {
    console.log(`[DELETE] /orders/ticket/message/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
