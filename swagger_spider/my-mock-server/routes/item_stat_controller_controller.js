
// item-stat-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /item/stat/{id}
   * 操作ID: getItemStat
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/item/stat/:id', (req, res) => {
    console.log(`[GET] /item/stat/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseItemStatResponse } = require('../models/responseitemstatresponse');
    res.json(generateResponseItemStatResponse());
  });

  /**
   * 
   * 原始路径: /item/stat/{id}
   * 操作ID: updateItemStat
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (ItemStatRequest)} body参数 - 必填
   */
  app.put('/item/stat/:id', (req, res) => {
    console.log(`[PUT] /item/stat/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /item/stat/{id}
   * 操作ID: deleteItemStat
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/item/stat/:id', (req, res) => {
    console.log(`[DELETE] /item/stat/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /item/stat/batch/store
   * 操作ID: batchUpdateItemStatStore
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 storeId - 必填
   */
  app.put('/item/stat/batch/store', (req, res) => {
    console.log(`[PUT] /item/stat/batch/store 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /item/stat/batch/item
   * 操作ID: batchUpdateItemStatItem
 * @param {array} query参数 ids - 必填
 * @param {integer (int64)} query参数 itemId - 必填
   */
  app.put('/item/stat/batch/item', (req, res) => {
    console.log(`[PUT] /item/stat/batch/item 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /item/stat
   * 操作ID: addItemStat
 * @param {对象 (ItemStatRequest)} body参数 - 必填
   */
  app.post('/item/stat', (req, res) => {
    console.log(`[POST] /item/stat 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /item/stat/list
   * 操作ID: listItemStat
 * @param {integer (int32)} query参数 storeId - 可选
 * @param {integer (int64)} query参数 itemId - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/item/stat/list', (req, res) => {
    console.log(`[GET] /item/stat/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListItemStatSimpleResponse } = require('../models/responsedatalistitemstatsimpleresponse');
    res.json(generateResponseDataListItemStatSimpleResponse());
  });

  /**
   * 
   * 原始路径: /item/stat/batch
   * 操作ID: batchDeleteItemStat
 * @param {array} query参数 ids - 必填
   */
  app.delete('/item/stat/batch', (req, res) => {
    console.log(`[DELETE] /item/stat/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
