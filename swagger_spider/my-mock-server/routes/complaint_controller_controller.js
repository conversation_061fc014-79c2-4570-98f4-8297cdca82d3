
// complaint-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /complaints/{id}
   * 操作ID: getComplaint
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/complaints/:id', (req, res) => {
    console.log(`[GET] /complaints/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseComplaintResponse } = require('../models/responsecomplaintresponse');
    res.json(generateResponseComplaintResponse());
  });

  /**
   * 
   * 原始路径: /complaints/{id}
   * 操作ID: updateComplaint
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (ComplaintRequest)} body参数 - 必填
   */
  app.put('/complaints/:id', (req, res) => {
    console.log(`[PUT] /complaints/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /complaints/{id}
   * 操作ID: deleteComplaint
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/complaints/:id', (req, res) => {
    console.log(`[DELETE] /complaints/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /complaints/batch/status
   * 操作ID: batchUpdateComplaintStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/complaints/batch/status', (req, res) => {
    console.log(`[PUT] /complaints/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /complaints
   * 操作ID: addComplaint
 * @param {对象 (ComplaintRequest)} body参数 - 必填
   */
  app.post('/complaints', (req, res) => {
    console.log(`[POST] /complaints 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /complaints/list
   * 操作ID: listComplaint
 * @param {integer (int32)} query参数 userId - 可选
 * @param {integer (int32)} query参数 storeId - 可选
 * @param {integer (int64)} query参数 itemId - 可选
 * @param {string} query参数 status - 可选
 * @param {string} query参数 complaintContent - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/complaints/list', (req, res) => {
    console.log(`[GET] /complaints/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListComplaintSimpleResponse } = require('../models/responsedatalistcomplaintsimpleresponse');
    res.json(generateResponseDataListComplaintSimpleResponse());
  });

  /**
   * 
   * 原始路径: /complaints/batch
   * 操作ID: batchDeleteComplaints
 * @param {array} query参数 ids - 必填
   */
  app.delete('/complaints/batch', (req, res) => {
    console.log(`[DELETE] /complaints/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
