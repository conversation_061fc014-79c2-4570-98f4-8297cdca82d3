
// services-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /services/{id}
   * 操作ID: getService
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/services/:id', (req, res) => {
    console.log(`[GET] /services/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseServicesResponse } = require('../models/responseservicesresponse');
    res.json(generateResponseServicesResponse());
  });

  /**
   * 
   * 原始路径: /services/{id}
   * 操作ID: updateService
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (ServicesRequest)} body参数 - 必填
   */
  app.put('/services/:id', (req, res) => {
    console.log(`[PUT] /services/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /services/{id}
   * 操作ID: deleteService
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/services/:id', (req, res) => {
    console.log(`[DELETE] /services/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /services/batch/status
   * 操作ID: batchUpdateServicesStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/services/batch/status', (req, res) => {
    console.log(`[PUT] /services/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /services/batch/parent
   * 操作ID: batchUpdateServicesParentId
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 parentId - 必填
   */
  app.put('/services/batch/parent', (req, res) => {
    console.log(`[PUT] /services/batch/parent 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /services
   * 操作ID: addService
 * @param {对象 (ServicesRequest)} body参数 - 必填
   */
  app.post('/services', (req, res) => {
    console.log(`[POST] /services 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /services/list
   * 操作ID: getServiceList
 * @param {string} query参数 servicesName - 可选
 * @param {string} query参数 status - 可选
 * @param {integer (int32)} query参数 parentId - 可选
 * @param {string} query参数 language - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/services/list', (req, res) => {
    console.log(`[GET] /services/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListServicesSimpleResponse } = require('../models/responsedatalistservicessimpleresponse');
    res.json(generateResponseDataListServicesSimpleResponse());
  });

  /**
   * 
   * 原始路径: /services/batch
   * 操作ID: batchDeleteService
 * @param {array} query参数 ids - 必填
   */
  app.delete('/services/batch', (req, res) => {
    console.log(`[DELETE] /services/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });
};
