
// payment-dispute-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /payment/dispute/{id}
   * 操作ID: getPaymentDispute
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/payment/dispute/:id', (req, res) => {
    console.log(`[GET] /payment/dispute/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponsePaymentDisputeResponse } = require('../models/responsepaymentdisputeresponse');
    res.json(generateResponsePaymentDisputeResponse());
  });

  /**
   * 
   * 原始路径: /payment/dispute/{id}
   * 操作ID: updatePaymentDispute
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (PaymentDisputeRequest)} body参数 - 必填
   */
  app.put('/payment/dispute/:id', (req, res) => {
    console.log(`[PUT] /payment/dispute/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /payment/dispute/{id}
   * 操作ID: deletePaymentDispute
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/payment/dispute/:id', (req, res) => {
    console.log(`[DELETE] /payment/dispute/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /payment/dispute/batch/user
   * 操作ID: batchUpdatePaymentDisputeUser
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 userId - 必填
   */
  app.put('/payment/dispute/batch/user', (req, res) => {
    console.log(`[PUT] /payment/dispute/batch/user 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /payment/dispute/batch/status
   * 操作ID: batchUpdatePaymentDisputeStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/payment/dispute/batch/status', (req, res) => {
    console.log(`[PUT] /payment/dispute/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /payment/dispute/batch/payment
   * 操作ID: batchUpdatePaymentDisputePayment
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 paymentId - 必填
   */
  app.put('/payment/dispute/batch/payment', (req, res) => {
    console.log(`[PUT] /payment/dispute/batch/payment 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /payment/dispute/batch/payment/record
   * 操作ID: batchUpdatePaymentDisputePaymentRecord
 * @param {array} query参数 ids - 必填
 * @param {integer (int64)} query参数 paymentRecordId - 必填
   */
  app.put('/payment/dispute/batch/payment/record', (req, res) => {
    console.log(`[PUT] /payment/dispute/batch/payment/record 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /payment/dispute/batch/manage
   * 操作ID: batchUpdatePaymentDisputeManage
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 manageId - 必填
   */
  app.put('/payment/dispute/batch/manage', (req, res) => {
    console.log(`[PUT] /payment/dispute/batch/manage 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /payment/dispute
   * 操作ID: addPaymentDispute
 * @param {对象 (PaymentDisputeRequest)} body参数 - 必填
   */
  app.post('/payment/dispute', (req, res) => {
    console.log(`[POST] /payment/dispute 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /payment/dispute/list
   * 操作ID: listPaymentDispute
 * @param {integer (int32)} query参数 userId - 可选
 * @param {integer (int64)} query参数 paymentRecordId - 可选
 * @param {integer (int32)} query参数 paymentId - 可选
 * @param {string} query参数 status - 可选
 * @param {integer (int32)} query参数 manageId - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/payment/dispute/list', (req, res) => {
    console.log(`[GET] /payment/dispute/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListPaymentDisputeResponse } = require('../models/responsedatalistpaymentdisputeresponse');
    res.json(generateResponseDataListPaymentDisputeResponse());
  });

  /**
   * 
   * 原始路径: /payment/dispute/batch
   * 操作ID: batchDeletePaymentDispute
 * @param {array} query参数 ids - 必填
   */
  app.delete('/payment/dispute/batch', (req, res) => {
    console.log(`[DELETE] /payment/dispute/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
