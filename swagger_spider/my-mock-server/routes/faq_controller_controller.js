
// faq-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /faq/{id}
   * 操作ID: getFaq
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/faq/:id', (req, res) => {
    console.log(`[GET] /faq/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseFaqResponse } = require('../models/responsefaqresponse');
    res.json(generateResponseFaqResponse());
  });

  /**
   * 
   * 原始路径: /faq/{id}
   * 操作ID: updateFaq
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (FaqRequest)} body参数 - 必填
   */
  app.put('/faq/:id', (req, res) => {
    console.log(`[PUT] /faq/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /faq/{id}
   * 操作ID: deleteFaq
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/faq/:id', (req, res) => {
    console.log(`[DELETE] /faq/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /faq/batch/status
   * 操作ID: batchUpdateFaqStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/faq/batch/status', (req, res) => {
    console.log(`[PUT] /faq/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /faq/batch/manage
   * 操作ID: batchUpdateFaqManageId
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 manageId - 必填
   */
  app.put('/faq/batch/manage', (req, res) => {
    console.log(`[PUT] /faq/batch/manage 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /faq
   * 操作ID: addFaq
 * @param {对象 (FaqRequest)} body参数 - 必填
   */
  app.post('/faq', (req, res) => {
    console.log(`[POST] /faq 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /faq/list
   * 操作ID: getFaqList
 * @param {integer (int32)} query参数 manageId - 可选
 * @param {string} query参数 language - 可选
 * @param {string} query参数 title - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/faq/list', (req, res) => {
    console.log(`[GET] /faq/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListFaqSimpleResponse } = require('../models/responsedatalistfaqsimpleresponse');
    res.json(generateResponseDataListFaqSimpleResponse());
  });

  /**
   * 
   * 原始路径: /faq/batch
   * 操作ID: batchDeleteFaqs
 * @param {array} query参数 ids - 必填
   */
  app.delete('/faq/batch', (req, res) => {
    console.log(`[DELETE] /faq/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });
};
