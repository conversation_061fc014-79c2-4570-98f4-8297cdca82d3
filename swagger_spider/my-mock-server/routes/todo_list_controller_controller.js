
// todo-list-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /todolist/{id}
   * 操作ID: getTodoList
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/todolist/:id', (req, res) => {
    console.log(`[GET] /todolist/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseTodoListResponse } = require('../models/responsetodolistresponse');
    res.json(generateResponseTodoListResponse());
  });

  /**
   * 
   * 原始路径: /todolist/{id}
   * 操作ID: updateTodoList
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (TodoListRequest)} body参数 - 必填
   */
  app.put('/todolist/:id', (req, res) => {
    console.log(`[PUT] /todolist/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /todolist/{id}
   * 操作ID: deleteTodoList
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/todolist/:id', (req, res) => {
    console.log(`[DELETE] /todolist/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /todolist
   * 操作ID: addTodoList
 * @param {对象 (TodoListRequest)} body参数 - 必填
   */
  app.post('/todolist', (req, res) => {
    console.log(`[POST] /todolist 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /todolist/list
   * 操作ID: listTodoList
 * @param {string} query参数 taskType - 可选
   */
  app.get('/todolist/list', (req, res) => {
    console.log(`[GET] /todolist/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseListTodoListSimpleResponse } = require('../models/responselisttodolistsimpleresponse');
    res.json(generateResponseListTodoListSimpleResponse());
  });

  /**
   * 
   * 原始路径: /todolist/finance/list/all
   * 操作ID: listFinanceTodoList

   */
  app.get('/todolist/finance/list/all', (req, res) => {
    console.log(`[GET] /todolist/finance/list/all 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseListTodoListSimpleResponse } = require('../models/responselisttodolistsimpleresponse');
    res.json(generateResponseListTodoListSimpleResponse());
  });

  /**
   * 
   * 原始路径: /todolist/finance/count
   * 操作ID: getFinanceTodoListCount

   */
  app.get('/todolist/finance/count', (req, res) => {
    console.log(`[GET] /todolist/finance/count 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseFinanceTodoListCountResponse } = require('../models/responsefinancetodolistcountresponse');
    res.json(generateResponseFinanceTodoListCountResponse());
  });

  /**
   * 
   * 原始路径: /todolist/customer/list/all
   * 操作ID: listCustomerTodoList

   */
  app.get('/todolist/customer/list/all', (req, res) => {
    console.log(`[GET] /todolist/customer/list/all 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseListTodoListSimpleResponse } = require('../models/responselisttodolistsimpleresponse');
    res.json(generateResponseListTodoListSimpleResponse());
  });

  /**
   * 
   * 原始路径: /todolist/customer/count
   * 操作ID: getCustomerTodoListCount

   */
  app.get('/todolist/customer/count', (req, res) => {
    console.log(`[GET] /todolist/customer/count 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseCustomerTodoListCountResponse } = require('../models/responsecustomertodolistcountresponse');
    res.json(generateResponseCustomerTodoListCountResponse());
  });
};
