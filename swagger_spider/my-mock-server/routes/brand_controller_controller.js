
// brand-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /brand/{id}
   * 操作ID: getBrand
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/brand/:id', (req, res) => {
    console.log(`[GET] /brand/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseBrandResponse } = require('../models/responsebrandresponse');
    res.json(generateResponseBrandResponse());
  });

  /**
   * 
   * 原始路径: /brand/{id}
   * 操作ID: updateBrand
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (BrandRequest)} body参数 - 必填
   */
  app.put('/brand/:id', (req, res) => {
    console.log(`[PUT] /brand/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /brand/{id}
   * 操作ID: deleteBrand
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/brand/:id', (req, res) => {
    console.log(`[DELETE] /brand/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /brand/batch/status
   * 操作ID: batchUpdateBrandStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/brand/batch/status', (req, res) => {
    console.log(`[PUT] /brand/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /brand/batch/parent
   * 操作ID: batchUpdateBrandParentId
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 parentId - 必填
   */
  app.put('/brand/batch/parent', (req, res) => {
    console.log(`[PUT] /brand/batch/parent 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /brand
   * 操作ID: addBrand
 * @param {对象 (BrandRequest)} body参数 - 必填
   */
  app.post('/brand', (req, res) => {
    console.log(`[POST] /brand 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /brand/names
   * 操作ID: getAllBrand

   */
  app.get('/brand/names', (req, res) => {
    console.log(`[GET] /brand/names 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseListNameResponse } = require('../models/responselistnameresponse');
    res.json(generateResponseListNameResponse());
  });

  /**
   * 
   * 原始路径: /brand/list
   * 操作ID: getBrandList
 * @param {string} query参数 status - 可选
 * @param {integer (int32)} query参数 parentId - 可选
 * @param {string} query参数 language - 可选
 * @param {integer (int32)} query参数 servicesId - 可选
 * @param {string} query参数 name - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/brand/list', (req, res) => {
    console.log(`[GET] /brand/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListBrandSimpleResponse } = require('../models/responsedatalistbrandsimpleresponse');
    res.json(generateResponseDataListBrandSimpleResponse());
  });

  /**
   * 
   * 原始路径: /brand/batch
   * 操作ID: batchDeleteBrand
 * @param {array} query参数 ids - 必填
   */
  app.delete('/brand/batch', (req, res) => {
    console.log(`[DELETE] /brand/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });
};
