
// campaign-tag-list-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /campaign/tags/{id}
   * 操作ID: getCampaignTagList
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/campaign/tags/:id', (req, res) => {
    console.log(`[GET] /campaign/tags/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseCampaignTagListResponse } = require('../models/responsecampaigntaglistresponse');
    res.json(generateResponseCampaignTagListResponse());
  });

  /**
   * 
   * 原始路径: /campaign/tags/{id}
   * 操作ID: updateCampaignTagList
 * @param {integer (int64)} path参数 id - 必填
 * @param {array} query参数 tagIds - 必填
   */
  app.put('/campaign/tags/:id', (req, res) => {
    console.log(`[PUT] /campaign/tags/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /campaign/tags/{id}
   * 操作ID: deleteCampaignTagList
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/campaign/tags/:id', (req, res) => {
    console.log(`[DELETE] /campaign/tags/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /campaign/tags
   * 操作ID: addCampaignTagList
 * @param {对象 (CampaignTagListRequest)} body参数 - 必填
   */
  app.post('/campaign/tags', (req, res) => {
    console.log(`[POST] /campaign/tags 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /campaign/tags/list
   * 操作ID: getCampaignTagListList
 * @param {integer (int64)} query参数 campaignId - 可选
 * @param {integer (int32)} query参数 tagId - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/campaign/tags/list', (req, res) => {
    console.log(`[GET] /campaign/tags/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListCampaignTagListSimpleResponse } = require('../models/responsedatalistcampaigntaglistsimpleresponse');
    res.json(generateResponseDataListCampaignTagListSimpleResponse());
  });

  /**
   * 
   * 原始路径: /campaign/tags/batch
   * 操作ID: batchDeleteCampaignTagList
 * @param {array} query参数 ids - 必填
   */
  app.delete('/campaign/tags/batch', (req, res) => {
    console.log(`[DELETE] /campaign/tags/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });
};
