
// whitelist-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /whitelist/{id}
   * 操作ID: getWhitelist
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/whitelist/:id', (req, res) => {
    console.log(`[GET] /whitelist/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseWhitelistResponse } = require('../models/responsewhitelistresponse');
    res.json(generateResponseWhitelistResponse());
  });

  /**
   * 
   * 原始路径: /whitelist/{id}
   * 操作ID: updateWhitelist
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (WhitelistRequest)} body参数 - 必填
   */
  app.put('/whitelist/:id', (req, res) => {
    console.log(`[PUT] /whitelist/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /whitelist/{id}
   * 操作ID: deleteWhitelist
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/whitelist/:id', (req, res) => {
    console.log(`[DELETE] /whitelist/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /whitelist/batch/type
   * 操作ID: batchUpdateWhitelistType
 * @param {string (byte)} query参数 type - 必填
 * @param {array} query参数 ids - 必填
   */
  app.put('/whitelist/batch/type', (req, res) => {
    console.log(`[PUT] /whitelist/batch/type 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /whitelist/batch/status
   * 操作ID: batchUpdateWhitelistStatus
 * @param {string} query参数 status - 必填
 * @param {array} query参数 ids - 必填
   */
  app.put('/whitelist/batch/status', (req, res) => {
    console.log(`[PUT] /whitelist/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /whitelist
   * 操作ID: addWhitelist
 * @param {对象 (WhitelistRequest)} body参数 - 必填
   */
  app.post('/whitelist', (req, res) => {
    console.log(`[POST] /whitelist 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /whitelist/batch
   * 操作ID: batchAddWhitelist
 * @param {对象 (BatchWhitelistRequest)} body参数 - 必填
   */
  app.post('/whitelist/batch', (req, res) => {
    console.log(`[POST] /whitelist/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /whitelist/batch
   * 操作ID: batchDeleteWhitelist
 * @param {array} query参数 ids - 必填
   */
  app.delete('/whitelist/batch', (req, res) => {
    console.log(`[DELETE] /whitelist/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /whitelist/list
   * 操作ID: listWhitelist
 * @param {string} query参数 status - 可选
 * @param {string} query参数 language - 可选
 * @param {string} query参数 itemType - 可选
 * @param {string} query参数 itemValue - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/whitelist/list', (req, res) => {
    console.log(`[GET] /whitelist/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListWhitelistResponse } = require('../models/responsedatalistwhitelistresponse');
    res.json(generateResponseDataListWhitelistResponse());
  });
};
