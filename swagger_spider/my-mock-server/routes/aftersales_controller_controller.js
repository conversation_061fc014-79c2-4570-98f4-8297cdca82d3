
// aftersales-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /aftersales/{id}
   * 操作ID: getAftersales
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/aftersales/:id', (req, res) => {
    console.log(`[GET] /aftersales/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseAftersalesResponse } = require('../models/responseaftersalesresponse');
    res.json(generateResponseAftersalesResponse());
  });

  /**
   * 
   * 原始路径: /aftersales/{id}
   * 操作ID: updateAftersales
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (AftersalesRequest)} body参数 - 必填
   */
  app.put('/aftersales/:id', (req, res) => {
    console.log(`[PUT] /aftersales/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /aftersales/{id}
   * 操作ID: deleteAftersales
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/aftersales/:id', (req, res) => {
    console.log(`[DELETE] /aftersales/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /aftersales/batch/status
   * 操作ID: batchUpdateAftersalesStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/aftersales/batch/status', (req, res) => {
    console.log(`[PUT] /aftersales/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /aftersales
   * 操作ID: addAftersales
 * @param {对象 (AftersalesRequest)} body参数 - 必填
   */
  app.post('/aftersales', (req, res) => {
    console.log(`[POST] /aftersales 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /aftersales/list
   * 操作ID: getAftersalesList
 * @param {integer (int32)} query参数 brandId - 可选
 * @param {integer (int32)} query参数 aftersalesTypeId - 可选
 * @param {string} query参数 status - 可选
 * @param {string} query参数 language - 可选
 * @param {string} query参数 reason - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/aftersales/list', (req, res) => {
    console.log(`[GET] /aftersales/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListAftersalesResponse } = require('../models/responsedatalistaftersalesresponse');
    res.json(generateResponseDataListAftersalesResponse());
  });

  /**
   * 
   * 原始路径: /aftersales/batch
   * 操作ID: batchDeleteAftersales
 * @param {array} query参数 ids - 必填
   */
  app.delete('/aftersales/batch', (req, res) => {
    console.log(`[DELETE] /aftersales/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });
};
