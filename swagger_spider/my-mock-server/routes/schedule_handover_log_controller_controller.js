
// schedule-handover-log-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /schedule/handover/log/{id}
   * 操作ID: getScheduleHandoverLog
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/schedule/handover/log/:id', (req, res) => {
    console.log(`[GET] /schedule/handover/log/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseScheduleHandoverLogResponse } = require('../models/responseschedulehandoverlogresponse');
    res.json(generateResponseScheduleHandoverLogResponse());
  });

  /**
   * 
   * 原始路径: /schedule/handover/log/{id}
   * 操作ID: updateScheduleHandoverLog
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (ScheduleHandoverLogRequest)} body参数 - 必填
   */
  app.put('/schedule/handover/log/:id', (req, res) => {
    console.log(`[PUT] /schedule/handover/log/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /schedule/handover/log/{id}
   * 操作ID: deleteScheduleHandoverLog
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/schedule/handover/log/:id', (req, res) => {
    console.log(`[DELETE] /schedule/handover/log/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /schedule/handover/log
   * 操作ID: addScheduleHandoverLog
 * @param {对象 (ScheduleHandoverLogRequest)} body参数 - 必填
   */
  app.post('/schedule/handover/log', (req, res) => {
    console.log(`[POST] /schedule/handover/log 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /schedule/handover/log/list
   * 操作ID: listScheduleHandoverLog
 * @param {integer (int32)} query参数 scheduleHandoverId - 必填
   */
  app.get('/schedule/handover/log/list', (req, res) => {
    console.log(`[GET] /schedule/handover/log/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseListScheduleHandoverLogSimpleResponse } = require('../models/responselistschedulehandoverlogsimpleresponse');
    res.json(generateResponseListScheduleHandoverLogSimpleResponse());
  });
};
