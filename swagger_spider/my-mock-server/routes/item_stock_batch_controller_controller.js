
// item-stock-batch-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /item/stock/batch/{id}
   * 操作ID: getItemStockBatch
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/item/stock/batch/:id', (req, res) => {
    console.log(`[GET] /item/stock/batch/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseItemStockBatchResponse } = require('../models/responseitemstockbatchresponse');
    res.json(generateResponseItemStockBatchResponse());
  });

  /**
   * 
   * 原始路径: /item/stock/batch/{id}
   * 操作ID: updateItemStockBatch
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (ItemStockBatchRequest)} body参数 - 必填
   */
  app.put('/item/stock/batch/:id', (req, res) => {
    console.log(`[PUT] /item/stock/batch/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /item/stock/batch/{id}
   * 操作ID: deleteItemStockBatch
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/item/stock/batch/:id', (req, res) => {
    console.log(`[DELETE] /item/stock/batch/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /item/stock/batch
   * 操作ID: addItemStockBatch
 * @param {对象 (ItemStockBatchRequest)} body参数 - 必填
   */
  app.post('/item/stock/batch', (req, res) => {
    console.log(`[POST] /item/stock/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /item/stock/batch/list
   * 操作ID: listItemStockBatch
 * @param {integer (int32)} query参数 storeId - 可选
 * @param {integer (int64)} query参数 itemId - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/item/stock/batch/list', (req, res) => {
    console.log(`[GET] /item/stock/batch/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListItemStockBatchSimpleResponse } = require('../models/responsedatalistitemstockbatchsimpleresponse');
    res.json(generateResponseDataListItemStockBatchSimpleResponse());
  });

  /**
   * 
   * 原始路径: /item/stock/batch/batch
   * 操作ID: batchDeleteItemStockBatch
 * @param {array} query参数 ids - 必填
   */
  app.delete('/item/stock/batch/batch', (req, res) => {
    console.log(`[DELETE] /item/stock/batch/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
