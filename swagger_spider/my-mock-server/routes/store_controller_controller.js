
// store-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /store/{id}
   * 操作ID: getStore
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/store/:id', (req, res) => {
    console.log(`[GET] /store/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseStoreResponse } = require('../models/responsestoreresponse');
    res.json(generateResponseStoreResponse());
  });

  /**
   * 
   * 原始路径: /store/{id}
   * 操作ID: updateStore
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (StoreRequest)} body参数 - 必填
   */
  app.put('/store/:id', (req, res) => {
    console.log(`[PUT] /store/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /store/{id}
   * 操作ID: deleteStore
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/store/:id', (req, res) => {
    console.log(`[DELETE] /store/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /store/batch/user
   * 操作ID: batchUpdateUser
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 userId - 必填
   */
  app.put('/store/batch/user', (req, res) => {
    console.log(`[PUT] /store/batch/user 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /store/batch/status
   * 操作ID: batchUpdateStoreStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/store/batch/status', (req, res) => {
    console.log(`[PUT] /store/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /store/batch/country
   * 操作ID: batchUpdateStoreCountry
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 country - 必填
   */
  app.put('/store/batch/country', (req, res) => {
    console.log(`[PUT] /store/batch/country 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /store
   * 操作ID: addStore
 * @param {对象 (StoreRequest)} body参数 - 必填
   */
  app.post('/store', (req, res) => {
    console.log(`[POST] /store 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /store/list
   * 操作ID: listStore
 * @param {string} query参数 status - 可选
 * @param {integer (int32)} query参数 userId - 可选
 * @param {string} query参数 country - 可选
 * @param {string} query参数 language - 可选
 * @param {string} query参数 name - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.post('/store/list', (req, res) => {
    console.log(`[POST] /store/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListStoreSimpleResponse } = require('../models/responsedataliststoresimpleresponse');
    res.json(generateResponseDataListStoreSimpleResponse());
  });

  /**
   * 
   * 原始路径: /store/batch
   * 操作ID: batchDeleteStore
 * @param {array} query参数 ids - 必填
   */
  app.delete('/store/batch', (req, res) => {
    console.log(`[DELETE] /store/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
