
// store-withdraw-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /store/withdrawals/{id}
   * 操作ID: getStoreWithdraw
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/store/withdrawals/:id', (req, res) => {
    console.log(`[GET] /store/withdrawals/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseStoreWithdrawResponse } = require('../models/responsestorewithdrawresponse');
    res.json(generateResponseStoreWithdrawResponse());
  });

  /**
   * 
   * 原始路径: /store/withdrawals/{id}
   * 操作ID: updateStoreWithdraw
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (StoreWithdrawRequest)} body参数 - 必填
   */
  app.put('/store/withdrawals/:id', (req, res) => {
    console.log(`[PUT] /store/withdrawals/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /store/withdrawals/{id}
   * 操作ID: deleteStoreWithdraw
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/store/withdrawals/:id', (req, res) => {
    console.log(`[DELETE] /store/withdrawals/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /store/withdrawals/{id}/approve
   * 操作ID: approveStoreWithdraw
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (StoreWithdrawApproveRequest)} body参数 - 必填
   */
  app.put('/store/withdrawals/:id/approve', (req, res) => {
    console.log(`[PUT] /store/withdrawals/{id}/approve 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseBoolean } = require('../models/responseboolean');
    res.json(generateResponseBoolean());
  });

  /**
   * 
   * 原始路径: /store/withdrawals/batch/wallet
   * 操作ID: batchUpdateStoreWithdrawWallet
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 walletId - 必填
   */
  app.put('/store/withdrawals/batch/wallet', (req, res) => {
    console.log(`[PUT] /store/withdrawals/batch/wallet 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /store/withdrawals/batch/store
   * 操作ID: batchUpdateStoreWithdrawStore
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 storeId - 必填
   */
  app.put('/store/withdrawals/batch/store', (req, res) => {
    console.log(`[PUT] /store/withdrawals/batch/store 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /store/withdrawals/batch/status
   * 操作ID: batchUpdateStoreWithdrawStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/store/withdrawals/batch/status', (req, res) => {
    console.log(`[PUT] /store/withdrawals/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /store/withdrawals
   * 操作ID: addStoreWithdraw
 * @param {对象 (StoreWithdrawRequest)} body参数 - 必填
   */
  app.post('/store/withdrawals', (req, res) => {
    console.log(`[POST] /store/withdrawals 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /store/withdrawals/list
   * 操作ID: listStoreWithdraw
 * @param {integer (int32)} query参数 storeId - 可选
 * @param {integer (int32)} query参数 userId - 可选
 * @param {integer (int32)} query参数 userWalletId - 可选
 * @param {string} query参数 status - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/store/withdrawals/list', (req, res) => {
    console.log(`[GET] /store/withdrawals/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListStoreWithdrawResponse } = require('../models/responsedataliststorewithdrawresponse');
    res.json(generateResponseDataListStoreWithdrawResponse());
  });

  /**
   * 
   * 原始路径: /store/withdrawals/batch
   * 操作ID: batchDeleteStoreWithdraw
 * @param {array} query参数 ids - 必填
   */
  app.delete('/store/withdrawals/batch', (req, res) => {
    console.log(`[DELETE] /store/withdrawals/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
