
// login-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /manage/logout
   * 操作ID: logout

   */
  app.post('/manage/logout', (req, res) => {
    console.log(`[POST] /manage/logout 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /manage/login
   * 操作ID: login
 * @param {string} header参数 key - 必填
 * @param {对象 (ManageLoginRequest)} body参数 - 必填
   */
  app.post('/manage/login', (req, res) => {
    console.log(`[POST] /manage/login 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
