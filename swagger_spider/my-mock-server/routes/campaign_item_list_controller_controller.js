
// campaign-item-list-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /campaign/items/{id}
   * 操作ID: getCampaignItemList
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/campaign/items/:id', (req, res) => {
    console.log(`[GET] /campaign/items/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseCampaignItemListResponse } = require('../models/responsecampaignitemlistresponse');
    res.json(generateResponseCampaignItemListResponse());
  });

  /**
   * 
   * 原始路径: /campaign/items/{id}
   * 操作ID: updateCampaignItemList
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (CampaignItemListRequest)} body参数 - 必填
   */
  app.put('/campaign/items/:id', (req, res) => {
    console.log(`[PUT] /campaign/items/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /campaign/items/{id}
   * 操作ID: deleteCampaignItemList
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/campaign/items/:id', (req, res) => {
    console.log(`[DELETE] /campaign/items/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /campaign/items
   * 操作ID: addCampaignItemList
 * @param {对象 (CampaignItemListRequest)} body参数 - 必填
   */
  app.post('/campaign/items', (req, res) => {
    console.log(`[POST] /campaign/items 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /campaign/items/list
   * 操作ID: getCampaignItemListList
 * @param {integer (int64)} query参数 campaignId - 可选
 * @param {integer (int64)} query参数 itemId - 可选
 * @param {integer (int32)} query参数 storeId - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/campaign/items/list', (req, res) => {
    console.log(`[GET] /campaign/items/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListCampaignItemListSimpleResponse } = require('../models/responsedatalistcampaignitemlistsimpleresponse');
    res.json(generateResponseDataListCampaignItemListSimpleResponse());
  });

  /**
   * 
   * 原始路径: /campaign/items/batch
   * 操作ID: batchDeleteCampaignItemList
 * @param {array} query参数 ids - 必填
   */
  app.delete('/campaign/items/batch', (req, res) => {
    console.log(`[DELETE] /campaign/items/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });
};
