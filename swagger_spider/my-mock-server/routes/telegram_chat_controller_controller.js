
// telegram-chat-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /telegram/chat/{id}
   * 操作ID: getTelegramChat
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/telegram/chat/:id', (req, res) => {
    console.log(`[GET] /telegram/chat/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseTelegramChatResponse } = require('../models/responsetelegramchatresponse');
    res.json(generateResponseTelegramChatResponse());
  });

  /**
   * 
   * 原始路径: /telegram/chat/{id}
   * 操作ID: updateTelegramChat
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (TelegramChatRequest)} body参数 - 必填
   */
  app.put('/telegram/chat/:id', (req, res) => {
    console.log(`[PUT] /telegram/chat/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /telegram/chat/{id}
   * 操作ID: deleteTelegramChat
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/telegram/chat/:id', (req, res) => {
    console.log(`[DELETE] /telegram/chat/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /telegram/chat/batch/status
   * 操作ID: batchUpdateTelegramChatStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/telegram/chat/batch/status', (req, res) => {
    console.log(`[PUT] /telegram/chat/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /telegram/chat/batch/robot
   * 操作ID: batchUpdateTelegramChatTelegramRobotId
 * @param {array} query参数 chatIds - 必填
 * @param {integer (int32)} query参数 telegramRobotId - 必填
   */
  app.put('/telegram/chat/batch/robot', (req, res) => {
    console.log(`[PUT] /telegram/chat/batch/robot 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /telegram/chat
   * 操作ID: addTelegramChat
 * @param {对象 (TelegramChatRequest)} body参数 - 必填
   */
  app.post('/telegram/chat', (req, res) => {
    console.log(`[POST] /telegram/chat 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /telegram/chat/list
   * 操作ID: listTelegramChat
 * @param {integer (int32)} query参数 telegramRobotId - 可选
 * @param {string} query参数 status - 可选
 * @param {string} query参数 username - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/telegram/chat/list', (req, res) => {
    console.log(`[GET] /telegram/chat/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListTelegramChatResponse } = require('../models/responsedatalisttelegramchatresponse');
    res.json(generateResponseDataListTelegramChatResponse());
  });

  /**
   * 
   * 原始路径: /telegram/chat/batch
   * 操作ID: batchDeleteTelegramChats
 * @param {array} query参数 ids - 必填
   */
  app.delete('/telegram/chat/batch', (req, res) => {
    console.log(`[DELETE] /telegram/chat/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
