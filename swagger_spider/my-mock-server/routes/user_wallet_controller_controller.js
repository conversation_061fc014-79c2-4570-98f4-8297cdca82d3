
// user-wallet-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /user/wallet/{id}
   * 操作ID: getUserWallet
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/user/wallet/:id', (req, res) => {
    console.log(`[GET] /user/wallet/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseUserWalletResponse } = require('../models/responseuserwalletresponse');
    res.json(generateResponseUserWalletResponse());
  });

  /**
   * 
   * 原始路径: /user/wallet/{id}
   * 操作ID: updateUserWallet
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (UserWalletRequest)} body参数 - 必填
   */
  app.put('/user/wallet/:id', (req, res) => {
    console.log(`[PUT] /user/wallet/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /user/wallet/{id}
   * 操作ID: deleteUserWallet
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/user/wallet/:id', (req, res) => {
    console.log(`[DELETE] /user/wallet/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /user/wallet/batch/status
   * 操作ID: batchUpdateUserWalletStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/user/wallet/batch/status', (req, res) => {
    console.log(`[PUT] /user/wallet/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /user/wallet
   * 操作ID: addUserWallet
 * @param {对象 (UserWalletRequest)} body参数 - 必填
   */
  app.post('/user/wallet', (req, res) => {
    console.log(`[POST] /user/wallet 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /user/wallet/list
   * 操作ID: listUserWallet
 * @param {integer (int32)} query参数 userId - 可选
 * @param {string} query参数 status - 可选
 * @param {string} query参数 walletType - 可选
 * @param {string} query参数 walletName - 可选
 * @param {string} query参数 receiveAccount - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/user/wallet/list', (req, res) => {
    console.log(`[GET] /user/wallet/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListUserWalletSimpleResponse } = require('../models/responsedatalistuserwalletsimpleresponse');
    res.json(generateResponseDataListUserWalletSimpleResponse());
  });

  /**
   * 
   * 原始路径: /user/wallet/batch
   * 操作ID: batchDeleteUserWallets
 * @param {array} query参数 ids - 必填
   */
  app.delete('/user/wallet/batch', (req, res) => {
    console.log(`[DELETE] /user/wallet/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
