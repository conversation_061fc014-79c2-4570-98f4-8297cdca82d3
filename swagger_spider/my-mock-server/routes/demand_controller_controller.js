
// demand-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /demand/{id}
   * 操作ID: getDemand
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/demand/:id', (req, res) => {
    console.log(`[GET] /demand/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDemandResponse } = require('../models/responsedemandresponse');
    res.json(generateResponseDemandResponse());
  });

  /**
   * 
   * 原始路径: /demand/{id}
   * 操作ID: updateDemand
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (DemandRequest)} body参数 - 必填
   */
  app.put('/demand/:id', (req, res) => {
    console.log(`[PUT] /demand/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /demand/{id}
   * 操作ID: deleteDemand
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/demand/:id', (req, res) => {
    console.log(`[DELETE] /demand/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /demand/batch/userId
   * 操作ID: batchUpdateDemandUserId
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 userId - 必填
   */
  app.put('/demand/batch/userId', (req, res) => {
    console.log(`[PUT] /demand/batch/userId 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /demand/batch/status
   * 操作ID: batchUpdateDemandStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/demand/batch/status', (req, res) => {
    console.log(`[PUT] /demand/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /demand/batch/servicesId
   * 操作ID: batchUpdateDemandServicesId
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 servicesId - 必填
   */
  app.put('/demand/batch/servicesId', (req, res) => {
    console.log(`[PUT] /demand/batch/servicesId 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /demand/batch/chooseStoreId
   * 操作ID: batchUpdateDemandChooseStoreId
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 chooseStoreId - 必填
   */
  app.put('/demand/batch/chooseStoreId', (req, res) => {
    console.log(`[PUT] /demand/batch/chooseStoreId 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /demand/batch/brandId
   * 操作ID: batchUpdateDemandBrandId
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 brandId - 必填
   */
  app.put('/demand/batch/brandId', (req, res) => {
    console.log(`[PUT] /demand/batch/brandId 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /demand
   * 操作ID: addDemand
 * @param {对象 (DemandRequest)} body参数 - 必填
   */
  app.post('/demand', (req, res) => {
    console.log(`[POST] /demand 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /demand/{id}/reject
   * 操作ID: rejectDemand
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (DemandRejectRequest)} body参数 - 必填
   */
  app.patch('/demand/:id/reject', (req, res) => {
    console.log(`[PATCH] /demand/{id}/reject 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /demand/{id}/approve
   * 操作ID: approveDemand
 * @param {integer (int64)} path参数 id - 必填
   */
  app.patch('/demand/:id/approve', (req, res) => {
    console.log(`[PATCH] /demand/{id}/approve 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /demand/list
   * 操作ID: listDemand
 * @param {integer (int32)} query参数 userId - 可选
 * @param {integer (int32)} query参数 brandId - 可选
 * @param {integer (int32)} query参数 servicesId - 可选
 * @param {string} query参数 status - 可选
 * @param {string} query参数 language - 可选
 * @param {string} query参数 demandName - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/demand/list', (req, res) => {
    console.log(`[GET] /demand/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListDemandSimpleResponse } = require('../models/responsedatalistdemandsimpleresponse');
    res.json(generateResponseDataListDemandSimpleResponse());
  });

  /**
   * 
   * 原始路径: /demand/batch
   * 操作ID: batchDeleteDemands
 * @param {array} query参数 ids - 必填
   */
  app.delete('/demand/batch', (req, res) => {
    console.log(`[DELETE] /demand/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
