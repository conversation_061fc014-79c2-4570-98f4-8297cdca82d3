
// item-stock-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /item/stock/{id}
   * 操作ID: getItemStock
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/item/stock/:id', (req, res) => {
    console.log(`[GET] /item/stock/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseItemStockResponse } = require('../models/responseitemstockresponse');
    res.json(generateResponseItemStockResponse());
  });

  /**
   * 
   * 原始路径: /item/stock/{id}
   * 操作ID: updateItemStock
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (ItemStockRequest)} body参数 - 必填
   */
  app.put('/item/stock/:id', (req, res) => {
    console.log(`[PUT] /item/stock/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /item/stock/{id}
   * 操作ID: deleteItemStock
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/item/stock/:id', (req, res) => {
    console.log(`[DELETE] /item/stock/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /item/stock/batch/checkStatus
   * 操作ID: batchUpdateItemStockCheckStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 checkStatus - 必填
   */
  app.put('/item/stock/batch/checkStatus', (req, res) => {
    console.log(`[PUT] /item/stock/batch/checkStatus 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /item/stock
   * 操作ID: addItemStock
 * @param {对象 (ItemStockRequest)} body参数 - 必填
   */
  app.post('/item/stock', (req, res) => {
    console.log(`[POST] /item/stock 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /item/stock/batch
   * 操作ID: batchDeleteItemStock
 * @param {array} query参数 ids - 必填
   */
  app.delete('/item/stock/batch', (req, res) => {
    console.log(`[DELETE] /item/stock/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /item/stock/list
   * 操作ID: listItemStock
 * @param {integer (int64)} query参数 itemId - 可选
 * @param {integer (int64)} query参数 batchId - 可选
 * @param {string} query参数 checkStatus - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/item/stock/list', (req, res) => {
    console.log(`[GET] /item/stock/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListItemStockResponse } = require('../models/responsedatalistitemstockresponse');
    res.json(generateResponseDataListItemStockResponse());
  });
};
