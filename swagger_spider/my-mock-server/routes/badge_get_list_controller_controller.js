
// badge-get-list-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /badge/gets/{id}
   * 操作ID: getBadgeGetList
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/badge/gets/:id', (req, res) => {
    console.log(`[GET] /badge/gets/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseBadgeGetListResponse } = require('../models/responsebadgegetlistresponse');
    res.json(generateResponseBadgeGetListResponse());
  });

  /**
   * 
   * 原始路径: /badge/gets/{id}
   * 操作ID: updateBadgeGetList
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (BadgeGetListRequest)} body参数 - 必填
   */
  app.put('/badge/gets/:id', (req, res) => {
    console.log(`[PUT] /badge/gets/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /badge/gets/{id}
   * 操作ID: deleteBadgeGetList
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/badge/gets/:id', (req, res) => {
    console.log(`[DELETE] /badge/gets/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /badge/gets/batch/badgeId
   * 操作ID: batchUpdateBadgeId
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 badgeId - 必填
   */
  app.put('/badge/gets/batch/badgeId', (req, res) => {
    console.log(`[PUT] /badge/gets/batch/badgeId 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /badge/gets
   * 操作ID: addBadgeGetList
 * @param {对象 (BadgeGetListRequest)} body参数 - 必填
   */
  app.post('/badge/gets', (req, res) => {
    console.log(`[POST] /badge/gets 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /badge/gets/list
   * 操作ID: getBadgeGetListList
 * @param {string} query参数 badgeType - 可选
 * @param {integer (int32)} query参数 badgeId - 可选
 * @param {integer (int32)} query参数 startDateTime - 可选
 * @param {integer (int32)} query参数 endDateTime - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/badge/gets/list', (req, res) => {
    console.log(`[GET] /badge/gets/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListBadgeGetListSimpleResponse } = require('../models/responsedatalistbadgegetlistsimpleresponse');
    res.json(generateResponseDataListBadgeGetListSimpleResponse());
  });

  /**
   * 
   * 原始路径: /badge/gets/batch
   * 操作ID: batchDeleteBadgeGetList
 * @param {array} query参数 ids - 必填
   */
  app.delete('/badge/gets/batch', (req, res) => {
    console.log(`[DELETE] /badge/gets/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });
};
