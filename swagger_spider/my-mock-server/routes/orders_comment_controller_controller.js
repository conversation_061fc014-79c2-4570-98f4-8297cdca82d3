
// orders-comment-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /orders/comment/{id}
   * 操作ID: getOrderComment
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/orders/comment/:id', (req, res) => {
    console.log(`[GET] /orders/comment/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseOrdersCommentResponse } = require('../models/responseorderscommentresponse');
    res.json(generateResponseOrdersCommentResponse());
  });

  /**
   * 
   * 原始路径: /orders/comment/{id}
   * 操作ID: updateOrderComment
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (OrdersCommentRequest)} body参数 - 必填
   */
  app.put('/orders/comment/:id', (req, res) => {
    console.log(`[PUT] /orders/comment/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /orders/comment/{id}
   * 操作ID: deleteOrderComment
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/orders/comment/:id', (req, res) => {
    console.log(`[DELETE] /orders/comment/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /orders/comment/batch/status
   * 操作ID: batchUpdateOrderCommentStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/orders/comment/batch/status', (req, res) => {
    console.log(`[PUT] /orders/comment/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /orders/comment
   * 操作ID: addOrderComment
 * @param {对象 (OrdersCommentRequest)} body参数 - 必填
   */
  app.post('/orders/comment', (req, res) => {
    console.log(`[POST] /orders/comment 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /orders/comment/list
   * 操作ID: listOrdersComment
 * @param {integer (int32)} query参数 userId - 可选
 * @param {integer (int64)} query参数 itemId - 可选
 * @param {integer (int64)} query参数 ordersId - 可选
 * @param {integer (int32)} query参数 storeId - 可选
 * @param {string} query参数 status - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/orders/comment/list', (req, res) => {
    console.log(`[GET] /orders/comment/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListOrdersCommentSimpleResponse } = require('../models/responsedatalistorderscommentsimpleresponse');
    res.json(generateResponseDataListOrdersCommentSimpleResponse());
  });

  /**
   * 
   * 原始路径: /orders/comment/batch
   * 操作ID: batchDeleteOrderComment
 * @param {array} query参数 ids - 必填
   */
  app.delete('/orders/comment/batch', (req, res) => {
    console.log(`[DELETE] /orders/comment/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
