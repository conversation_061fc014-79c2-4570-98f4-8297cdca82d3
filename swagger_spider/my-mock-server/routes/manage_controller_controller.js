
// manage-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /manage/{id}
   * 操作ID: getManage
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/manage/:id', (req, res) => {
    console.log(`[GET] /manage/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseManageResponse } = require('../models/responsemanageresponse');
    res.json(generateResponseManageResponse());
  });

  /**
   * 
   * 原始路径: /manage/{id}
   * 操作ID: updateManage
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (ManageRequest)} body参数 - 必填
   */
  app.put('/manage/:id', (req, res) => {
    console.log(`[PUT] /manage/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /manage/{id}
   * 操作ID: deleteManage
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/manage/:id', (req, res) => {
    console.log(`[DELETE] /manage/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /manage/batch/status
   * 操作ID: batchUpdateManageStatus
 * @param {array} query参数 manageIds - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/manage/batch/status', (req, res) => {
    console.log(`[PUT] /manage/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /manage
   * 操作ID: addManage
 * @param {对象 (ManageRequest)} body参数 - 必填
   */
  app.post('/manage', (req, res) => {
    console.log(`[POST] /manage 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /manage/list
   * 操作ID: listManage
 * @param {string} query参数 status - 可选
 * @param {integer (int32)} query参数 roleId - 可选
 * @param {string} query参数 username - 可选
 * @param {string} query参数 email - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/manage/list', (req, res) => {
    console.log(`[GET] /manage/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListManageSimpleResponse } = require('../models/responsedatalistmanagesimpleresponse');
    res.json(generateResponseDataListManageSimpleResponse());
  });

  /**
   * 
   * 原始路径: /manage/list/all
   * 操作ID: getAllManages

   */
  app.get('/manage/list/all', (req, res) => {
    console.log(`[GET] /manage/list/all 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseListNameResponse } = require('../models/responselistnameresponse');
    res.json(generateResponseListNameResponse());
  });

  /**
   * 
   * 原始路径: /manage/batch
   * 操作ID: batchDeleteManage
 * @param {array} query参数 manageIds - 必填
   */
  app.delete('/manage/batch', (req, res) => {
    console.log(`[DELETE] /manage/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });
};
