
// task-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /task/{id}
   * 操作ID: getTaskConfig
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/task/:id', (req, res) => {
    console.log(`[GET] /task/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseTaskConfigResponse } = require('../models/responsetaskconfigresponse');
    res.json(generateResponseTaskConfigResponse());
  });

  /**
   * 
   * 原始路径: /task/{id}
   * 操作ID: updateTaskConfig
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (TaskConfigRequest)} body参数 - 必填
   */
  app.put('/task/:id', (req, res) => {
    console.log(`[PUT] /task/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /task/{id}
   * 操作ID: deleteTaskConfig
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/task/:id', (req, res) => {
    console.log(`[DELETE] /task/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /task
   * 操作ID: addTaskConfig
 * @param {对象 (TaskConfigRequest)} body参数 - 必填
   */
  app.post('/task', (req, res) => {
    console.log(`[POST] /task 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /task/list
   * 操作ID: listTaskConfig
 * @param {string} query参数 taskName - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/task/list', (req, res) => {
    console.log(`[GET] /task/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListTaskConfigResponse } = require('../models/responsedatalisttaskconfigresponse');
    res.json(generateResponseDataListTaskConfigResponse());
  });

  /**
   * 
   * 原始路径: /task/batch
   * 操作ID: batchDeleteTaskConfig
 * @param {array} query参数 ids - 必填
   */
  app.delete('/task/batch', (req, res) => {
    console.log(`[DELETE] /task/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });
};
