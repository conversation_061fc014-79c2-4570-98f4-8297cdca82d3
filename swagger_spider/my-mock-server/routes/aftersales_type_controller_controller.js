
// aftersales-type-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /aftersales/type/{id}
   * 操作ID: getAftersalesTypeById
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/aftersales/type/:id', (req, res) => {
    console.log(`[GET] /aftersales/type/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseAftersalesTypeResponse } = require('../models/responseaftersalestyperesponse');
    res.json(generateResponseAftersalesTypeResponse());
  });

  /**
   * 
   * 原始路径: /aftersales/type/{id}
   * 操作ID: updateAftersalesType
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (AftersalesTypeRequest)} body参数 - 必填
   */
  app.put('/aftersales/type/:id', (req, res) => {
    console.log(`[PUT] /aftersales/type/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /aftersales/type/{id}
   * 操作ID: deleteAftersalesType
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/aftersales/type/:id', (req, res) => {
    console.log(`[DELETE] /aftersales/type/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /aftersales/type/batch/status
   * 操作ID: batchUpdateAftersalesTypeStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/aftersales/type/batch/status', (req, res) => {
    console.log(`[PUT] /aftersales/type/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /aftersales/type
   * 操作ID: addAftersalesType
 * @param {对象 (AftersalesTypeRequest)} body参数 - 必填
   */
  app.post('/aftersales/type', (req, res) => {
    console.log(`[POST] /aftersales/type 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /aftersales/type/list
   * 操作ID: getAftersalesTypeList
 * @param {integer (int32)} query参数 parentId - 可选
 * @param {string} query参数 language - 可选
 * @param {string} query参数 status - 可选
 * @param {string} query参数 customUrl - 可选
 * @param {string} query参数 typeName - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/aftersales/type/list', (req, res) => {
    console.log(`[GET] /aftersales/type/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListAftersalesTypeResponse } = require('../models/responsedatalistaftersalestyperesponse');
    res.json(generateResponseDataListAftersalesTypeResponse());
  });

  /**
   * 
   * 原始路径: /aftersales/type/batch
   * 操作ID: batchDeleteAftersalesType
 * @param {array} query参数 ids - 必填
   */
  app.delete('/aftersales/type/batch', (req, res) => {
    console.log(`[DELETE] /aftersales/type/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });
};
