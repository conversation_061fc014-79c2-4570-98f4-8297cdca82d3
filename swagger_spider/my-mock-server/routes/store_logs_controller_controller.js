
// store-logs-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /store/logs/{id}
   * 操作ID: getStoreLogs
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/store/logs/:id', (req, res) => {
    console.log(`[GET] /store/logs/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseStoreLogsResponse } = require('../models/responsestorelogsresponse');
    res.json(generateResponseStoreLogsResponse());
  });

  /**
   * 
   * 原始路径: /store/logs/{id}
   * 操作ID: updateStoreLogs
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (StoreLogsRequest)} body参数 - 必填
   */
  app.put('/store/logs/:id', (req, res) => {
    console.log(`[PUT] /store/logs/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /store/logs/{id}
   * 操作ID: deleteStoreLogs
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/store/logs/:id', (req, res) => {
    console.log(`[DELETE] /store/logs/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /store/logs/batch/store
   * 操作ID: batchUpdateStoreLogsStore
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 storeId - 必填
   */
  app.put('/store/logs/batch/store', (req, res) => {
    console.log(`[PUT] /store/logs/batch/store 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /store/logs/batch/staff
   * 操作ID: batchUpdateStoreLogsStaff
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 staffId - 必填
   */
  app.put('/store/logs/batch/staff', (req, res) => {
    console.log(`[PUT] /store/logs/batch/staff 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /store/logs/batch/method
   * 操作ID: batchUpdateStoreLogsMethod
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 method - 必填
   */
  app.put('/store/logs/batch/method', (req, res) => {
    console.log(`[PUT] /store/logs/batch/method 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /store/logs
   * 操作ID: addStoreLogs
 * @param {对象 (StoreLogsRequest)} body参数 - 必填
   */
  app.post('/store/logs', (req, res) => {
    console.log(`[POST] /store/logs 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /store/logs/list
   * 操作ID: listStoreLogs
 * @param {integer (int32)} query参数 storeId - 可选
 * @param {integer (int32)} query参数 staffId - 可选
 * @param {string} query参数 method - 可选
 * @param {string} query参数 module - 可选
 * @param {string} query参数 action - 可选
 * @param {integer (int32)} query参数 code - 可选
 * @param {integer (int64)} query参数 ip - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/store/logs/list', (req, res) => {
    console.log(`[GET] /store/logs/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListStoreLogsSimpleResponse } = require('../models/responsedataliststorelogssimpleresponse');
    res.json(generateResponseDataListStoreLogsSimpleResponse());
  });

  /**
   * 
   * 原始路径: /store/logs/batch
   * 操作ID: batchDeleteStoreLogs
 * @param {array} query参数 ids - 必填
   */
  app.delete('/store/logs/batch', (req, res) => {
    console.log(`[DELETE] /store/logs/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
