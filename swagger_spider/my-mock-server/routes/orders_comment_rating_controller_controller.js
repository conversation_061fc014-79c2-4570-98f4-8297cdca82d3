
// orders-comment-rating-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /orders/comment/rating/{id}
   * 操作ID: getOrdersCommentRating
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/orders/comment/rating/:id', (req, res) => {
    console.log(`[GET] /orders/comment/rating/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseOrdersCommentRatingResponse } = require('../models/responseorderscommentratingresponse');
    res.json(generateResponseOrdersCommentRatingResponse());
  });

  /**
   * 
   * 原始路径: /orders/comment/rating/{id}
   * 操作ID: updateOrdersCommentRating
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (OrdersCommentRatingRequest)} body参数 - 必填
   */
  app.put('/orders/comment/rating/:id', (req, res) => {
    console.log(`[PUT] /orders/comment/rating/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /orders/comment/rating/{id}
   * 操作ID: deleteOrdersCommentRating
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/orders/comment/rating/:id', (req, res) => {
    console.log(`[DELETE] /orders/comment/rating/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /orders/comment/rating
   * 操作ID: addOrdersCommentRating
 * @param {对象 (OrdersCommentRatingRequest)} body参数 - 必填
   */
  app.post('/orders/comment/rating', (req, res) => {
    console.log(`[POST] /orders/comment/rating 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /orders/comment/rating/list
   * 操作ID: listOrdersCommentRating
 * @param {integer (int64)} query参数 ordersCommentId - 可选
 * @param {integer (int32)} query参数 ratingId - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/orders/comment/rating/list', (req, res) => {
    console.log(`[GET] /orders/comment/rating/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListOrdersCommentRatingResponse } = require('../models/responsedatalistorderscommentratingresponse');
    res.json(generateResponseDataListOrdersCommentRatingResponse());
  });

  /**
   * 
   * 原始路径: /orders/comment/rating/batch
   * 操作ID: batchDeleteOrdersCommentRatings
 * @param {array} query参数 ids - 必填
   */
  app.delete('/orders/comment/rating/batch', (req, res) => {
    console.log(`[DELETE] /orders/comment/rating/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
