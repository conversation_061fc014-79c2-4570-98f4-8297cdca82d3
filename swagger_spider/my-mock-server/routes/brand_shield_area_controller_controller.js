
// brand-shield-area-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /brand/shield/area/{id}
   * 操作ID: getBrandShieldArea
 * @param {integer (int32)} path参数 id - 必填
   */
  app.get('/brand/shield/area/:id', (req, res) => {
    console.log(`[GET] /brand/shield/area/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseBrandShieldAreaResponse } = require('../models/responsebrandshieldarearesponse');
    res.json(generateResponseBrandShieldAreaResponse());
  });

  /**
   * 
   * 原始路径: /brand/shield/area/{id}
   * 操作ID: updateBrandShieldArea
 * @param {integer (int32)} path参数 id - 必填
 * @param {对象 (BrandShieldAreaRequest)} body参数 - 必填
   */
  app.put('/brand/shield/area/:id', (req, res) => {
    console.log(`[PUT] /brand/shield/area/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /brand/shield/area/{id}
   * 操作ID: deleteBrandShieldArea
 * @param {integer (int32)} path参数 id - 必填
   */
  app.delete('/brand/shield/area/:id', (req, res) => {
    console.log(`[DELETE] /brand/shield/area/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /brand/shield/area/batch/status
   * 操作ID: batchUpdateBrandShieldAreaStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/brand/shield/area/batch/status', (req, res) => {
    console.log(`[PUT] /brand/shield/area/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /brand/shield/area/batch/brand
   * 操作ID: batchUpdateBrandShieldAreaBrand
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 brandId - 必填
   */
  app.put('/brand/shield/area/batch/brand', (req, res) => {
    console.log(`[PUT] /brand/shield/area/batch/brand 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /brand/shield/area
   * 操作ID: addBrandShieldArea
 * @param {对象 (BrandShieldAreaRequest)} body参数 - 必填
   */
  app.post('/brand/shield/area', (req, res) => {
    console.log(`[POST] /brand/shield/area 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /brand/shield/area/list
   * 操作ID: getBrandShieldAreaList
 * @param {integer (int32)} query参数 brandId - 可选
 * @param {string} query参数 status - 可选
 * @param {string} query参数 country - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/brand/shield/area/list', (req, res) => {
    console.log(`[GET] /brand/shield/area/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListBrandShieldAreaSimpleResponse } = require('../models/responsedatalistbrandshieldareasimpleresponse');
    res.json(generateResponseDataListBrandShieldAreaSimpleResponse());
  });

  /**
   * 
   * 原始路径: /brand/shield/area/batch
   * 操作ID: batchDeleteBrandShieldArea
 * @param {array} query参数 ids - 必填
   */
  app.delete('/brand/shield/area/batch', (req, res) => {
    console.log(`[DELETE] /brand/shield/area/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });
};
