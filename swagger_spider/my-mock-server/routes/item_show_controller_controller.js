
// item-show-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /item/show/{id}
   * 操作ID: getItemShow
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/item/show/:id', (req, res) => {
    console.log(`[GET] /item/show/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseItemShowResponse } = require('../models/responseitemshowresponse');
    res.json(generateResponseItemShowResponse());
  });

  /**
   * 
   * 原始路径: /item/show/{id}
   * 操作ID: updateItemShow
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (ItemShowRequest)} body参数 - 必填
   */
  app.put('/item/show/:id', (req, res) => {
    console.log(`[PUT] /item/show/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /item/show/{id}
   * 操作ID: deleteItemShow
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/item/show/:id', (req, res) => {
    console.log(`[DELETE] /item/show/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /item/show/batch/status
   * 操作ID: batchUpdateItemShowStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/item/show/batch/status', (req, res) => {
    console.log(`[PUT] /item/show/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /item/show/batch/item
   * 操作ID: batchUpdateItemShowItem
 * @param {array} query参数 ids - 必填
 * @param {integer (int64)} query参数 itemId - 必填
   */
  app.put('/item/show/batch/item', (req, res) => {
    console.log(`[PUT] /item/show/batch/item 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /item/show
   * 操作ID: addItemShow
 * @param {对象 (ItemShowRequest)} body参数 - 必填
   */
  app.post('/item/show', (req, res) => {
    console.log(`[POST] /item/show 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /item/show/list
   * 操作ID: listItemShow
 * @param {integer (int64)} query参数 itemId - 可选
 * @param {string} query参数 status - 可选
 * @param {string} query参数 language - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/item/show/list', (req, res) => {
    console.log(`[GET] /item/show/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListItemShowResponse } = require('../models/responsedatalistitemshowresponse');
    res.json(generateResponseDataListItemShowResponse());
  });

  /**
   * 
   * 原始路径: /item/show/batch
   * 操作ID: batchDeleteItemShow
 * @param {array} query参数 ids - 必填
   */
  app.delete('/item/show/batch', (req, res) => {
    console.log(`[DELETE] /item/show/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
