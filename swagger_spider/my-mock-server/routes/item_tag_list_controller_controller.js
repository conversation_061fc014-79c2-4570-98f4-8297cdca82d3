
// item-tag-list-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /item/tags/{id}
   * 操作ID: getItemTagList
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/item/tags/:id', (req, res) => {
    console.log(`[GET] /item/tags/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseItemTagListResponse } = require('../models/responseitemtaglistresponse');
    res.json(generateResponseItemTagListResponse());
  });

  /**
   * 
   * 原始路径: /item/tags/{id}
   * 操作ID: updateItemTagList
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (ItemTagListRequest)} body参数 - 必填
   */
  app.put('/item/tags/:id', (req, res) => {
    console.log(`[PUT] /item/tags/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /item/tags/{id}
   * 操作ID: deleteItemTagList
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/item/tags/:id', (req, res) => {
    console.log(`[DELETE] /item/tags/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /item/tags
   * 操作ID: addItemTagList
 * @param {对象 (ItemTagListRequest)} body参数 - 必填
   */
  app.post('/item/tags', (req, res) => {
    console.log(`[POST] /item/tags 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseInteger } = require('../models/responseinteger');
    res.json(generateResponseInteger());
  });

  /**
   * 
   * 原始路径: /item/tags/list
   * 操作ID: listItemTagList
 * @param {integer (int64)} query参数 itemId - 可选
 * @param {integer (int32)} query参数 tagId - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/item/tags/list', (req, res) => {
    console.log(`[GET] /item/tags/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListItemTagListResponse } = require('../models/responsedatalistitemtaglistresponse');
    res.json(generateResponseDataListItemTagListResponse());
  });

  /**
   * 
   * 原始路径: /item/tags/batch
   * 操作ID: batchDeleteItemTagList
 * @param {array} query参数 ids - 必填
   */
  app.delete('/item/tags/batch', (req, res) => {
    console.log(`[DELETE] /item/tags/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
