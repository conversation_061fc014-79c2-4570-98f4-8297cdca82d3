
// store-notice-controller 路由
// 由SwaggerCrawler自动生成

module.exports = (app) => {

  /**
   * 
   * 原始路径: /store/notice/{id}
   * 操作ID: getStoreNotice
 * @param {integer (int64)} path参数 id - 必填
   */
  app.get('/store/notice/:id', (req, res) => {
    console.log(`[GET] /store/notice/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseStoreNoticeResponse } = require('../models/responsestorenoticeresponse');
    res.json(generateResponseStoreNoticeResponse());
  });

  /**
   * 
   * 原始路径: /store/notice/{id}
   * 操作ID: updateStoreNotice
 * @param {integer (int64)} path参数 id - 必填
 * @param {对象 (StoreNoticeRequest)} body参数 - 必填
   */
  app.put('/store/notice/:id', (req, res) => {
    console.log(`[PUT] /store/notice/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /store/notice/{id}
   * 操作ID: deleteStoreNotice
 * @param {integer (int64)} path参数 id - 必填
   */
  app.delete('/store/notice/:id', (req, res) => {
    console.log(`[DELETE] /store/notice/{id} 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /store/notice/batch/store
   * 操作ID: batchUpdateStoreNoticeStore
 * @param {array} query参数 ids - 必填
 * @param {integer (int32)} query参数 storeId - 必填
   */
  app.put('/store/notice/batch/store', (req, res) => {
    console.log(`[PUT] /store/notice/batch/store 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /store/notice/batch/status
   * 操作ID: batchUpdateStoreNoticeStatus
 * @param {array} query参数 ids - 必填
 * @param {string} query参数 status - 必填
   */
  app.put('/store/notice/batch/status', (req, res) => {
    console.log(`[PUT] /store/notice/batch/status 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });

  /**
   * 
   * 原始路径: /store/notice
   * 操作ID: addStoreNotice
 * @param {对象 (StoreNoticeRequest)} body参数 - 必填
   */
  app.post('/store/notice', (req, res) => {
    console.log(`[POST] /store/notice 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseLong } = require('../models/responselong');
    res.json(generateResponseLong());
  });

  /**
   * 
   * 原始路径: /store/notice/list
   * 操作ID: listStoreNotice
 * @param {integer (int32)} query参数 storeId - 可选
 * @param {string} query参数 noticeType - 可选
 * @param {string} query参数 status - 可选
 * @param {integer (int32)} query参数 createTimeStart - 可选
 * @param {integer (int32)} query参数 createTimeEnd - 可选
 * @param {integer (int32)} query参数 page - 必填
 * @param {integer (int32)} query参数 pageSize - 必填
   */
  app.get('/store/notice/list', (req, res) => {
    console.log(`[GET] /store/notice/list 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponseDataListStoreNoticeResponse } = require('../models/responsedataliststorenoticeresponse');
    res.json(generateResponseDataListStoreNoticeResponse());
  });

  /**
   * 
   * 原始路径: /store/notice/batch
   * 操作ID: batchDeleteStoreNotice
 * @param {array} query参数 ids - 必填
   */
  app.delete('/store/notice/batch', (req, res) => {
    console.log(`[DELETE] /store/notice/batch 被调用`);
    
    // 请求参数
    const params = req.params;
    const query = req.query;
    const body = req.body;
    
    console.log('请求参数:', { params, query, body });
    
    // 返回模拟响应
    const { generateResponse } = require('../models/response');
    res.json(generateResponse());
  });
};
