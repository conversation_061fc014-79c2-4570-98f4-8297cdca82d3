
// ResultCampaignTagListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultCampaignTagListResponse模型的模拟数据
 * @returns {ResultCampaignTagListResponse} 模拟数据
 */
function generateResultCampaignTagListResponse() {
  return {"code": "demand_publish_attr_list_not_empty", "data": {"id": 337, "campaignId": 240, "tagId": 775, "createTime": 1753433976}};
}

module.exports = {
  generateResultCampaignTagListResponse
};
