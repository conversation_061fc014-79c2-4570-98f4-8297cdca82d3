
// ResponseSearchResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseSearchResponse模型的模拟数据
 * @returns {ResponseSearchResponse} 模拟数据
 */
function generateResponseSearchResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 618, "searchType": "1", "searchTypeName": "thing", "brand": {"id": 660, "name": "discuss", "url": "south", "avatar": "lay"}, "status": "1", "statusName": "base", "sortIndex": 163, "searchCount": 996, "createTime": 1753433976, "updateTime": 1753433976}, "result": {"code": "attr_update_error", "data": {"id": 544, "searchType": "1", "searchTypeName": "wait", "brand": {"id": 53, "name": "laugh", "url": "over", "avatar": "nor"}, "status": "1", "statusName": "office", "sortIndex": 191, "searchCount": 795, "createTime": 1753433976, "updateTime": 1753433976}}, "errMessageOnly": "speech", "successMessage": "establish", "errMessage": "husband"};
}

module.exports = {
  generateResponseSearchResponse
};
