
// ResponseTelegramRobotResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseTelegramRobotResponse模型的模拟数据
 * @returns {ResponseTelegramRobotResponse} 模拟数据
 */
function generateResponseTelegramRobotResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 445, "status": "1", "name": "provide", "botName": "left", "username": "measure", "token": "finish", "mark": "hundred", "createTime": 1753433976}, "result": {"code": "search_batch_update_status_error", "data": {"id": 243, "status": "1", "name": "exactly", "botName": "would", "username": "social", "token": "attention", "mark": "agree", "createTime": 1753433976}}, "errMessageOnly": "blue", "successMessage": "board", "errMessage": "daughter"};
}

module.exports = {
  generateResponseTelegramRobotResponse
};
