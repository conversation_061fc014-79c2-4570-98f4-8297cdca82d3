
// ResponseRateConfigResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseRateConfigResponse模型的模拟数据
 * @returns {ResponseRateConfigResponse} 模拟数据
 */
function generateResponseRateConfigResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 664, "country": "agreement", "currency": "reveal", "currencyName": "federal", "symbolFloat": 786, "currencySymbol": "last", "rate": 360, "mark": "still", "createTime": 1753433976, "updateTime": 1753433976}, "result": {"code": "user_save_list_save_type_invalid", "data": {"id": 140, "country": "bit", "currency": "clear", "currencyName": "special", "symbolFloat": 387, "currencySymbol": "opportunity", "rate": 455, "mark": "brother", "createTime": 1753433976, "updateTime": 1753433976}}, "errMessageOnly": "here", "successMessage": "computer", "errMessage": "rather"};
}

module.exports = {
  generateResponseRateConfigResponse
};
