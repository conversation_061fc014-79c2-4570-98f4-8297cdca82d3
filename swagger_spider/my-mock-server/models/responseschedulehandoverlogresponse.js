
// ResponseScheduleHandoverLogResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseScheduleHandoverLogResponse模型的模拟数据
 * @returns {ResponseScheduleHandoverLogResponse} 模拟数据
 */
function generateResponseScheduleHandoverLogResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 912, "scheduleHandoverId": 639, "scheduleId": 83, "manageId": 261, "status": "1", "mark": "pay", "createTime": 1753433976}, "result": {"code": "orders_ticket_user_id_not_match", "data": {"id": 518, "scheduleHandoverId": 223, "scheduleId": 587, "manageId": 325, "status": "1", "mark": "him", "createTime": 1753433976}}, "errMessageOnly": "why", "successMessage": "human", "errMessage": "movie"};
}

module.exports = {
  generateResponseScheduleHandoverLogResponse
};
