
// StoreSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成StoreSimpleResponse模型的模拟数据
 * @returns {StoreSimpleResponse} 模拟数据
 */
function generateStoreSimpleResponse() {
  return {"id": 431, "user": {"id": 591, "name": "story", "url": "approach", "avatar": "according"}, "status": "1", "language": "en_US", "name": "state", "brandList": [{"id": 694, "name": "cold", "url": "fine", "avatar": "toward"}], "tagList": [{"id": 440, "name": "floor", "url": "far", "avatar": "add"}], "servicesList": [{"id": 215, "name": "air", "url": "physical", "avatar": "stage"}, {"id": 109, "name": "until", "url": "make", "avatar": "century"}], "logo": "bed", "country": "happy"};
}

module.exports = {
  generateStoreSimpleResponse
};
