
// ResponseDataListUserSubscribeListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListUserSubscribeListResponse模型的模拟数据
 * @returns {ResponseDataListUserSubscribeListResponse} 模拟数据
 */
function generateResponseDataListUserSubscribeListResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 875, "maxPage": 49, "total": 8856, "data": [{"id": 806, "user": {"id": 538, "name": "however", "url": "pattern", "avatar": "amount"}, "store": {"id": 254, "name": "including", "url": "themselves", "avatar": "quite"}, "item": {"id": 89, "name": "themselves", "url": "different", "avatar": "news"}, "noticePrice": 92}, {"id": 779, "user": {"id": 74, "name": "marriage", "url": "official", "avatar": "number"}, "store": {"id": 608, "name": "hear", "url": "dinner", "avatar": "fight"}, "item": {"id": 151, "name": "law", "url": "window", "avatar": "local"}, "noticePrice": 893}]}, "result": {"code": "phone_number_error", "data": {"curPage": 803, "maxPage": 609, "total": 328, "data": [{"id": 370, "user": {"id": 587, "name": "believe", "url": "listen", "avatar": "ground"}, "store": {"id": 589, "name": "agency", "url": "forward", "avatar": "son"}, "item": {"id": 633, "name": "article", "url": "since", "avatar": "themselves"}, "noticePrice": 284}, {"id": 846, "user": {"id": 643, "name": "billion", "url": "seek", "avatar": "husband"}, "store": {"id": 641, "name": "able", "url": "democratic", "avatar": "affect"}, "item": {"id": 127, "name": "walk", "url": "agent", "avatar": "baby"}, "noticePrice": 582}]}}, "errMessageOnly": "nothing", "successMessage": "to", "errMessage": "adult"};
}

module.exports = {
  generateResponseDataListUserSubscribeListResponse
};
