
// ResponseDataListItemAftersalesStatResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListItemAftersalesStatResponse模型的模拟数据
 * @returns {ResponseDataListItemAftersalesStatResponse} 模拟数据
 */
function generateResponseDataListItemAftersalesStatResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 132, "maxPage": 64, "total": 510, "data": [{"id": 184, "aftersalesId": 38, "store": {"id": 555, "name": "mean", "url": "key", "avatar": "modern"}, "item": {"id": 62, "name": "allow", "url": "sort", "avatar": "then"}, "hitCount": 923}, {"id": 83, "aftersalesId": 750, "store": {"id": 410, "name": "sister", "url": "fear", "avatar": "past"}, "item": {"id": 154, "name": "century", "url": "before", "avatar": "occur"}, "hitCount": 525}]}, "result": {"code": "schedule_time_range_error", "data": {"curPage": 12, "maxPage": 227, "total": 210, "data": [{"id": 792, "aftersalesId": 176, "store": {"id": 728, "name": "old", "url": "street", "avatar": "seat"}, "item": {"id": 910, "name": "agree", "url": "land", "avatar": "yourself"}, "hitCount": 635}, {"id": 342, "aftersalesId": 956, "store": {"id": 887, "name": "realize", "url": "close", "avatar": "win"}, "item": {"id": 526, "name": "dinner", "url": "whatever", "avatar": "soldier"}, "hitCount": 40}]}}, "errMessageOnly": "nation", "successMessage": "assume", "errMessage": "since"};
}

module.exports = {
  generateResponseDataListItemAftersalesStatResponse
};
