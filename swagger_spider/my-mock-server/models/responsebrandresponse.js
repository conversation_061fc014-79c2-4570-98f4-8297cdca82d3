
// ResponseBrandResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseBrandResponse模型的模拟数据
 * @returns {ResponseBrandResponse} 模拟数据
 */
function generateResponseBrandResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 175, "parentId": 741, "status": "1", "brandType": "1", "brandLogo": "far", "customUrl": "officer", "createTime": 1753433976, "updateTime": 1753433976, "brandLangId": 531, "language": "en_US"}, "result": {"code": "schedule_sign_log_review_status_invalid", "data": {"id": 583, "parentId": 847, "status": "1", "brandType": "1", "brandLogo": "adult", "customUrl": "daughter", "createTime": 1753433976, "updateTime": 1753433976, "brandLangId": 272, "language": "en_US"}}, "errMessageOnly": "assume", "successMessage": "her", "errMessage": "add"};
}

module.exports = {
  generateResponseBrandResponse
};
