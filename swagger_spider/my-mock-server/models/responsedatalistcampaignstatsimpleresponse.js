
// ResponseDataListCampaignStatSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListCampaignStatSimpleResponse模型的模拟数据
 * @returns {ResponseDataListCampaignStatSimpleResponse} 模拟数据
 */
function generateResponseDataListCampaignStatSimpleResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 82, "maxPage": 156, "total": 3285, "data": [{"id": 671, "campaign": {"id": 345, "name": "anyone", "url": "best", "avatar": "degree"}, "visitCount": 670, "ordersCount": 917, "salesAmount": 616}]}, "result": {"code": "todo_list_batch_delete_failed", "data": {"curPage": 174, "maxPage": 580, "total": 2321, "data": [{"id": 759, "campaign": {"id": 413, "name": "water", "url": "agency", "avatar": "build"}, "visitCount": 646, "ordersCount": 789, "salesAmount": 996}, {"id": 233, "campaign": {"id": 873, "name": "every", "url": "do", "avatar": "degree"}, "visitCount": 718, "ordersCount": 195, "salesAmount": 18}]}}, "errMessageOnly": "draw", "successMessage": "wall", "errMessage": "mouth"};
}

module.exports = {
  generateResponseDataListCampaignStatSimpleResponse
};
