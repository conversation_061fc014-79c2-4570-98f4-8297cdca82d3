
// StoreStatRequest 模型
// 由SwaggerCrawler自动生成

/**
 * 生成StoreStatRequest模型的模拟数据
 * @returns {StoreStatRequest} 模拟数据
 */
function generateStoreStatRequest() {
  return {"storeId": 219, "visitCount": 276, "ordersCount": 342, "salesAmount": 747, "salesCount": 579, "salesProfit": 828, "refundAmount": 352, "withdrawAmount": 992, "riskChatCount": 211, "riskSaleCount": 703};
}

module.exports = {
  generateStoreStatRequest
};
