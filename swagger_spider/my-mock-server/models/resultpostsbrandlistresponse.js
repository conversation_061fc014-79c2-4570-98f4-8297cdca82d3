
// ResultPostsBrandListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultPostsBrandListResponse模型的模拟数据
 * @returns {ResultPostsBrandListResponse} 模拟数据
 */
function generateResultPostsBrandListResponse() {
  return {"code": "whitelist_id_not_exists", "data": {"id": 499, "posts": {"id": 133, "name": "message", "url": "run", "avatar": "end"}, "brand": {"id": 571, "name": "find", "url": "when", "avatar": "at"}, "createTime": 1753433976}};
}

module.exports = {
  generateResultPostsBrandListResponse
};
