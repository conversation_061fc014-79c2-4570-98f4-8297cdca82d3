
// ResultQuickMessageResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultQuickMessageResponse模型的模拟数据
 * @returns {ResultQuickMessageResponse} 模拟数据
 */
function generateResultQuickMessageResponse() {
  return {"code": "orders_comment_id_not_exists", "data": {"id": 447, "quickMessageTypeId": 734, "manageId": 52, "status": "1", "useCount": 166, "mark": "future", "lastUseTime": 1753433976, "updateTime": 1753433976, "createTime": 1753433976, "quickMessageLangId": 670}};
}

module.exports = {
  generateResultQuickMessageResponse
};
