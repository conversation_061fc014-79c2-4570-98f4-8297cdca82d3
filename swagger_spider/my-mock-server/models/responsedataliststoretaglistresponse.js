
// ResponseDataListStoreTagListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListStoreTagListResponse模型的模拟数据
 * @returns {ResponseDataListStoreTagListResponse} 模拟数据
 */
function generateResponseDataListStoreTagListResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 397, "maxPage": 701, "total": 1687, "data": [{"id": 903, "store": {"id": 245, "name": "everyone", "url": "threat", "avatar": "rise"}, "tag": {"id": 518, "name": "agency", "url": "where", "avatar": "result"}, "createTime": 1753433976}, {"id": 614, "store": {"id": 924, "name": "road", "url": "parent", "avatar": "fire"}, "tag": {"id": 705, "name": "even", "url": "after", "avatar": "movement"}, "createTime": 1753433976}, {"id": 570, "store": {"id": 393, "name": "production", "url": "cold", "avatar": "effect"}, "tag": {"id": 73, "name": "tell", "url": "reach", "avatar": "either"}, "createTime": 1753433976}]}, "result": {"code": "rating_add_error", "data": {"curPage": 852, "maxPage": 456, "total": 3949, "data": [{"id": 436, "store": {"id": 437, "name": "movement", "url": "college", "avatar": "sit"}, "tag": {"id": 2, "name": "describe", "url": "along", "avatar": "raise"}, "createTime": 1753433976}, {"id": 386, "store": {"id": 922, "name": "down", "url": "lot", "avatar": "there"}, "tag": {"id": 560, "name": "affect", "url": "free", "avatar": "majority"}, "createTime": 1753433976}, {"id": 808, "store": {"id": 438, "name": "certain", "url": "writer", "avatar": "take"}, "tag": {"id": 178, "name": "summer", "url": "science", "avatar": "sure"}, "createTime": 1753433976}]}}, "errMessageOnly": "maybe", "successMessage": "reflect", "errMessage": "do"};
}

module.exports = {
  generateResponseDataListStoreTagListResponse
};
