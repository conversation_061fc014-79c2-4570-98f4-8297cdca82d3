
// ResultStoreResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultStoreResponse模型的模拟数据
 * @returns {ResultStoreResponse} 模拟数据
 */
function generateResultStoreResponse() {
  return {"code": "search_status_invalid", "data": {"id": 434, "user": {"id": 279, "name": "never", "url": "on", "avatar": "main"}, "status": "1", "language": "en_US", "name": "ahead", "brandList": [{"id": 871, "name": "political", "url": "PM", "avatar": "world"}, {"id": 352, "name": "who", "url": "consumer", "avatar": "radio"}, {"id": 666, "name": "action", "url": "performance", "avatar": "meet"}], "tagList": [{"id": 457, "name": "moment", "url": "mouth", "avatar": "away"}, {"id": 705, "name": "watch", "url": "center", "avatar": "north"}, {"id": 845, "name": "level", "url": "nature", "avatar": "game"}], "servicesList": [{"id": 442, "name": "really", "url": "effort", "avatar": "medical"}, {"id": 482, "name": "realize", "url": "Mrs", "avatar": "security"}], "logo": "join", "country": "sure"}};
}

module.exports = {
  generateResultStoreResponse
};
