
// ResponseStoreLogsResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseStoreLogsResponse模型的模拟数据
 * @returns {ResponseStoreLogsResponse} 模拟数据
 */
function generateResponseStoreLogsResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 11, "store": {"id": 297, "name": "technology", "url": "state", "avatar": "your"}, "staff": {"id": 332, "name": "sit", "url": "why", "avatar": "particularly"}, "method": "run", "module": "share", "action": "energy", "ip": 191, "createTime": 1753433976, "code": 0, "ua": "tough"}, "result": {"code": "page_invalid", "data": {"id": 841, "store": {"id": 809, "name": "weight", "url": "art", "avatar": "within"}, "staff": {"id": 443, "name": "perhaps", "url": "discuss", "avatar": "go"}, "method": "happy", "module": "reflect", "action": "these", "ip": 169, "createTime": 1753433976, "code": 0, "ua": "ago"}}, "errMessageOnly": "stand", "successMessage": "suddenly", "errMessage": "one"};
}

module.exports = {
  generateResponseStoreLogsResponse
};
