
// ResponseDataListTagSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListTagSimpleResponse模型的模拟数据
 * @returns {ResponseDataListTagSimpleResponse} 模拟数据
 */
function generateResponseDataListTagSimpleResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 73, "maxPage": 402, "total": 1403, "data": [{"id": 696, "status": "1", "sortIndex": "0", "useType": "1", "language": "en_US"}, {"id": 549, "status": "1", "sortIndex": "0", "useType": "1", "language": "en_US"}, {"id": 262, "status": "1", "sortIndex": "0", "useType": "1", "language": "en_US"}]}, "result": {"code": "demand_user_id_error", "data": {"curPage": 235, "maxPage": 923, "total": 6632, "data": [{"id": 527, "status": "1", "sortIndex": "0", "useType": "1", "language": "en_US"}, {"id": 504, "status": "1", "sortIndex": "0", "useType": "1", "language": "en_US"}]}}, "errMessageOnly": "watch", "successMessage": "finish", "errMessage": "total"};
}

module.exports = {
  generateResponseDataListTagSimpleResponse
};
