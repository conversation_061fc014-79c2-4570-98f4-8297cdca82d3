
// ResultResourcesPermissionsResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultResourcesPermissionsResponse模型的模拟数据
 * @returns {ResultResourcesPermissionsResponse} 模拟数据
 */
function generateResultResourcesPermissionsResponse() {
  return {"code": "black_list_id_not_exists", "data": {"id": 515, "resourcesId": 429, "sortIndex": "0", "showStatus": "1", "permissionName": "up", "fields": ["road", "south"], "buttons": ["above", "your", "firm"], "urls": ["from", "decade", "consider"], "createTime": 1753433976}};
}

module.exports = {
  generateResultResourcesPermissionsResponse
};
