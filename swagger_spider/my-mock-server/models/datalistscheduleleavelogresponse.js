
// DataListScheduleLeaveLogResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListScheduleLeaveLogResponse模型的模拟数据
 * @returns {DataListScheduleLeaveLogResponse} 模拟数据
 */
function generateDataListScheduleLeaveLogResponse() {
  return {"curPage": 916, "maxPage": 672, "total": 2019, "data": [{"id": 427, "schedule": {"id": 270, "name": "little", "url": "man", "avatar": "together"}, "manage": {"id": 378, "name": "let", "url": "partner", "avatar": "sense"}, "status": "1", "statusName": "agency", "askTime": "1982-06-03T21:56:37.839163", "leaveTime": "2011-06-17T01:29:03.619158", "backTime": "2004-12-17T01:58:07.448544", "leaveType": "1", "leaveTypeName": "will"}, {"id": 572, "schedule": {"id": 290, "name": "and", "url": "fine", "avatar": "any"}, "manage": {"id": 896, "name": "ball", "url": "of", "avatar": "cold"}, "status": "1", "statusName": "story", "askTime": "1973-09-16T10:57:14.252683", "leaveTime": "1982-01-03T09:43:52.877596", "backTime": "1984-07-15T00:43:56.554996", "leaveType": "1", "leaveTypeName": "particular"}, {"id": 375, "schedule": {"id": 657, "name": "back", "url": "head", "avatar": "resource"}, "manage": {"id": 774, "name": "alone", "url": "window", "avatar": "event"}, "status": "1", "statusName": "for", "askTime": "2013-10-17T00:02:46.667890", "leaveTime": "1974-11-03T07:21:01.714178", "backTime": "1995-10-07T01:48:55.459729", "leaveType": "1", "leaveTypeName": "dog"}]};
}

module.exports = {
  generateDataListScheduleLeaveLogResponse
};
