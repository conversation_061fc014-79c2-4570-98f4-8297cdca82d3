
// ResultDataListPaymentSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListPaymentSimpleResponse模型的模拟数据
 * @returns {ResultDataListPaymentSimpleResponse} 模拟数据
 */
function generateResultDataListPaymentSimpleResponse() {
  return {"code": "services_id_not_exists", "data": {"curPage": 689, "maxPage": 33, "total": 5425, "data": [{"id": 849, "status": "1", "paymentType": "firm", "paymentName": "meet", "paymentLogo": "kitchen"}, {"id": 254, "status": "1", "paymentType": "data", "paymentName": "claim", "paymentLogo": "type"}]}};
}

module.exports = {
  generateResultDataListPaymentSimpleResponse
};
