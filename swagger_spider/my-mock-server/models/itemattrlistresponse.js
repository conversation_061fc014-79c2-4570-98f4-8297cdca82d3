
// ItemAttrListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ItemAttrListResponse模型的模拟数据
 * @returns {ItemAttrListResponse} 模拟数据
 */
function generateItemAttrListResponse() {
  return {"id": 563, "item": {"id": 730, "name": "peace", "url": "old", "avatar": "finish"}, "attr": {"id": 713, "name": "impact", "url": "less", "avatar": "central"}, "attrValue": {"id": 799, "name": "center", "url": "kid", "avatar": "assume"}, "createTime": 1753433976};
}

module.exports = {
  generateItemAttrListResponse
};
