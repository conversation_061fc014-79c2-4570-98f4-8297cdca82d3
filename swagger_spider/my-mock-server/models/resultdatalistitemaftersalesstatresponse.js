
// ResultDataListItemAftersalesStatResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListItemAftersalesStatResponse模型的模拟数据
 * @returns {ResultDataListItemAftersalesStatResponse} 模拟数据
 */
function generateResultDataListItemAftersalesStatResponse() {
  return {"code": "schedule_handover_log_delete_failed", "data": {"curPage": 852, "maxPage": 803, "total": 8768, "data": [{"id": 805, "aftersalesId": 574, "store": {"id": 182, "name": "thing", "url": "because", "avatar": "decision"}, "item": {"id": 548, "name": "then", "url": "attention", "avatar": "reduce"}, "hitCount": 533}, {"id": 801, "aftersalesId": 681, "store": {"id": 179, "name": "soldier", "url": "able", "avatar": "local"}, "item": {"id": 648, "name": "learn", "url": "such", "avatar": "part"}, "hitCount": 581}]}};
}

module.exports = {
  generateResultDataListItemAftersalesStatResponse
};
