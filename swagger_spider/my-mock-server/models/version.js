
// Version 模型
// 由SwaggerCrawler自动生成

/**
 * 生成Version模型的模拟数据
 * @returns {Version} 模拟数据
 */
function generateVersion() {
  return {"number": "find", "buildFlavor": "several", "buildType": "hit", "buildHash": "your", "buildDate": "style", "luceneVersion": "this", "minimumWireCompatibilityVersion": "ago", "minimumIndexCompatibilityVersion": "their", "snapshot": false};
}

module.exports = {
  generateVersion
};
