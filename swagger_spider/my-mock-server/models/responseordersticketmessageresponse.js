
// ResponseOrdersTicketMessageResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseOrdersTicketMessageResponse模型的模拟数据
 * @returns {ResponseOrdersTicketMessageResponse} 模拟数据
 */
function generateResponseOrdersTicketMessageResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 693, "ordersTicketId": 395, "user": {"id": 119, "name": "mouth", "url": "of", "avatar": "note"}, "store": {"id": 675, "name": "subject", "url": "feeling", "avatar": "others"}, "userReadTime": 1753433976, "storeReadTime": 1753433976, "replyId": 413, "replyMessageSummary": "Mr", "messageType": "1", "message": ["1", "1"]}, "result": {"code": "user_login_failed", "data": {"id": 563, "ordersTicketId": 714, "user": {"id": 800, "name": "least", "url": "although", "avatar": "memory"}, "store": {"id": 968, "name": "human", "url": "fund", "avatar": "million"}, "userReadTime": 1753433976, "storeReadTime": 1753433976, "replyId": 34, "replyMessageSummary": "do", "messageType": "1", "message": ["1", "1", "1"]}}, "errMessageOnly": "another", "successMessage": "form", "errMessage": "few"};
}

module.exports = {
  generateResponseOrdersTicketMessageResponse
};
