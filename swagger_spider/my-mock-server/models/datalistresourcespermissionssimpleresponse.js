
// DataListResourcesPermissionsSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListResourcesPermissionsSimpleResponse模型的模拟数据
 * @returns {DataListResourcesPermissionsSimpleResponse} 模拟数据
 */
function generateDataListResourcesPermissionsSimpleResponse() {
  return {"curPage": 694, "maxPage": 585, "total": 7039, "data": [{"id": 215, "resourcesId": 391, "resourceMenuPath": "us", "resourceMenuName": "next", "sortIndex": "0", "showStatus": "1", "permissionName": "mother", "fields": ["debate", "question", "perform"], "buttons": ["president", "exactly", "hit"], "urls": ["laugh", "decade"]}, {"id": 570, "resourcesId": 916, "resourceMenuPath": "environment", "resourceMenuName": "personal", "sortIndex": "0", "showStatus": "1", "permissionName": "west", "fields": ["line", "his", "political"], "buttons": ["we", "foreign", "north"], "urls": ["management"]}]};
}

module.exports = {
  generateDataListResourcesPermissionsSimpleResponse
};
