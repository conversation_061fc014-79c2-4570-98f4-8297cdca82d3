
// ResponseDataListManageLogsSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListManageLogsSimpleResponse模型的模拟数据
 * @returns {ResponseDataListManageLogsSimpleResponse} 模拟数据
 */
function generateResponseDataListManageLogsSimpleResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 899, "maxPage": 97, "total": 9237, "data": [{"id": 437, "manageId": 281, "manageUsername": "focus", "ip": 9356, "method": "indeed"}, {"id": 318, "manageId": 937, "manageUsername": "great", "ip": 6430, "method": "return"}, {"id": 251, "manageId": 795, "manageUsername": "sister", "ip": 7565, "method": "personal"}]}, "result": {"code": "user_change_password_token_incorrect", "data": {"curPage": 585, "maxPage": 948, "total": 3732, "data": [{"id": 779, "manageId": 585, "manageUsername": "population", "ip": 80, "method": "stand"}, {"id": 55, "manageId": 350, "manageUsername": "article", "ip": 6609, "method": "by"}, {"id": 463, "manageId": 141, "manageUsername": "industry", "ip": 1761, "method": "beyond"}]}}, "errMessageOnly": "minute", "successMessage": "position", "errMessage": "partner"};
}

module.exports = {
  generateResponseDataListManageLogsSimpleResponse
};
