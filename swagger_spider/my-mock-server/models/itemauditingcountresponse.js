
// ItemAuditingCountResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ItemAuditingCountResponse模型的模拟数据
 * @returns {ItemAuditingCountResponse} 模拟数据
 */
function generateItemAuditingCountResponse() {
  return {"todayItemAuditingCount": 5930, "todayNewItemAuditingCount": 5191, "todayPassItemAuditingCount": 717, "todayRejectedItemAuditingCount": 2};
}

module.exports = {
  generateItemAuditingCountResponse
};
