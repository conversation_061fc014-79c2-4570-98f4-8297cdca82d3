
// ResultDataListFaqSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListFaqSimpleResponse模型的模拟数据
 * @returns {ResultDataListFaqSimpleResponse} 模拟数据
 */
function generateResultDataListFaqSimpleResponse() {
  return {"code": "demand_delivered_failed", "data": {"curPage": 824, "maxPage": 323, "total": 7292, "data": [{"id": 521, "useTypeName": "eye", "manage": {"id": 667, "name": "worker", "url": "suggest", "avatar": "run"}, "brand": {"id": 884, "name": "final", "url": "young", "avatar": "owner"}, "statusName": "audience"}, {"id": 248, "useTypeName": "trial", "manage": {"id": 653, "name": "blue", "url": "about", "avatar": "station"}, "brand": {"id": 90, "name": "spend", "url": "family", "avatar": "support"}, "statusName": "food"}, {"id": 112, "useTypeName": "theory", "manage": {"id": 3, "name": "she", "url": "later", "avatar": "hundred"}, "brand": {"id": 937, "name": "record", "url": "south", "avatar": "later"}, "statusName": "good"}]}};
}

module.exports = {
  generateResultDataListFaqSimpleResponse
};
