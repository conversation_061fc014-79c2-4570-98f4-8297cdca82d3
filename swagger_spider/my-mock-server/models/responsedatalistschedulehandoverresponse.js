
// ResponseDataListScheduleHandoverResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListScheduleHandoverResponse模型的模拟数据
 * @returns {ResponseDataListScheduleHandoverResponse} 模拟数据
 */
function generateResponseDataListScheduleHandoverResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 508, "maxPage": 549, "total": 447, "data": [{"id": 330, "status": "1", "workSort": 339, "handoverType": "1", "relationId": 204}, {"id": 495, "status": "1", "workSort": 756, "handoverType": "1", "relationId": 409}]}, "result": {"code": "user_email_token_expired", "data": {"curPage": 174, "maxPage": 476, "total": 7421, "data": [{"id": 225, "status": "1", "workSort": 944, "handoverType": "1", "relationId": 999}, {"id": 268, "status": "1", "workSort": 357, "handoverType": "1", "relationId": 155}]}}, "errMessageOnly": "couple", "successMessage": "wind", "errMessage": "ten"};
}

module.exports = {
  generateResponseDataListScheduleHandoverResponse
};
