
// ResponseDataListSearchResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListSearchResponse模型的模拟数据
 * @returns {ResponseDataListSearchResponse} 模拟数据
 */
function generateResponseDataListSearchResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 599, "maxPage": 737, "total": 4347, "data": [{"id": 7, "searchType": "1", "searchTypeName": "long", "brand": {"id": 262, "name": "analysis", "url": "I", "avatar": "image"}, "status": "1"}]}, "result": {"code": "manage_update_error", "data": {"curPage": 623, "maxPage": 543, "total": 7732, "data": [{"id": 753, "searchType": "1", "searchTypeName": "book", "brand": {"id": 821, "name": "none", "url": "force", "avatar": "commercial"}, "status": "1"}, {"id": 223, "searchType": "1", "searchTypeName": "section", "brand": {"id": 472, "name": "camera", "url": "feeling", "avatar": "sport"}, "status": "1"}, {"id": 370, "searchType": "1", "searchTypeName": "assume", "brand": {"id": 571, "name": "from", "url": "type", "avatar": "color"}, "status": "1"}]}}, "errMessageOnly": "green", "successMessage": "old", "errMessage": "after"};
}

module.exports = {
  generateResponseDataListSearchResponse
};
