
// ResponseUserKycResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseUserKycResponse模型的模拟数据
 * @returns {ResponseUserKycResponse} 模拟数据
 */
function generateResponseUserKycResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 168, "status": "1", "user": {"id": 916, "name": "hotel", "url": "form", "avatar": "everyone"}, "version": 879, "kycType": "1", "firstName": "hold", "lastName": "structure", "birthDate": "2006-03-25T06:42:50.999527", "idPic": "discover", "handsPic": "door"}, "result": {"code": "demand_can_not_select_bidder", "data": {"id": 345, "status": "1", "user": {"id": 76, "name": "though", "url": "door", "avatar": "small"}, "version": 496, "kycType": "1", "firstName": "must", "lastName": "brother", "birthDate": "1986-03-11T19:58:08.052247", "idPic": "place", "handsPic": "different"}}, "errMessageOnly": "reveal", "successMessage": "mother", "errMessage": "hundred"};
}

module.exports = {
  generateResponseUserKycResponse
};
