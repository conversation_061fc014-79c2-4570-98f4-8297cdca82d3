
// ResultDataListBlackListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListBlackListResponse模型的模拟数据
 * @returns {ResultDataListBlackListResponse} 模拟数据
 */
function generateResultDataListBlackListResponse() {
  return {"code": "badge_custom_url_exists", "data": {"curPage": 777, "maxPage": 97, "total": 8363, "data": [{"id": 615, "user": {"id": 444, "name": "billion", "url": "western", "avatar": "main"}, "limitType": "1", "reason": "nothing", "startTime": 1753433976}, {"id": 321, "user": {"id": 512, "name": "wait", "url": "down", "avatar": "nature"}, "limitType": "1", "reason": "rather", "startTime": 1753433976}, {"id": 828, "user": {"id": 675, "name": "whom", "url": "eight", "avatar": "lose"}, "limitType": "1", "reason": "power", "startTime": 1753433976}]}};
}

module.exports = {
  generateResultDataListBlackListResponse
};
