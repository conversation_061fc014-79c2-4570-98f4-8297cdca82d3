
// DataListStoreFaqSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListStoreFaqSimpleResponse模型的模拟数据
 * @returns {DataListStoreFaqSimpleResponse} 模拟数据
 */
function generateDataListStoreFaqSimpleResponse() {
  return {"curPage": 754, "maxPage": 607, "total": 2260, "data": [{"id": 24, "store": {"id": 25, "name": "understand", "url": "let", "avatar": "deep"}, "item": {"id": 31, "name": "artist", "url": "customer", "avatar": "so"}, "status": "1", "language": "en_US", "question": "know", "answer": "special", "sortIndex": 206, "createTime": 1753433976}]};
}

module.exports = {
  generateDataListStoreFaqSimpleResponse
};
