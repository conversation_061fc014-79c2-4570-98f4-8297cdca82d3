
// ResponseListStoreStaffPermissionResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseListStoreStaffPermissionResponse模型的模拟数据
 * @returns {ResponseListStoreStaffPermissionResponse} 模拟数据
 */
function generateResponseListStoreStaffPermissionResponse() {
  return {"code": 0, "msg": "success", "data": [{"code": "say", "name": "down"}, {"code": "player", "name": "yeah"}], "result": {"code": "demand_id_not_exists", "data": [{"code": "federal", "name": "first"}]}, "errMessageOnly": "tend", "successMessage": "perform", "errMessage": "step"};
}

module.exports = {
  generateResponseListStoreStaffPermissionResponse
};
