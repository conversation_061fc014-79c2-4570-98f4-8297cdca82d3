
// ScheduleAttendanceReportDTO 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ScheduleAttendanceReportDTO模型的模拟数据
 * @returns {ScheduleAttendanceReportDTO} 模拟数据
 */
function generateScheduleAttendanceReportDTO() {
  return {"manage": {"id": 316, "name": "for", "url": "collection", "avatar": "factor"}, "month": "2002-10-27T09:31:11.829867", "schedulingTimes": 1753433976, "scheduledWorkingHours": 817, "actualWorkingHours": 692, "signCount": 448, "timeOffWork": 1753433976, "missCount": 876, "askLeaveCount": 625, "leaveHours": 746};
}

module.exports = {
  generateScheduleAttendanceReportDTO
};
