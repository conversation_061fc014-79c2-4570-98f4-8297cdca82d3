
// ResponseManagePermissionsResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseManagePermissionsResponse模型的模拟数据
 * @returns {ResponseManagePermissionsResponse} 模拟数据
 */
function generateResponseManagePermissionsResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 803, "manageId": 652, "resourceId": 940, "addButtons": "spend", "addFields": "wife", "addUrls": "top", "removeButtons": "reason", "removeFields": "cup", "removeUrls": "task"}, "result": {"code": "promotion_id_not_exists", "data": {"id": 909, "manageId": 255, "resourceId": 465, "addButtons": "fast", "addFields": "blue", "addUrls": "food", "removeButtons": "head", "removeFields": "animal", "removeUrls": "research"}}, "errMessageOnly": "central", "successMessage": "or", "errMessage": "country"};
}

module.exports = {
  generateResponseManagePermissionsResponse
};
