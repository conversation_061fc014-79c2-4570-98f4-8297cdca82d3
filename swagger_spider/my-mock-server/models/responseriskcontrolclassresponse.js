
// ResponseRiskControlClassResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseRiskControlClassResponse模型的模拟数据
 * @returns {ResponseRiskControlClassResponse} 模拟数据
 */
function generateResponseRiskControlClassResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 572, "status": "1", "className": "country", "riskInit": {}, "riskExecuteClass": "market", "mark": "write", "createTime": 1753433976}, "result": {"code": "item_is_not_on_sale", "data": {"id": 405, "status": "1", "className": "building", "riskInit": {}, "riskExecuteClass": "special", "mark": "energy", "createTime": 1753433976}}, "errMessageOnly": "including", "successMessage": "more", "errMessage": "church"};
}

module.exports = {
  generateResponseRiskControlClassResponse
};
