
// RiskControlFinanceResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成RiskControlFinanceResponse模型的模拟数据
 * @returns {RiskControlFinanceResponse} 模拟数据
 */
function generateRiskControlFinanceResponse() {
  return {"id": 367, "status": "1", "riskName": "effect", "extraData": {}, "checkClass": "bank", "actionClass": "money", "riskCount": 263, "createTime": **********};
}

module.exports = {
  generateRiskControlFinanceResponse
};
