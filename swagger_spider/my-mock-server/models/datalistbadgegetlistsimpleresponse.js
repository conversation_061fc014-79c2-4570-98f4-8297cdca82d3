
// DataListBadgeGetListSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListBadgeGetListSimpleResponse模型的模拟数据
 * @returns {DataListBadgeGetListSimpleResponse} 模拟数据
 */
function generateDataListBadgeGetListSimpleResponse() {
  return {"curPage": 893, "maxPage": 221, "total": 2269, "data": [{"id": 763, "badgeType": "1", "badge": {"id": 798, "name": "others", "url": "show", "avatar": "character"}, "relation": {"id": 218, "name": "beat", "url": "campaign", "avatar": "head"}, "expireTime": 1753433976}]};
}

module.exports = {
  generateDataListBadgeGetListSimpleResponse
};
