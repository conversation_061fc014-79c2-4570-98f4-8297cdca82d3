
// DataListBrandFieldSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListBrandFieldSimpleResponse模型的模拟数据
 * @returns {DataListBrandFieldSimpleResponse} 模拟数据
 */
function generateDataListBrandFieldSimpleResponse() {
  return {"curPage": 753, "maxPage": 639, "total": 4702, "data": [{"id": 586, "brand": {"id": 494, "name": "financial", "url": "realize", "avatar": "speech"}, "fieldName": "discussion", "fieldType": "1", "required": "1", "language": "en_US", "label": "create", "defaultValue": "who", "placeholder": "democratic", "valueList": [{"key": "kind", "value": "those"}, {"key": "professional", "value": "magazine"}, {"key": "lot", "value": "western"}]}, {"id": 206, "brand": {"id": 104, "name": "huge", "url": "color", "avatar": "recognize"}, "fieldName": "southern", "fieldType": "1", "required": "1", "language": "en_US", "label": "film", "defaultValue": "size", "placeholder": "eat", "valueList": [{"key": "economy", "value": "west"}, {"key": "fill", "value": "team"}, {"key": "suddenly", "value": "central"}]}]};
}

module.exports = {
  generateDataListBrandFieldSimpleResponse
};
