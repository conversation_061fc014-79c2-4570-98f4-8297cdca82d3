
// ResultDataListStoreStatLogSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListStoreStatLogSimpleResponse模型的模拟数据
 * @returns {ResultDataListStoreStatLogSimpleResponse} 模拟数据
 */
function generateResultDataListStoreStatLogSimpleResponse() {
  return {"code": "user_email_activate_failed", "data": {"curPage": 657, "maxPage": 353, "total": 3079, "data": [{"id": 188, "date": "2003-12-05T17:24:48.601916", "store": {"id": 423, "name": "teach", "url": "imagine", "avatar": "wife"}, "visitCount": 316, "ordersCount": 993}, {"id": 439, "date": "2021-11-11T11:01:00.494473", "store": {"id": 869, "name": "spend", "url": "present", "avatar": "enjoy"}, "visitCount": 415, "ordersCount": 69}, {"id": 706, "date": "1971-03-16T11:49:51.773954", "store": {"id": 123, "name": "its", "url": "president", "avatar": "entire"}, "visitCount": 416, "ordersCount": 655}]}};
}

module.exports = {
  generateResultDataListStoreStatLogSimpleResponse
};
