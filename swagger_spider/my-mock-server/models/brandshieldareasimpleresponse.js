
// BrandShieldAreaSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成BrandShieldAreaSimpleResponse模型的模拟数据
 * @returns {BrandShieldAreaSimpleResponse} 模拟数据
 */
function generateBrandShieldAreaSimpleResponse() {
  return {"id": 901, "brand": {"id": 323, "name": "at", "url": "physical", "avatar": "him"}, "status": "1", "country": "change", "mark": "degree", "createTime": 1753433976};
}

module.exports = {
  generateBrandShieldAreaSimpleResponse
};
