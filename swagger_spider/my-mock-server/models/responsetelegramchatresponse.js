
// ResponseTelegramChatResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseTelegramChatResponse模型的模拟数据
 * @returns {ResponseTelegramChatResponse} 模拟数据
 */
function generateResponseTelegramChatResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 385, "telegramRobot": {"id": 423, "name": "reduce", "url": "compare", "avatar": "worry"}, "status": "1", "username": "no", "chatId": 25, "firstName": "white", "lastName": "go", "createTime": 1753433976}, "result": {"code": "aftersales_id_not_null", "data": {"id": 158, "telegramRobot": {"id": 798, "name": "card", "url": "hear", "avatar": "pressure"}, "status": "1", "username": "similar", "chatId": 861, "firstName": "major", "lastName": "girl", "createTime": 1753433976}}, "errMessageOnly": "similar", "successMessage": "up", "errMessage": "vote"};
}

module.exports = {
  generateResponseTelegramChatResponse
};
