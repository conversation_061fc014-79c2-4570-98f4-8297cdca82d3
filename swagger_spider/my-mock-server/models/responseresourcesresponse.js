
// ResponseResourcesResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseResourcesResponse模型的模拟数据
 * @returns {ResponseResourcesResponse} 模拟数据
 */
function generateResponseResourcesResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 521, "parentId": 841, "level": "1", "status": "1", "sortIndex": 402, "showStatus": "1", "resourcesKey": "third", "menuName": "people", "menuPath": "government", "createTime": 1753433976}, "result": {"code": "demand_publish_attr_list_not_empty", "data": {"id": 947, "parentId": 400, "level": "1", "status": "1", "sortIndex": 163, "showStatus": "1", "resourcesKey": "city", "menuName": "close", "menuPath": "paper", "createTime": 1753433976}}, "errMessageOnly": "explain", "successMessage": "section", "errMessage": "he"};
}

module.exports = {
  generateResponseResourcesResponse
};
