
// AttrValueSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成AttrValueSimpleResponse模型的模拟数据
 * @returns {AttrValueSimpleResponse} 模拟数据
 */
function generateAttrValueSimpleResponse() {
  return {"id": 896, "attr": {"id": 277, "name": "hard", "url": "future", "avatar": "something"}, "status": "1", "customUrl": "woman", "language": "en_US", "attrValue": "both", "updateTime": 1753433976, "createTime": 1753433976};
}

module.exports = {
  generateAttrValueSimpleResponse
};
