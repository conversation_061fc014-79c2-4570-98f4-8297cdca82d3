
// ResultDataListVipSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListVipSimpleResponse模型的模拟数据
 * @returns {ResultDataListVipSimpleResponse} 模拟数据
 */
function generateResultDataListVipSimpleResponse() {
  return {"code": "brand_lang_custom_url_exists", "data": {"curPage": 861, "maxPage": 369, "total": 4887, "data": [{"id": 377, "level": "1", "vipFrom": 663, "vipTo": 336, "language": "en_US"}]}};
}

module.exports = {
  generateResultDataListVipSimpleResponse
};
