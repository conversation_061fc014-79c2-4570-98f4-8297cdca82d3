
// ResultWebsiteDashboardOverviewResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultWebsiteDashboardOverviewResponse模型的模拟数据
 * @returns {ResultWebsiteDashboardOverviewResponse} 模拟数据
 */
function generateResultWebsiteDashboardOverviewResponse() {
  return {"code": "todo_list_add_failed", "data": {"websiteFlowResponse": {"websiteFlowCount": 5546, "websiteAccessCount": 5499, "loginDeviceCount": 9407, "onlineUserCount": 793}, "websiteUserCountResponse": {"websiteUserCount": 6988, "websitePageBounceRate": 521.98, "websiteSessionDurationCount": 5687, "websiteConversionRate": 226.07}, "websiteNewUserCountResponse": {"newUserCount": 5257, "activeUserCount": 7652, "todayPayUserCount": 6589, "yesterdayPayUserCount": 2636}, "websiteUserKYCCountResponse": {"todayTotalUserKYCCount": 8804, "authenticatedTodayTotalUserKYCCount": 574, "refuseAuthenticatedTodayTotalUserKYCCount": 8012, "newUserTodayTotalUserKYCCount": 9726}, "demandAuditingCountResponse": {"todayDemandAuditingCount": 2270, "todayNewDemandAuditingCount": 4449, "todayDemandAuditingPassCount": 9072, "todayDemandWinningCount": 6954}, "ordersCountResponse": {"todayOrdersCount": 355, "todayOrdersPendingCount": 3895, "todayOrdersAverageUnitPrice": 277, "todayOrdersAverageQuantity": 14}, "ordersRefundCountResponse": {"todayOrderRefundCount": 1411, "todayNewOrderRefundCount": 286, "todayOrderRefundCompletedCount": 8068, "todayOrderRefundRefusedCount": 6193}, "ordersExchangeCountResponse": {"todayOrderExchangeCount": 4447, "todayNewOrdersExchangeCount": 9317, "todayCompletedOrderExchangeCount": 8791, "todayRefuseOrderExchangeCount": 1771}, "ordersTicketCountResponse": {"todayOrdersTicketCount": 4400, "todayNewOrdersTicketCount": 2709, "todayCompletedOrdersTicketCount": 3232, "todayTimeoutOrdersTicketCount": 1753433976}, "customerComplaintCountResponse": {"todayComplaintCount": 6273, "todayNewComplaintCount": 6873, "todayCompletedComplaintCount": 7644}}};
}

module.exports = {
  generateResultWebsiteDashboardOverviewResponse
};
