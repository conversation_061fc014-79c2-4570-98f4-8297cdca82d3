
// ResultDataListManageLogsSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListManageLogsSimpleResponse模型的模拟数据
 * @returns {ResultDataListManageLogsSimpleResponse} 模拟数据
 */
function generateResultDataListManageLogsSimpleResponse() {
  return {"code": "store_stat_id_not_exists", "data": {"curPage": 623, "maxPage": 832, "total": 5319, "data": [{"id": 901, "manageId": 718, "manageUsername": "pick", "ip": 5933, "method": "business"}, {"id": 127, "manageId": 829, "manageUsername": "each", "ip": 4453, "method": "other"}]}};
}

module.exports = {
  generateResultDataListManageLogsSimpleResponse
};
