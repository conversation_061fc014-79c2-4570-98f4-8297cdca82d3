
// ResultDataListScheduleLeaveLogResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListScheduleLeaveLogResponse模型的模拟数据
 * @returns {ResultDataListScheduleLeaveLogResponse} 模拟数据
 */
function generateResultDataListScheduleLeaveLogResponse() {
  return {"code": "store_exists_by_user_id", "data": {"curPage": 227, "maxPage": 106, "total": 683, "data": [{"id": 275, "schedule": {"id": 831, "name": "necessary", "url": "before", "avatar": "remember"}, "manage": {"id": 606, "name": "operation", "url": "professional", "avatar": "smile"}, "status": "1", "statusName": "reach"}, {"id": 856, "schedule": {"id": 275, "name": "develop", "url": "various", "avatar": "every"}, "manage": {"id": 703, "name": "deep", "url": "return", "avatar": "value"}, "status": "1", "statusName": "pick"}]}};
}

module.exports = {
  generateResultDataListScheduleLeaveLogResponse
};
