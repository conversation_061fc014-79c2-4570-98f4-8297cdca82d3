
// PaymentResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成PaymentResponse模型的模拟数据
 * @returns {PaymentResponse} 模拟数据
 */
function generatePaymentResponse() {
  return {"sortIndex": "0", "paymentKey": "since", "dayLimitCount": 861, "dayLimitAmount": 701, "monthLimitCount": 961, "monthLimitAmount": 548, "minimum": 749, "maxmum": 578, "commissionFixConst": 197, "mark": "three"};
}

module.exports = {
  generatePaymentResponse
};
