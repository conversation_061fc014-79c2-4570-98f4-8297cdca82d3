
// UserLogsSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成UserLogsSimpleResponse模型的模拟数据
 * @returns {UserLogsSimpleResponse} 模拟数据
 */
function generateUserLogsSimpleResponse() {
  return {"id": 55, "user": {"id": 581, "name": "child", "url": "life", "avatar": "return"}, "method": "above", "module": "along", "action": "season", "ip": 777, "createTime": 1753433975};
}

module.exports = {
  generateUserLogsSimpleResponse
};
