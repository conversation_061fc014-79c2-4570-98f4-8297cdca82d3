
// ResultStoreFaqResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultStoreFaqResponse模型的模拟数据
 * @returns {ResultStoreFaqResponse} 模拟数据
 */
function generateResultStoreFaqResponse() {
  return {"code": "user_change_password_2fa_code_expired", "data": {"id": 978, "store": {"id": 802, "name": "view", "url": "mother", "avatar": "determine"}, "item": {"id": 88, "name": "true", "url": "before", "avatar": "price"}, "status": "1", "language": "en_US", "question": "show", "answer": "left", "sortIndex": 274, "createTime": 1753433976, "version": 438}};
}

module.exports = {
  generateResultStoreFaqResponse
};
