
// DataListDemandAttrListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListDemandAttrListResponse模型的模拟数据
 * @returns {DataListDemandAttrListResponse} 模拟数据
 */
function generateDataListDemandAttrListResponse() {
  return {"curPage": 38, "maxPage": 526, "total": 3391, "data": [{"id": 511, "demand": {"id": 993, "name": "often", "url": "effect", "avatar": "paper"}, "attr": {"id": 432, "name": "avoid", "url": "computer", "avatar": "again"}, "attrValue": {"id": 274, "name": "dinner", "url": "thank", "avatar": "such"}, "createTime": 1753433976}, {"id": 102, "demand": {"id": 198, "name": "play", "url": "her", "avatar": "may"}, "attr": {"id": 676, "name": "individual", "url": "subject", "avatar": "save"}, "attrValue": {"id": 230, "name": "message", "url": "up", "avatar": "push"}, "createTime": 1753433976}, {"id": 444, "demand": {"id": 797, "name": "water", "url": "age", "avatar": "five"}, "attr": {"id": 27, "name": "project", "url": "firm", "avatar": "why"}, "attrValue": {"id": 564, "name": "what", "url": "mission", "avatar": "student"}, "createTime": 1753433976}]};
}

module.exports = {
  generateDataListDemandAttrListResponse
};
