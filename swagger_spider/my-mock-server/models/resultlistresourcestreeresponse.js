
// ResultListResourcesTreeResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultListResourcesTreeResponse模型的模拟数据
 * @returns {ResultListResourcesTreeResponse} 模拟数据
 */
function generateResultListResourcesTreeResponse() {
  return {"code": "campaign_item_list_update_error", "data": [{"id": 285, "parentId": 915, "level": "1", "status": "1", "sortIndex": 952, "showStatus": "1", "resourcesKey": "great", "menuName": "yet", "menuPath": "leg"}, {"id": 159, "parentId": 709, "level": "1", "status": "1", "sortIndex": 181, "showStatus": "1", "resourcesKey": "political", "menuName": "sing", "menuPath": "agent"}]};
}

module.exports = {
  generateResultListResourcesTreeResponse
};
