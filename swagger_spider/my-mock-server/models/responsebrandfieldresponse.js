
// ResponseBrandFieldResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseBrandFieldResponse模型的模拟数据
 * @returns {ResponseBrandFieldResponse} 模拟数据
 */
function generateResponseBrandFieldResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 366, "brandId": 558, "fieldName": "focus", "fieldType": "1", "required": "1", "createTime": 1753433976, "updateTime": 1753433976, "brandFieldLangId": 811, "language": "en_US", "label": "pull"}, "result": {"code": "orders_comment_exists", "data": {"id": 856, "brandId": 421, "fieldName": "require", "fieldType": "1", "required": "1", "createTime": 1753433976, "updateTime": 1753433976, "brandFieldLangId": 609, "language": "en_US", "label": "director"}}, "errMessageOnly": "property", "successMessage": "decision", "errMessage": "great"};
}

module.exports = {
  generateResponseBrandFieldResponse
};
