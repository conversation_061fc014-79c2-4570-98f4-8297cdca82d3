
// ResultDataListBrandServicesListSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListBrandServicesListSimpleResponse模型的模拟数据
 * @returns {ResultDataListBrandServicesListSimpleResponse} 模拟数据
 */
function generateResultDataListBrandServicesListSimpleResponse() {
  return {"code": "store_staff_add_failed", "data": {"curPage": 301, "maxPage": 220, "total": 1722, "data": [{"id": 708, "brand": {"id": 318, "name": "white", "url": "goal", "avatar": "some"}, "services": {"id": 41, "name": "rule", "url": "cost", "avatar": "budget"}, "createTime": 1753433976, "fieldList": [{"label": "example", "fieldType": "international", "fieldName": "news", "required": true, "defaultValue": "because"}, {"label": "tough", "fieldType": "effort", "fieldName": "from", "required": false, "defaultValue": "word"}]}, {"id": 676, "brand": {"id": 566, "name": "have", "url": "million", "avatar": "maintain"}, "services": {"id": 396, "name": "simple", "url": "food", "avatar": "them"}, "createTime": 1753433976, "fieldList": [{"label": "attention", "fieldType": "democratic", "fieldName": "friend", "required": false, "defaultValue": "agency"}, {"label": "wide", "fieldType": "least", "fieldName": "effect", "required": false, "defaultValue": "green"}]}]}};
}

module.exports = {
  generateResultDataListBrandServicesListSimpleResponse
};
