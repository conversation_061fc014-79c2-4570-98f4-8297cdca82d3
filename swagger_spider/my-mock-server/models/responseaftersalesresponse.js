
// ResponseAftersalesResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseAftersalesResponse模型的模拟数据
 * @returns {ResponseAftersalesResponse} 模拟数据
 */
function generateResponseAftersalesResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 649, "brand": {"id": 556, "name": "me", "url": "Democrat", "avatar": "series"}, "aftersalesType": {"id": 138, "name": "ask", "url": "within", "avatar": "find"}, "status": "1", "statusName": "song", "createTime": 1753433976, "updateTime": 1753433976, "language": "en_US", "reason": "identify"}, "result": {"code": "orders_comment_like_not_null", "data": {"id": 661, "brand": {"id": 991, "name": "figure", "url": "far", "avatar": "condition"}, "aftersalesType": {"id": 945, "name": "next", "url": "trip", "avatar": "purpose"}, "status": "1", "statusName": "process", "createTime": 1753433976, "updateTime": 1753433976, "language": "en_US", "reason": "our"}}, "errMessageOnly": "stock", "successMessage": "life", "errMessage": "some"};
}

module.exports = {
  generateResponseAftersalesResponse
};
