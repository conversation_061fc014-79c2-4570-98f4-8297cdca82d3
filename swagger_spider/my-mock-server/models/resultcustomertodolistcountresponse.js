
// ResultCustomerTodoListCountResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultCustomerTodoListCountResponse模型的模拟数据
 * @returns {ResultCustomerTodoListCountResponse} 模拟数据
 */
function generateResultCustomerTodoListCountResponse() {
  return {"code": "faq_id_not_null", "data": {"articleAuditTaskCount": 8660, "kycAuditTaskCount": 4954, "storeAuditTaskCount": 562, "faqAuditTaskCount": 5734, "ticketHandlingTaskCount": 2533, "demandAuditTaskCount": 6640, "blogTaskCount": 1117, "complaintAuditTaskCount": 9951, "itemAuditTaskCount": 2475, "violationPenaltyTaskCount": 2569}};
}

module.exports = {
  generateResultCustomerTodoListCountResponse
};
