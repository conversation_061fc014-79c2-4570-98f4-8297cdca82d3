
// ResponseMainResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseMainResponse模型的模拟数据
 * @returns {ResponseMainResponse} 模拟数据
 */
function generateResponseMainResponse() {
  return {"code": 0, "msg": "success", "data": {"nodeName": "church", "version": {"number": "course", "buildFlavor": "pass", "buildType": "sign", "buildHash": "church", "buildDate": "base", "luceneVersion": "now", "minimumWireCompatibilityVersion": "treat", "minimumIndexCompatibilityVersion": "Mr", "snapshot": false}, "clusterName": "perhaps", "clusterUuid": "decade", "tagline": "us"}, "result": {"code": "quick_message_batch_add_failure", "data": {"nodeName": "population", "version": {"number": "son", "buildFlavor": "coach", "buildType": "sit", "buildHash": "reality", "buildDate": "condition"}, "clusterName": "painting", "clusterUuid": "young", "tagline": "note"}}, "errMessageOnly": "when", "successMessage": "such", "errMessage": "off"};
}

module.exports = {
  generateResponseMainResponse
};
