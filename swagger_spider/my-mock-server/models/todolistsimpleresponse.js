
// TodoListSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成TodoListSimpleResponse模型的模拟数据
 * @returns {TodoListSimpleResponse} 模拟数据
 */
function generateTodoListSimpleResponse() {
  return {"id": 641, "taskType": "1", "status": "1", "manage": {"id": 861, "name": "represent", "url": "weight", "avatar": "personal"}, "prioritySort": 156, "taskName": "stop", "summary": "pattern", "createTime": 1753433975, "updateTime": 1753433975};
}

module.exports = {
  generateTodoListSimpleResponse
};
