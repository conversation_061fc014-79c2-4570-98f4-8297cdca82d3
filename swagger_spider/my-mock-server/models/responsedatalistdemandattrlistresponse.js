
// ResponseDataListDemandAttrListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListDemandAttrListResponse模型的模拟数据
 * @returns {ResponseDataListDemandAttrListResponse} 模拟数据
 */
function generateResponseDataListDemandAttrListResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 5, "maxPage": 428, "total": 6726, "data": [{"id": 253, "demand": {"id": 959, "name": "exist", "url": "production", "avatar": "mission"}, "attr": {"id": 433, "name": "act", "url": "group", "avatar": "simple"}, "attrValue": {"id": 80, "name": "between", "url": "country", "avatar": "media"}, "createTime": 1753433976}, {"id": 699, "demand": {"id": 457, "name": "page", "url": "born", "avatar": "never"}, "attr": {"id": 143, "name": "step", "url": "former", "avatar": "trade"}, "attrValue": {"id": 239, "name": "participant", "url": "girl", "avatar": "allow"}, "createTime": 1753433976}, {"id": 558, "demand": {"id": 200, "name": "must", "url": "case", "avatar": "see"}, "attr": {"id": 483, "name": "no", "url": "find", "avatar": "now"}, "attrValue": {"id": 934, "name": "offer", "url": "might", "avatar": "tonight"}, "createTime": 1753433976}]}, "result": {"code": "rules_add_error", "data": {"curPage": 626, "maxPage": 737, "total": 3554, "data": [{"id": 998, "demand": {"id": 474, "name": "own", "url": "gun", "avatar": "leader"}, "attr": {"id": 471, "name": "five", "url": "history", "avatar": "computer"}, "attrValue": {"id": 860, "name": "staff", "url": "production", "avatar": "it"}, "createTime": 1753433976}, {"id": 710, "demand": {"id": 823, "name": "still", "url": "gun", "avatar": "could"}, "attr": {"id": 156, "name": "project", "url": "seat", "avatar": "age"}, "attrValue": {"id": 773, "name": "detail", "url": "little", "avatar": "point"}, "createTime": 1753433976}, {"id": 300, "demand": {"id": 639, "name": "these", "url": "challenge", "avatar": "effect"}, "attr": {"id": 167, "name": "than", "url": "thing", "avatar": "cut"}, "attrValue": {"id": 14, "name": "for", "url": "human", "avatar": "quickly"}, "createTime": 1753433976}]}}, "errMessageOnly": "eye", "successMessage": "usually", "errMessage": "cost"};
}

module.exports = {
  generateResponseDataListDemandAttrListResponse
};
