
// DataListItemSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListItemSimpleResponse模型的模拟数据
 * @returns {DataListItemSimpleResponse} 模拟数据
 */
function generateDataListItemSimpleResponse() {
  return {"curPage": 692, "maxPage": 787, "total": 5638, "data": [{"id": 630, "user": {"id": 507, "name": "late", "url": "next", "avatar": "information"}, "store": {"id": 484, "name": "add", "url": "station", "avatar": "little"}, "brand": {"id": 362, "name": "save", "url": "behind", "avatar": "instead"}, "services": {"id": 931, "name": "sort", "url": "provide", "avatar": "rest"}, "tagList": [{"id": 425, "name": "wide", "url": "seat", "avatar": "answer"}, {"id": 979, "name": "lawyer", "url": "interesting", "avatar": "deep"}, {"id": 225, "name": "seat", "url": "do", "avatar": "case"}], "attrList": [{"attrId": 914, "attrName": "coach", "attrValueId": 187, "attrValueName": "protect"}, {"attrId": 28, "attrName": "piece", "attrValueId": 970, "attrValueName": "police"}, {"attrId": 767, "attrName": "parent", "attrValueId": 238, "attrValueName": "writer"}], "status": "1", "language": "en_US", "name": "as"}, {"id": 400, "user": {"id": 714, "name": "short", "url": "professional", "avatar": "become"}, "store": {"id": 312, "name": "hard", "url": "amount", "avatar": "always"}, "brand": {"id": 11, "name": "environmental", "url": "executive", "avatar": "two"}, "services": {"id": 759, "name": "walk", "url": "according", "avatar": "such"}, "tagList": [{"id": 595, "name": "how", "url": "thought", "avatar": "she"}, {"id": 292, "name": "daughter", "url": "give", "avatar": "today"}], "attrList": [{"attrId": 288, "attrName": "sense", "attrValueId": 761, "attrValueName": "college"}, {"attrId": 172, "attrName": "how", "attrValueId": 956, "attrValueName": "take"}, {"attrId": 455, "attrName": "away", "attrValueId": 457, "attrValueName": "hand"}], "status": "1", "language": "en_US", "name": "can"}]};
}

module.exports = {
  generateDataListItemSimpleResponse
};
