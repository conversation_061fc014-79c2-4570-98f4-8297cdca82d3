
// ResultDataListPagesTypeResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListPagesTypeResponse模型的模拟数据
 * @returns {ResultDataListPagesTypeResponse} 模拟数据
 */
function generateResultDataListPagesTypeResponse() {
  return {"code": "campaign_tag_list_update_error", "data": {"curPage": 862, "maxPage": 174, "total": 6039, "data": [{"id": 818, "status": "1", "statusName": "mean", "langStatus": "1", "langStatusName": "future"}]}};
}

module.exports = {
  generateResultDataListPagesTypeResponse
};
