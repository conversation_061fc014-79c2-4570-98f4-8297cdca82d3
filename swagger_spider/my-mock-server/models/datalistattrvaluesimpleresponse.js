
// DataListAttrValueSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListAttrValueSimpleResponse模型的模拟数据
 * @returns {DataListAttrValueSimpleResponse} 模拟数据
 */
function generateDataListAttrValueSimpleResponse() {
  return {"curPage": 112, "maxPage": 269, "total": 3266, "data": [{"id": 511, "attr": {"id": 149, "name": "money", "url": "for", "avatar": "property"}, "status": "1", "customUrl": "toward", "language": "en_US", "attrValue": "alone", "updateTime": 1753433976, "createTime": 1753433976}, {"id": 99, "attr": {"id": 516, "name": "make", "url": "help", "avatar": "own"}, "status": "1", "customUrl": "range", "language": "en_US", "attrValue": "now", "updateTime": 1753433976, "createTime": 1753433976}]};
}

module.exports = {
  generateDataListAttrValueSimpleResponse
};
