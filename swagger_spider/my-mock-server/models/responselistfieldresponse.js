
// ResponseListFieldResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseListFieldResponse模型的模拟数据
 * @returns {ResponseListFieldResponse} 模拟数据
 */
function generateResponseListFieldResponse() {
  return {"code": 0, "msg": "success", "data": [{"label": "outside", "fieldType": "arrive", "fieldName": "but", "required": false, "defaultValue": "civil", "placeholder": "quality", "valueList": [{}, {}, {}]}], "result": {"code": "search_list_error", "data": [{"label": "pay", "fieldType": "beat", "fieldName": "attack", "required": false, "defaultValue": "true"}]}, "errMessageOnly": "commercial", "successMessage": "put", "errMessage": "answer"};
}

module.exports = {
  generateResponseListFieldResponse
};
