// DynamicswaggerResources_4Response 模型
// 由SwaggerCrawler动态生成

/**
 * 生成DynamicswaggerResources_4Response模型的模拟数据
 * @returns {DynamicswaggerResources_4Response} 模拟数据
 */
function generateDynamicswaggerResources_4Response() {
  return [{"name": "own","url": "hope","swaggerVersion": "result","location": "federal"},{"name": "leader","url": "civil","swaggerVersion": "view","location": "population"},{"name": "several","url": "guess","swaggerVersion": "carry","location": "dark"}];
}

module.exports = {
  generateDynamicswaggerResources_4Response
};
