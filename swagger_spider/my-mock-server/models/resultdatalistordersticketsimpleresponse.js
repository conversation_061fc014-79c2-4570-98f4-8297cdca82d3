
// ResultDataListOrdersTicketSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListOrdersTicketSimpleResponse模型的模拟数据
 * @returns {ResultDataListOrdersTicketSimpleResponse} 模拟数据
 */
function generateResultDataListOrdersTicketSimpleResponse() {
  return {"code": "pages_type_id_not_exists", "data": {"curPage": 423, "maxPage": 987, "total": 3924, "data": [{"id": 210, "status": "1", "ordersId": 194, "user": {"id": 364, "name": "around", "url": "break", "avatar": "indeed"}, "store": {"id": 929, "name": "term", "url": "though", "avatar": "others"}}, {"id": 115, "status": "1", "ordersId": 861, "user": {"id": 101, "name": "shake", "url": "this", "avatar": "create"}, "store": {"id": 535, "name": "wait", "url": "tell", "avatar": "agree"}}]}};
}

module.exports = {
  generateResultDataListOrdersTicketSimpleResponse
};
