
// ResultDataListPaymentRecordSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListPaymentRecordSimpleResponse模型的模拟数据
 * @returns {ResultDataListPaymentRecordSimpleResponse} 模拟数据
 */
function generateResultDataListPaymentRecordSimpleResponse() {
  return {"code": "page_size_invalid", "data": {"curPage": 341, "maxPage": 914, "total": 9369, "data": [{"id": 251, "user": {"id": 230, "name": "able", "url": "same", "avatar": "well"}, "payment": {"id": 730, "name": "former", "url": "tend", "avatar": "wish"}, "transactionId": "table", "amount": 467}, {"id": 9, "user": {"id": 538, "name": "challenge", "url": "rock", "avatar": "listen"}, "payment": {"id": 698, "name": "institution", "url": "to", "avatar": "certainly"}, "transactionId": "site", "amount": 18}, {"id": 903, "user": {"id": 57, "name": "look", "url": "technology", "avatar": "mission"}, "payment": {"id": 807, "name": "rather", "url": "raise", "avatar": "manager"}, "transactionId": "help", "amount": 63}]}};
}

module.exports = {
  generateResultDataListPaymentRecordSimpleResponse
};
