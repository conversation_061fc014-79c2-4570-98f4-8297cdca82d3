
// DataListPagesPageResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListPagesPageResponse模型的模拟数据
 * @returns {DataListPagesPageResponse} 模拟数据
 */
function generateDataListPagesPageResponse() {
  return {"curPage": 746, "maxPage": 459, "total": 2222, "data": [{"id": 746, "manage": {"id": 479, "name": "book", "url": "research", "avatar": "cover"}, "pagesType": {"id": 198, "name": "to", "url": "course", "avatar": "cultural"}, "statusName": "image", "language": "en_US", "title": "house", "customUrl": "save", "coverPic": "majority", "createTime": 1753433976}, {"id": 355, "manage": {"id": 691, "name": "firm", "url": "partner", "avatar": "coach"}, "pagesType": {"id": 681, "name": "structure", "url": "hold", "avatar": "station"}, "statusName": "leader", "language": "en_US", "title": "loss", "customUrl": "difference", "coverPic": "suddenly", "createTime": 1753433976}, {"id": 3, "manage": {"id": 799, "name": "wait", "url": "send", "avatar": "because"}, "pagesType": {"id": 680, "name": "become", "url": "without", "avatar": "action"}, "statusName": "hot", "language": "en_US", "title": "else", "customUrl": "another", "coverPic": "movement", "createTime": 1753433976}]};
}

module.exports = {
  generateDataListPagesPageResponse
};
