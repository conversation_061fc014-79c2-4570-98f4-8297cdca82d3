
// ResponseDataListPaymentRecordSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListPaymentRecordSimpleResponse模型的模拟数据
 * @returns {ResponseDataListPaymentRecordSimpleResponse} 模拟数据
 */
function generateResponseDataListPaymentRecordSimpleResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 190, "maxPage": 94, "total": 60, "data": [{"id": 818, "user": {"id": 23, "name": "agreement", "url": "local", "avatar": "system"}, "payment": {"id": 11, "name": "through", "url": "study", "avatar": "kitchen"}, "transactionId": "consumer", "amount": 367}, {"id": 146, "user": {"id": 466, "name": "federal", "url": "agreement", "avatar": "ok"}, "payment": {"id": 148, "name": "that", "url": "do", "avatar": "bring"}, "transactionId": "peace", "amount": 160}, {"id": 198, "user": {"id": 787, "name": "administration", "url": "growth", "avatar": "last"}, "payment": {"id": 543, "name": "fear", "url": "society", "avatar": "reality"}, "transactionId": "book", "amount": 701}]}, "result": {"code": "del_error", "data": {"curPage": 391, "maxPage": 819, "total": 5017, "data": [{"id": 62, "user": {"id": 433, "name": "letter", "url": "national", "avatar": "his"}, "payment": {"id": 625, "name": "arrive", "url": "pressure", "avatar": "heart"}, "transactionId": "until", "amount": 14}, {"id": 3, "user": {"id": 119, "name": "catch", "url": "everything", "avatar": "lot"}, "payment": {"id": 367, "name": "spend", "url": "fill", "avatar": "safe"}, "transactionId": "individual", "amount": 380}]}}, "errMessageOnly": "take", "successMessage": "until", "errMessage": "environmental"};
}

module.exports = {
  generateResponseDataListPaymentRecordSimpleResponse
};
