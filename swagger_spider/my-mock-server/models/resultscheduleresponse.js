
// ResultScheduleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultScheduleResponse模型的模拟数据
 * @returns {ResultScheduleResponse} 模拟数据
 */
function generateResultScheduleResponse() {
  return {"code": "search_brand_id_invalid", "data": {"id": 687, "status": "1", "statusName": "boy", "date": "prepare", "from": "employee", "to": "dog", "manage": {"id": 423, "name": "throw", "url": "time", "avatar": "common"}, "trueManage": {"id": 531, "name": "I", "url": "push", "avatar": "community"}, "workStart": "best", "workEnd": "enter"}};
}

module.exports = {
  generateResultScheduleResponse
};
