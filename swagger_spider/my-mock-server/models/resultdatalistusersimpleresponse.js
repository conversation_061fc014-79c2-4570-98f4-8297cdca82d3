
// ResultDataListUserSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListUserSimpleResponse模型的模拟数据
 * @returns {ResultDataListUserSimpleResponse} 模拟数据
 */
function generateResultDataListUserSimpleResponse() {
  return {"code": "send_fail", "data": {"curPage": 436, "maxPage": 310, "total": 4201, "data": [{"id": 319, "username": "especially", "email": "father", "status": 192, "verified": 217}, {"id": 905, "username": "situation", "email": "hospital", "status": 263, "verified": 499}, {"id": 970, "username": "none", "email": "system", "status": 254, "verified": 422}]}};
}

module.exports = {
  generateResultDataListUserSimpleResponse
};
