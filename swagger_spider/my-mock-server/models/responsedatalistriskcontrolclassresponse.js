
// ResponseDataListRiskControlClassResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListRiskControlClassResponse模型的模拟数据
 * @returns {ResponseDataListRiskControlClassResponse} 模拟数据
 */
function generateResponseDataListRiskControlClassResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 469, "maxPage": 574, "total": 9977, "data": [{"id": 319, "status": "1", "className": "include", "riskInit": {}, "riskExecuteClass": "thus"}, {"id": 813, "status": "1", "className": "air", "riskInit": {}, "riskExecuteClass": "walk"}, {"id": 453, "status": "1", "className": "question", "riskInit": {}, "riskExecuteClass": "into"}]}, "result": {"code": "schedule_time_range_error", "data": {"curPage": 8, "maxPage": 31, "total": 4414, "data": [{"id": 839, "status": "1", "className": "firm", "riskInit": {}, "riskExecuteClass": "north"}, {"id": 201, "status": "1", "className": "similar", "riskInit": {}, "riskExecuteClass": "song"}]}}, "errMessageOnly": "tough", "successMessage": "as", "errMessage": "rule"};
}

module.exports = {
  generateResponseDataListRiskControlClassResponse
};
