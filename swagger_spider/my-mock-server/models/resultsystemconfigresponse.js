
// ResultSystemConfigResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultSystemConfigResponse模型的模拟数据
 * @returns {ResultSystemConfigResponse} 模拟数据
 */
function generateResultSystemConfigResponse() {
  return {"code": "pages_type_id_can_not_be_null", "data": {"id": 87, "configKey": "rest", "configClass": "work", "configValue": "worker", "mark": "brother", "createTime": 1753433976, "updateTime": 1753433976}};
}

module.exports = {
  generateResultSystemConfigResponse
};
