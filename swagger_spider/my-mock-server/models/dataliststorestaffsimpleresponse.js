
// DataListStoreStaffSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListStoreStaffSimpleResponse模型的模拟数据
 * @returns {DataListStoreStaffSimpleResponse} 模拟数据
 */
function generateDataListStoreStaffSimpleResponse() {
  return {"curPage": 999, "maxPage": 78, "total": 6254, "data": [{"id": 33, "store": {"id": 27, "name": "protect", "url": "something", "avatar": "human"}, "user": {"id": 235, "name": "according", "url": "find", "avatar": "window"}, "status": "1", "nickName": "smile", "avatar": "action", "phone": "after", "permissions": "face", "createTime": 1753433976}, {"id": 314, "store": {"id": 75, "name": "concern", "url": "look", "avatar": "or"}, "user": {"id": 714, "name": "visit", "url": "material", "avatar": "fast"}, "status": "1", "nickName": "west", "avatar": "young", "phone": "especially", "permissions": "either", "createTime": 1753433976}]};
}

module.exports = {
  generateDataListStoreStaffSimpleResponse
};
