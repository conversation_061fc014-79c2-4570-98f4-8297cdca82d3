
// DataListRiskControlClassResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListRiskControlClassResponse模型的模拟数据
 * @returns {DataListRiskControlClassResponse} 模拟数据
 */
function generateDataListRiskControlClassResponse() {
  return {"curPage": 993, "maxPage": 263, "total": 2564, "data": [{"id": 632, "status": "1", "className": "space", "riskInit": {}, "riskExecuteClass": "lay", "mark": "right", "createTime": 1753433976}, {"id": 161, "status": "1", "className": "guess", "riskInit": {}, "riskExecuteClass": "feeling", "mark": "marriage", "createTime": 1753433976}]};
}

module.exports = {
  generateDataListRiskControlClassResponse
};
