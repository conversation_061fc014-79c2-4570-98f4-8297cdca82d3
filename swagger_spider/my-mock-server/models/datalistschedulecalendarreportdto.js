
// DataListScheduleCalendarReportDTO 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListScheduleCalendarReportDTO模型的模拟数据
 * @returns {DataListScheduleCalendarReportDTO} 模拟数据
 */
function generateDataListScheduleCalendarReportDTO() {
  return {"curPage": 119, "maxPage": 859, "total": 1101, "data": [{"date": "1973-06-15T06:30:17.476091", "schedules": [{"from": "network", "to": "pick", "manageId": 487, "trueManageId": 565, "scheduleId": 258}]}, {"date": "2002-10-08T01:30:09.661978", "schedules": [{"from": "decision", "to": "how", "manageId": 540, "trueManageId": 609, "scheduleId": 654}, {"from": "news", "to": "other", "manageId": 640, "trueManageId": 355, "scheduleId": 127}]}]};
}

module.exports = {
  generateDataListScheduleCalendarReportDTO
};
