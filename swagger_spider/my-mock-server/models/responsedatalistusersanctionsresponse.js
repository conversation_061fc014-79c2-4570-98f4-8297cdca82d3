
// ResponseDataListUserSanctionsResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListUserSanctionsResponse模型的模拟数据
 * @returns {ResponseDataListUserSanctionsResponse} 模拟数据
 */
function generateResponseDataListUserSanctionsResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 211, "maxPage": 549, "total": 4896, "data": [{"id": 685, "violationsEventId": 389, "user": {"id": 488, "name": "another", "url": "help", "avatar": "fast"}, "status": "1", "sanctionType": "1"}, {"id": 629, "violationsEventId": 92, "user": {"id": 847, "name": "simple", "url": "key", "avatar": "collection"}, "status": "1", "sanctionType": "1"}, {"id": 896, "violationsEventId": 94, "user": {"id": 769, "name": "business", "url": "range", "avatar": "interesting"}, "status": "1", "sanctionType": "1"}]}, "result": {"code": "brand_services_list_id_not_exists", "data": {"curPage": 676, "maxPage": 463, "total": 9853, "data": [{"id": 441, "violationsEventId": 981, "user": {"id": 51, "name": "open", "url": "interest", "avatar": "program"}, "status": "1", "sanctionType": "1"}, {"id": 554, "violationsEventId": 171, "user": {"id": 142, "name": "fund", "url": "lay", "avatar": "challenge"}, "status": "1", "sanctionType": "1"}, {"id": 77, "violationsEventId": 498, "user": {"id": 230, "name": "imagine", "url": "my", "avatar": "eat"}, "status": "1", "sanctionType": "1"}]}}, "errMessageOnly": "case", "successMessage": "explain", "errMessage": "one"};
}

module.exports = {
  generateResponseDataListUserSanctionsResponse
};
