
// ResultScheduleLeaveLogResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultScheduleLeaveLogResponse模型的模拟数据
 * @returns {ResultScheduleLeaveLogResponse} 模拟数据
 */
function generateResultScheduleLeaveLogResponse() {
  return {"code": "schedule_sign_log_can_not_delete", "data": {"id": 635, "schedule": {"id": 228, "name": "beyond", "url": "international", "avatar": "none"}, "manage": {"id": 691, "name": "choose", "url": "reduce", "avatar": "scene"}, "status": "1", "statusName": "few", "askTime": "2012-02-19T13:56:30.587376", "leaveTime": "1978-01-18T17:05:01.472710", "backTime": "2021-11-26T01:49:40.001835", "leaveType": "1", "leaveTypeName": "far"}};
}

module.exports = {
  generateResultScheduleLeaveLogResponse
};
