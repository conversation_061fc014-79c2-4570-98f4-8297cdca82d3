
// PostsTypeResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成PostsTypeResponse模型的模拟数据
 * @returns {PostsTypeResponse} 模拟数据
 */
function generatePostsTypeResponse() {
  return {"id": 148, "status": "1", "language": "en_US", "typeName": "ability", "customUrl": "allow", "coverPic": "affect", "createTime": 1753433976, "parentId": 614, "seoKeywords": "speak", "seoDescription": "cut"};
}

module.exports = {
  generatePostsTypeResponse
};
