
// QuickMessageSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成QuickMessageSimpleResponse模型的模拟数据
 * @returns {QuickMessageSimpleResponse} 模拟数据
 */
function generateQuickMessageSimpleResponse() {
  return {"id": 460, "quickMessageTypeId": 459, "quickMessageTypeName": "anyone", "manageId": 219, "manageName": "position", "quickMessageLangId": 135, "language": "en_US", "message": "stop"};
}

module.exports = {
  generateQuickMessageSimpleResponse
};
