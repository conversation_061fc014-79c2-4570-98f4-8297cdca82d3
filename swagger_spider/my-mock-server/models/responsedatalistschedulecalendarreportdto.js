
// ResponseDataListScheduleCalendarReportDTO 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListScheduleCalendarReportDTO模型的模拟数据
 * @returns {ResponseDataListScheduleCalendarReportDTO} 模拟数据
 */
function generateResponseDataListScheduleCalendarReportDTO() {
  return {"code": 0, "msg": "success", "data": {"curPage": 408, "maxPage": 566, "total": 4426, "data": [{"date": "1982-01-23T00:53:41.079753", "schedules": [{"from": "whatever", "to": "station", "manageId": 731, "trueManageId": 158, "scheduleId": 866}, {"from": "everything", "to": "seat", "manageId": 12, "trueManageId": 454, "scheduleId": 261}]}]}, "result": {"code": "coupon_id_not_exists", "data": {"curPage": 756, "maxPage": 860, "total": 265, "data": [{"date": "1998-12-03T05:14:16.798077", "schedules": [{"from": "gas", "to": "century", "manageId": 847, "trueManageId": 382, "scheduleId": 532}, {"from": "save", "to": "pay", "manageId": 829, "trueManageId": 12, "scheduleId": 338}]}, {"date": "2012-01-23T13:38:28.404496", "schedules": [{"from": "debate", "to": "school", "manageId": 944, "trueManageId": 327, "scheduleId": 97}, {"from": "law", "to": "structure", "manageId": 388, "trueManageId": 204, "scheduleId": 120}]}, {"date": "1998-07-06T10:27:53.689423", "schedules": [{"from": "later", "to": "try", "manageId": 436, "trueManageId": 80, "scheduleId": 368}, {"from": "rule", "to": "walk", "manageId": 658, "trueManageId": 369, "scheduleId": 857}]}]}}, "errMessageOnly": "others", "successMessage": "ask", "errMessage": "statement"};
}

module.exports = {
  generateResponseDataListScheduleCalendarReportDTO
};
