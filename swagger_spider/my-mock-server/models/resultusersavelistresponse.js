
// ResultUserSaveListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultUserSaveListResponse模型的模拟数据
 * @returns {ResultUserSaveListResponse} 模拟数据
 */
function generateResultUserSaveListResponse() {
  return {"code": "manage_telegram_exists", "data": {"id": 55, "user": {"id": 118, "name": "spring", "url": "section", "avatar": "at"}, "saveType": "1", "relationId": 207, "createTime": 1753433975}};
}

module.exports = {
  generateResultUserSaveListResponse
};
