
// ResultDataListRiskControlClassResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListRiskControlClassResponse模型的模拟数据
 * @returns {ResultDataListRiskControlClassResponse} 模拟数据
 */
function generateResultDataListRiskControlClassResponse() {
  return {"code": "pages_type_batch_update_error", "data": {"curPage": 529, "maxPage": 295, "total": 9173, "data": [{"id": 672, "status": "1", "className": "suffer", "riskInit": {}, "riskExecuteClass": "above"}, {"id": 151, "status": "1", "className": "response", "riskInit": {}, "riskExecuteClass": "interesting"}]}};
}

module.exports = {
  generateResultDataListRiskControlClassResponse
};
