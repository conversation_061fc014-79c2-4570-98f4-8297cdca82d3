
// ScheduleLeaveLogRequest 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ScheduleLeaveLogRequest模型的模拟数据
 * @returns {ScheduleLeaveLogRequest} 模拟数据
 */
function generateScheduleLeaveLogRequest() {
  return {"scheduleId": 204, "manageId": 317, "status": "REJECTED", "askTime": "2007-06-30T02:48:37.491684", "leaveTime": "1996-03-05T20:41:57.134298", "backTime": "1991-11-22T10:25:12.071662", "leaveType": "ANNUAL", "reason": "where", "mark": "question"};
}

module.exports = {
  generateScheduleLeaveLogRequest
};
