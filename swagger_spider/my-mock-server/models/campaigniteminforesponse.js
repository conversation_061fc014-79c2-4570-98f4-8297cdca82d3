
// CampaignItemInfoResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成CampaignItemInfoResponse模型的模拟数据
 * @returns {CampaignItemInfoResponse} 模拟数据
 */
function generateCampaignItemInfoResponse() {
  return {"id": 371, "item": {"id": 680, "name": "audience", "url": "about", "avatar": "nor"}, "store": {"id": 903, "name": "person", "url": "measure", "avatar": "religious"}};
}

module.exports = {
  generateCampaignItemInfoResponse
};
