
// DataListRulesSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListRulesSimpleResponse模型的模拟数据
 * @returns {DataListRulesSimpleResponse} 模拟数据
 */
function generateDataListRulesSimpleResponse() {
  return {"curPage": 703, "maxPage": 861, "total": 6717, "data": [{"id": 189, "status": "1", "rulesType": "1", "indexNumber": "add", "createTime": 1753433976, "language": "en_US", "content": "what"}]};
}

module.exports = {
  generateDataListRulesSimpleResponse
};
