
// ResultUserBindResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultUserBindResponse模型的模拟数据
 * @returns {ResultUserBindResponse} 模拟数据
 */
function generateResultUserBindResponse() {
  return {"code": "repeat_request", "data": {"id": 282, "user": {"id": 537, "name": "officer", "url": "court", "avatar": "interesting"}, "platform": "1", "platformId": "discuss", "bindTime": 1753433975, "relationData": "space"}};
}

module.exports = {
  generateResultUserBindResponse
};
