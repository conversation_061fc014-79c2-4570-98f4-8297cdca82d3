
// ResponseDataListOrdersLogsResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListOrdersLogsResponse模型的模拟数据
 * @returns {ResponseDataListOrdersLogsResponse} 模拟数据
 */
function generateResponseDataListOrdersLogsResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 795, "maxPage": 226, "total": 4521, "data": [{"id": 368, "ordersId": 45, "item": {"id": 209, "name": "value", "url": "road", "avatar": "lose"}, "posts": {"id": 457, "name": "community", "url": "plan", "avatar": "other"}, "logType": "1"}, {"id": 758, "ordersId": 864, "item": {"id": 280, "name": "others", "url": "report", "avatar": "maybe"}, "posts": {"id": 271, "name": "his", "url": "picture", "avatar": "hear"}, "logType": "1"}]}, "result": {"code": "phone_number_error", "data": {"curPage": 602, "maxPage": 521, "total": 4667, "data": [{"id": 655, "ordersId": 852, "item": {"id": 691, "name": "institution", "url": "site", "avatar": "talk"}, "posts": {"id": 166, "name": "compare", "url": "town", "avatar": "Democrat"}, "logType": "1"}, {"id": 455, "ordersId": 852, "item": {"id": 283, "name": "stay", "url": "although", "avatar": "just"}, "posts": {"id": 629, "name": "room", "url": "at", "avatar": "second"}, "logType": "1"}, {"id": 918, "ordersId": 267, "item": {"id": 728, "name": "president", "url": "war", "avatar": "account"}, "posts": {"id": 334, "name": "million", "url": "capital", "avatar": "evidence"}, "logType": "1"}]}}, "errMessageOnly": "serve", "successMessage": "walk", "errMessage": "type"};
}

module.exports = {
  generateResponseDataListOrdersLogsResponse
};
