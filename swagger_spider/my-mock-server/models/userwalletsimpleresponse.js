
// UserWalletSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成UserWalletSimpleResponse模型的模拟数据
 * @returns {UserWalletSimpleResponse} 模拟数据
 */
function generateUserWalletSimpleResponse() {
  return {"id": 845, "status": "1", "walletName": "worry", "user": {"id": 919, "name": "leave", "url": "short", "avatar": "set"}, "walletType": "1", "country": "walk", "firstName": "standard", "receiveAccount": "seem", "createTime": **********};
}

module.exports = {
  generateUserWalletSimpleResponse
};
