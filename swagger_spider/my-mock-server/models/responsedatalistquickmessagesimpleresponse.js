
// ResponseDataListQuickMessageSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListQuickMessageSimpleResponse模型的模拟数据
 * @returns {ResponseDataListQuickMessageSimpleResponse} 模拟数据
 */
function generateResponseDataListQuickMessageSimpleResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 350, "maxPage": 659, "total": 13, "data": [{"id": 975, "quickMessageTypeId": 516, "quickMessageTypeName": "Congress", "manageId": 456, "manageName": "treat"}, {"id": 208, "quickMessageTypeId": 789, "quickMessageTypeName": "threat", "manageId": 369, "manageName": "property"}, {"id": 32, "quickMessageTypeId": 656, "quickMessageTypeName": "by", "manageId": 232, "manageName": "easy"}]}, "result": {"code": "store_notice_id_not_exists", "data": {"curPage": 783, "maxPage": 566, "total": 3292, "data": [{"id": 210, "quickMessageTypeId": 605, "quickMessageTypeName": "key", "manageId": 367, "manageName": "professional"}, {"id": 369, "quickMessageTypeId": 359, "quickMessageTypeName": "bag", "manageId": 993, "manageName": "practice"}, {"id": 317, "quickMessageTypeId": 847, "quickMessageTypeName": "chance", "manageId": 819, "manageName": "full"}]}}, "errMessageOnly": "Congress", "successMessage": "car", "errMessage": "world"};
}

module.exports = {
  generateResponseDataListQuickMessageSimpleResponse
};
