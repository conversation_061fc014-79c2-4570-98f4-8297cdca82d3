
// ResponsePostsTypeResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponsePostsTypeResponse模型的模拟数据
 * @returns {ResponsePostsTypeResponse} 模拟数据
 */
function generateResponsePostsTypeResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 557, "status": "1", "language": "en_US", "typeName": "catch", "customUrl": "society", "coverPic": "blue", "createTime": 1753433976, "parentId": 16, "seoKeywords": "sure", "seoDescription": "beyond"}, "result": {"code": "faq_delete_error", "data": {"id": 426, "status": "1", "language": "en_US", "typeName": "someone", "customUrl": "wrong", "coverPic": "picture", "createTime": 1753433976, "parentId": 468, "seoKeywords": "low", "seoDescription": "response"}}, "errMessageOnly": "cultural", "successMessage": "entire", "errMessage": "purpose"};
}

module.exports = {
  generateResponsePostsTypeResponse
};
