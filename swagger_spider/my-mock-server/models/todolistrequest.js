
// TodoListRequest 模型
// 由SwaggerCrawler自动生成

/**
 * 生成TodoListRequest模型的模拟数据
 * @returns {TodoListRequest} 模拟数据
 */
function generateTodoListRequest() {
  return {"taskType": "SCHEDULE_LEAVE_AUDIT", "status": "CANCELLED", "manageId": 617, "roleId": 405, "relationType": "USER_KYC", "relationId": 796, "prioritySort": "WITHDRAWAL_AUDIT", "taskName": "enter", "summary": "someone"};
}

module.exports = {
  generateTodoListRequest
};
