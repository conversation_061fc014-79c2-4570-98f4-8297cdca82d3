
// ResultDataListCampaignSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListCampaignSimpleResponse模型的模拟数据
 * @returns {ResultDataListCampaignSimpleResponse} 模拟数据
 */
function generateResultDataListCampaignSimpleResponse() {
  return {"code": "attr_value_exists", "data": {"curPage": 892, "maxPage": 404, "total": 4510, "data": [{"id": 870, "status": "1", "customUrl": "game", "language": "en_US", "title": "partner"}, {"id": 294, "status": "1", "customUrl": "out", "language": "en_US", "title": "affect"}]}};
}

module.exports = {
  generateResultDataListCampaignSimpleResponse
};
