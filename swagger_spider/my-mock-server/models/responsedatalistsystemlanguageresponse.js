
// ResponseDataListSystemLanguageResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListSystemLanguageResponse模型的模拟数据
 * @returns {ResponseDataListSystemLanguageResponse} 模拟数据
 */
function generateResponseDataListSystemLanguageResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 170, "maxPage": 874, "total": 5826, "data": [{"id": 765, "language": "en_US", "langKey": "country", "languageValue": "both", "createTime": 1753433976}, {"id": 90, "language": "en_US", "langKey": "expect", "languageValue": "never", "createTime": 1753433976}, {"id": 573, "language": "en_US", "langKey": "however", "languageValue": "hair", "createTime": 1753433976}]}, "result": {"code": "faq_use_type_invalid", "data": {"curPage": 114, "maxPage": 679, "total": 2469, "data": [{"id": 378, "language": "en_US", "langKey": "remain", "languageValue": "vote", "createTime": 1753433976}, {"id": 73, "language": "en_US", "langKey": "people", "languageValue": "eye", "createTime": 1753433976}, {"id": 337, "language": "en_US", "langKey": "whether", "languageValue": "perform", "createTime": 1753433976}]}}, "errMessageOnly": "address", "successMessage": "land", "errMessage": "soldier"};
}

module.exports = {
  generateResponseDataListSystemLanguageResponse
};
