
// ResponseDataListItemAttrListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListItemAttrListResponse模型的模拟数据
 * @returns {ResponseDataListItemAttrListResponse} 模拟数据
 */
function generateResponseDataListItemAttrListResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 274, "maxPage": 12, "total": 5256, "data": [{"id": 413, "item": {"id": 12, "name": "student", "url": "later", "avatar": "true"}, "attr": {"id": 144, "name": "type", "url": "indeed", "avatar": "together"}, "attrValue": {"id": 800, "name": "suggest", "url": "daughter", "avatar": "long"}, "createTime": 1753433976}, {"id": 201, "item": {"id": 39, "name": "four", "url": "country", "avatar": "watch"}, "attr": {"id": 73, "name": "data", "url": "point", "avatar": "ten"}, "attrValue": {"id": 682, "name": "open", "url": "deal", "avatar": "even"}, "createTime": 1753433976}, {"id": 371, "item": {"id": 872, "name": "face", "url": "where", "avatar": "price"}, "attr": {"id": 485, "name": "between", "url": "born", "avatar": "majority"}, "attrValue": {"id": 758, "name": "fast", "url": "night", "avatar": "term"}, "createTime": 1753433976}]}, "result": {"code": "quick_message_add_failure", "data": {"curPage": 222, "maxPage": 706, "total": 6120, "data": [{"id": 271, "item": {"id": 699, "name": "animal", "url": "tree", "avatar": "themselves"}, "attr": {"id": 74, "name": "hot", "url": "theory", "avatar": "sister"}, "attrValue": {"id": 558, "name": "half", "url": "explain", "avatar": "he"}, "createTime": 1753433976}, {"id": 711, "item": {"id": 751, "name": "really", "url": "fine", "avatar": "least"}, "attr": {"id": 697, "name": "live", "url": "world", "avatar": "help"}, "attrValue": {"id": 561, "name": "use", "url": "amount", "avatar": "wrong"}, "createTime": 1753433976}]}}, "errMessageOnly": "performance", "successMessage": "everybody", "errMessage": "fine"};
}

module.exports = {
  generateResponseDataListItemAttrListResponse
};
