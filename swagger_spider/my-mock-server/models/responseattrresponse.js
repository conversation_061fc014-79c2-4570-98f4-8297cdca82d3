
// ResponseAttrResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseAttrResponse模型的模拟数据
 * @returns {ResponseAttrResponse} 模拟数据
 */
function generateResponseAttrResponse() {
  return {"code": 0, "msg": "success", "data": {"attrId": 872, "attrName": "both", "attrValueId": 696, "attrValueName": "watch"}, "result": {"code": "user_save_list_is_exits", "data": {"attrId": 697, "attrName": "very", "attrValueId": 845, "attrValueName": "produce"}}, "errMessageOnly": "plan", "successMessage": "machine", "errMessage": "Democrat"};
}

module.exports = {
  generateResponseAttrResponse
};
