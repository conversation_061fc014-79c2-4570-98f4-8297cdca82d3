
// ResultListScheduleHandoverLogSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultListScheduleHandoverLogSimpleResponse模型的模拟数据
 * @returns {ResultListScheduleHandoverLogSimpleResponse} 模拟数据
 */
function generateResultListScheduleHandoverLogSimpleResponse() {
  return {"code": "role_batch_delete_failed", "data": [{"id": 837, "scheduleId": 102, "scheduleName": "discuss", "manageId": 60, "manageName": "price", "status": "1", "mark": "person", "createTime": 1753433976}, {"id": 473, "scheduleId": 794, "scheduleName": "himself", "manageId": 793, "manageName": "really", "status": "1", "mark": "woman", "createTime": 1753433976}]};
}

module.exports = {
  generateResultListScheduleHandoverLogSimpleResponse
};
