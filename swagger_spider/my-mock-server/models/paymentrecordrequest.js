
// PaymentRecordRequest 模型
// 由SwaggerCrawler自动生成

/**
 * 生成PaymentRecordRequest模型的模拟数据
 * @returns {PaymentRecordRequest} 模拟数据
 */
function generatePaymentRecordRequest() {
  return {"userId": 900, "paymentId": 302, "transactionId": "admit", "amount": 265, "currency": "base", "status": "PENDING", "paymentTime": 1753433975, "refundAmount": 384, "refundTime": 1753433975, "paymentMethod": "suggest"};
}

module.exports = {
  generatePaymentRecordRequest
};
