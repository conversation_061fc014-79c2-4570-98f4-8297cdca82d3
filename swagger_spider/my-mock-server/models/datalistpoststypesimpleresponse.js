
// DataListPostsTypeSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListPostsTypeSimpleResponse模型的模拟数据
 * @returns {DataListPostsTypeSimpleResponse} 模拟数据
 */
function generateDataListPostsTypeSimpleResponse() {
  return {"curPage": 912, "maxPage": 898, "total": 2225, "data": [{"id": 697, "status": "1", "language": "en_US", "typeName": "edge", "customUrl": "spend", "coverPic": "station", "createTime": 1753433976}]};
}

module.exports = {
  generateDataListPostsTypeSimpleResponse
};
