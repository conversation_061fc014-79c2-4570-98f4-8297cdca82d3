
// ResultDataListUserSaveListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListUserSaveListResponse模型的模拟数据
 * @returns {ResultDataListUserSaveListResponse} 模拟数据
 */
function generateResultDataListUserSaveListResponse() {
  return {"code": "pages_batch_delete_error", "data": {"curPage": 68, "maxPage": 507, "total": 3166, "data": [{"id": 641, "user": {"id": 896, "name": "perhaps", "url": "bar", "avatar": "after"}, "saveType": "1", "relationId": 329, "createTime": 1753433975}]}};
}

module.exports = {
  generateResultDataListUserSaveListResponse
};
