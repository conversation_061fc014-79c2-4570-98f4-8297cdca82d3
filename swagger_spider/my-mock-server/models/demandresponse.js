
// DemandResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DemandResponse模型的模拟数据
 * @returns {DemandResponse} 模拟数据
 */
function generateDemandResponse() {
  return {"id": 210, "user": {"id": 663, "name": "listen", "url": "collection", "avatar": "lead"}, "brand": {"id": 590, "name": "another", "url": "official", "avatar": "force"}, "services": {"id": 652, "name": "director", "url": "admit", "avatar": "kid"}, "attrList": [{"attrId": 817, "attrName": "soon", "attrValueId": 787, "attrValueName": "may"}, {"attrId": 896, "attrName": "business", "attrValueId": 151, "attrValueName": "when"}, {"attrId": 415, "attrName": "operation", "attrValueId": 833, "attrValueName": "require"}], "status": "1", "language": "en_US", "demandName": "impact", "priceFrom": 432, "priceTo": 934};
}

module.exports = {
  generateDemandResponse
};
