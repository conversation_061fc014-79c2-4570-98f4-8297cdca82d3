
// ResultStoreStatLogResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultStoreStatLogResponse模型的模拟数据
 * @returns {ResultStoreStatLogResponse} 模拟数据
 */
function generateResultStoreStatLogResponse() {
  return {"code": "posts_type_id_not_exists", "data": {"id": 144, "date": "1977-01-27T00:50:15.901631", "store": {"id": 678, "name": "family", "url": "side", "avatar": "when"}, "visitCount": 505, "ordersCount": 368, "salesAmount": 54, "updateTime": 1753433976, "salesCount": 562, "refundAmount": 424, "withdrawAmount": 578}};
}

module.exports = {
  generateResultStoreStatLogResponse
};
