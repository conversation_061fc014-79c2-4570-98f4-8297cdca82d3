
// BrandResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成BrandResponse模型的模拟数据
 * @returns {BrandResponse} 模拟数据
 */
function generateBrandResponse() {
  return {"id": 600, "parentId": 585, "status": "1", "brandType": "1", "brandLogo": "where", "customUrl": "star", "createTime": 1753433976, "updateTime": 1753433976, "brandLangId": 430, "language": "en_US"};
}

module.exports = {
  generateBrandResponse
};
