
// DemandBidSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DemandBidSimpleResponse模型的模拟数据
 * @returns {DemandBidSimpleResponse} 模拟数据
 */
function generateDemandBidSimpleResponse() {
  return {"id": 506, "demand": {"id": 734, "name": "energy", "url": "during", "avatar": "science"}, "user": {"id": 277, "name": "challenge", "url": "game", "avatar": "ready"}, "store": {"id": 814, "name": "four", "url": "all", "avatar": "skin"}, "status": "1", "bidTime": 1753433976, "bidPrice": 824, "bidPics": ["remain"]};
}

module.exports = {
  generateDemandBidSimpleResponse
};
