
// ResultBrandFieldResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultBrandFieldResponse模型的模拟数据
 * @returns {ResultBrandFieldResponse} 模拟数据
 */
function generateResultBrandFieldResponse() {
  return {"code": "system_config_add_error", "data": {"id": 412, "brandId": 890, "fieldName": "think", "fieldType": "1", "required": "1", "createTime": 1753433976, "updateTime": 1753433976, "brandFieldLangId": 638, "language": "en_US", "label": "man"}};
}

module.exports = {
  generateResultBrandFieldResponse
};
