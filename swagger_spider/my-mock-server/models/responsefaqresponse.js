
// ResponseFaqResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseFaqResponse模型的模拟数据
 * @returns {ResponseFaqResponse} 模拟数据
 */
function generateResponseFaqResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 205, "useTypeName": "near", "manage": {"id": 366, "name": "direction", "url": "strategy", "avatar": "degree"}, "brand": {"id": 710, "name": "project", "url": "mission", "avatar": "inside"}, "statusName": "likely", "language": "en_US", "title": "light", "coverPic": "reason", "createTime": 1753433976, "useType": "1"}, "result": {"code": "schedule_batch_delete_error", "data": {"id": 995, "useTypeName": "exist", "manage": {"id": 486, "name": "happy", "url": "whom", "avatar": "glass"}, "brand": {"id": 994, "name": "increase", "url": "movement", "avatar": "water"}, "statusName": "culture", "language": "en_US", "title": "according", "coverPic": "fine", "createTime": 1753433976, "useType": "1"}}, "errMessageOnly": "by", "successMessage": "teach", "errMessage": "these"};
}

module.exports = {
  generateResponseFaqResponse
};
