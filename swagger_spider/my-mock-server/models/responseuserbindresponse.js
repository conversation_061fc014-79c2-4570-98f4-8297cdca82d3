
// ResponseUserBindResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseUserBindResponse模型的模拟数据
 * @returns {ResponseUserBindResponse} 模拟数据
 */
function generateResponseUserBindResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 872, "user": {"id": 654, "name": "describe", "url": "race", "avatar": "tree"}, "platform": "1", "platformId": "available", "bindTime": 1753433975, "relationData": "well"}, "result": {"code": "rules_delete_error", "data": {"id": 807, "user": {"id": 680, "name": "it", "url": "two", "avatar": "list"}, "platform": "1", "platformId": "development", "bindTime": 1753433975, "relationData": "explain"}}, "errMessageOnly": "food", "successMessage": "Congress", "errMessage": "lot"};
}

module.exports = {
  generateResponseUserBindResponse
};
