
// ResultManageLogsResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultManageLogsResponse模型的模拟数据
 * @returns {ResultManageLogsResponse} 模拟数据
 */
function generateResultManageLogsResponse() {
  return {"code": "brand_field_add_error", "data": {"id": 642, "manageId": 210, "ip": 1783, "ua": "easy", "method": "medical", "module": "carry", "action": "argue", "code": 0, "params": {}, "response": {}}};
}

module.exports = {
  generateResultManageLogsResponse
};
