
// ServicesRequest 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ServicesRequest模型的模拟数据
 * @returns {ServicesRequest} 模拟数据
 */
function generateServicesRequest() {
  return {"parentId": 759, "status": "ENABLED", "sortIndex": "0", "customUrl": "here", "servicesKey": "chance", "servicesLogo": "wide", "language": "en_US", "seoKeywords": "away", "seoDescription": "ground", "servicesName": "for"};
}

module.exports = {
  generateServicesRequest
};
