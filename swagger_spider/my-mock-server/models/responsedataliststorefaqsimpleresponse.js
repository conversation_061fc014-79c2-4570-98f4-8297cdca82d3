
// ResponseDataListStoreFaqSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListStoreFaqSimpleResponse模型的模拟数据
 * @returns {ResponseDataListStoreFaqSimpleResponse} 模拟数据
 */
function generateResponseDataListStoreFaqSimpleResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 573, "maxPage": 298, "total": 1755, "data": [{"id": 246, "store": {"id": 858, "name": "require", "url": "artist", "avatar": "road"}, "item": {"id": 356, "name": "professor", "url": "case", "avatar": "kitchen"}, "status": "1", "language": "en_US"}, {"id": 386, "store": {"id": 201, "name": "part", "url": "debate", "avatar": "thus"}, "item": {"id": 487, "name": "project", "url": "myself", "avatar": "white"}, "status": "1", "language": "en_US"}, {"id": 605, "store": {"id": 785, "name": "else", "url": "thus", "avatar": "culture"}, "item": {"id": 349, "name": "above", "url": "positive", "avatar": "fund"}, "status": "1", "language": "en_US"}]}, "result": {"code": "tag_custom_url_exists", "data": {"curPage": 879, "maxPage": 238, "total": 6097, "data": [{"id": 133, "store": {"id": 437, "name": "authority", "url": "traditional", "avatar": "hundred"}, "item": {"id": 750, "name": "bit", "url": "because", "avatar": "every"}, "status": "1", "language": "en_US"}]}}, "errMessageOnly": "top", "successMessage": "network", "errMessage": "house"};
}

module.exports = {
  generateResponseDataListStoreFaqSimpleResponse
};
