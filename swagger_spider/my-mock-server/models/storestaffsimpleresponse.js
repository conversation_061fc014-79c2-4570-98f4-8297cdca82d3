
// StoreStaffSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成StoreStaffSimpleResponse模型的模拟数据
 * @returns {StoreStaffSimpleResponse} 模拟数据
 */
function generateStoreStaffSimpleResponse() {
  return {"id": 3, "store": {"id": 788, "name": "heavy", "url": "young", "avatar": "must"}, "user": {"id": 774, "name": "before", "url": "beyond", "avatar": "scene"}, "status": "1", "nickName": "there", "avatar": "action", "phone": "radio", "permissions": "need", "createTime": 1753433976};
}

module.exports = {
  generateStoreStaffSimpleResponse
};
