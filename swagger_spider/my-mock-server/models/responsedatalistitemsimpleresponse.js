
// ResponseDataListItemSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListItemSimpleResponse模型的模拟数据
 * @returns {ResponseDataListItemSimpleResponse} 模拟数据
 */
function generateResponseDataListItemSimpleResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 210, "maxPage": 104, "total": 3980, "data": [{"id": 418, "user": {"id": 715, "name": "south", "url": "enough", "avatar": "treat"}, "store": {"id": 790, "name": "owner", "url": "find", "avatar": "food"}, "brand": {"id": 524, "name": "popular", "url": "area", "avatar": "worry"}, "services": {"id": 55, "name": "its", "url": "out", "avatar": "large"}}]}, "result": {"code": "complaint_id_not_exists", "data": {"curPage": 303, "maxPage": 914, "total": 7628, "data": [{"id": 969, "user": {"id": 407, "name": "house", "url": "into", "avatar": "man"}, "store": {"id": 685, "name": "subject", "url": "stand", "avatar": "election"}, "brand": {"id": 908, "name": "bit", "url": "political", "avatar": "report"}, "services": {"id": 829, "name": "song", "url": "with", "avatar": "write"}}, {"id": 467, "user": {"id": 836, "name": "sit", "url": "couple", "avatar": "paper"}, "store": {"id": 655, "name": "child", "url": "town", "avatar": "within"}, "brand": {"id": 389, "name": "catch", "url": "establish", "avatar": "other"}, "services": {"id": 858, "name": "general", "url": "care", "avatar": "need"}}, {"id": 434, "user": {"id": 600, "name": "cultural", "url": "service", "avatar": "west"}, "store": {"id": 145, "name": "single", "url": "focus", "avatar": "expect"}, "brand": {"id": 965, "name": "husband", "url": "century", "avatar": "worker"}, "services": {"id": 593, "name": "blood", "url": "teacher", "avatar": "arrive"}}]}}, "errMessageOnly": "coach", "successMessage": "remain", "errMessage": "church"};
}

module.exports = {
  generateResponseDataListItemSimpleResponse
};
