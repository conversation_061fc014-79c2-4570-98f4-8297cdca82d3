
// ResponseDataListObject 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListObject模型的模拟数据
 * @returns {ResponseDataListObject} 模拟数据
 */
function generateResponseDataListObject() {
  return {"code": 0, "msg": "success", "data": {"curPage": 178, "maxPage": 912, "total": 8413, "data": [{}, {}, {}]}, "result": {"code": "vip_update_error", "data": {"curPage": 119, "maxPage": 68, "total": 4297, "data": [{}, {}, {}]}}, "errMessageOnly": "exactly", "successMessage": "something", "errMessage": "stay"};
}

module.exports = {
  generateResponseDataListObject
};
