
// ResultItemStockBatchResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultItemStockBatchResponse模型的模拟数据
 * @returns {ResultItemStockBatchResponse} 模拟数据
 */
function generateResultItemStockBatchResponse() {
  return {"code": "system_config_value_invalid", "data": {"id": 193, "store": {"id": 590, "name": "evidence", "url": "public", "avatar": "nature"}, "item": {"id": 571, "name": "everything", "url": "herself", "avatar": "central"}, "batchCount": 844, "salesCount": 202, "replacementCount": 739, "refundCount": 725, "restCount": 923, "createTime": 1753433976, "status": "1"}};
}

module.exports = {
  generateResultItemStockBatchResponse
};
