
// ResultDataListAttrSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListAttrSimpleResponse模型的模拟数据
 * @returns {ResultDataListAttrSimpleResponse} 模拟数据
 */
function generateResultDataListAttrSimpleResponse() {
  return {"code": "resources_delete_error", "data": {"curPage": 130, "maxPage": 102, "total": 1788, "data": [{"id": 770, "parent": {"id": 639, "name": "concern", "url": "contain", "avatar": "catch"}, "status": "1", "attrType": "1", "language": "en_US"}]}};
}

module.exports = {
  generateResultDataListAttrSimpleResponse
};
