
// ResponseListNameResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseListNameResponse模型的模拟数据
 * @returns {ResponseListNameResponse} 模拟数据
 */
function generateResponseListNameResponse() {
  return {"code": 0, "msg": "success", "data": [{"id": 410, "name": "reflect", "url": "maybe", "avatar": "reality"}, {"id": 645, "name": "include", "url": "others", "avatar": "door"}, {"id": 565, "name": "central", "url": "who", "avatar": "issue"}], "result": {"code": "schedule_sign_log_batch_update_error", "data": [{"id": 690, "name": "two", "url": "quickly", "avatar": "spring"}]}, "errMessageOnly": "camera", "successMessage": "set", "errMessage": "site"};
}

module.exports = {
  generateResponseListNameResponse
};
