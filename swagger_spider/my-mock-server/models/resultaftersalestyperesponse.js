
// ResultAftersalesTypeResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultAftersalesTypeResponse模型的模拟数据
 * @returns {ResultAftersalesTypeResponse} 模拟数据
 */
function generateResultAftersalesTypeResponse() {
  return {"code": "risk_control_finance_id_not_exists", "data": {"id": 15, "parentId": 804, "parentType": {"id": 118, "name": "manager", "url": "wait", "avatar": "home"}, "status": "1", "statusName": "look", "customUrl": "true", "createTime": 1753433976, "updateTime": 1753433976, "language": "en_US", "typeName": "too"}};
}

module.exports = {
  generateResultAftersalesTypeResponse
};
