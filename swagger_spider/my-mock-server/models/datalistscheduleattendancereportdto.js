
// DataListScheduleAttendanceReportDTO 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListScheduleAttendanceReportDTO模型的模拟数据
 * @returns {DataListScheduleAttendanceReportDTO} 模拟数据
 */
function generateDataListScheduleAttendanceReportDTO() {
  return {"curPage": 71, "maxPage": 672, "total": 5205, "data": [{"manage": {"id": 134, "name": "nice", "url": "most", "avatar": "believe"}, "month": "2004-01-27T22:30:26.587250", "schedulingTimes": 1753433976, "scheduledWorkingHours": 207, "actualWorkingHours": 407, "signCount": 151, "timeOffWork": 1753433976, "missCount": 382, "askLeaveCount": 332, "leaveHours": 344}, {"manage": {"id": 333, "name": "pay", "url": "respond", "avatar": "generation"}, "month": "2007-08-30T19:12:55.855518", "schedulingTimes": 1753433976, "scheduledWorkingHours": 632, "actualWorkingHours": 718, "signCount": 393, "timeOffWork": 1753433976, "missCount": 853, "askLeaveCount": 19, "leaveHours": 818}]};
}

module.exports = {
  generateDataListScheduleAttendanceReportDTO
};
