
// AttrValueResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成AttrValueResponse模型的模拟数据
 * @returns {AttrValueResponse} 模拟数据
 */
function generateAttrValueResponse() {
  return {"id": 272, "attrId": 463, "status": "1", "sortIndex": "0", "customUrl": "commercial", "updateTime": 1753433976, "createTime": 1753433976, "attrValueLangId": 916, "language": "en_US", "attrValue": "least"};
}

module.exports = {
  generateAttrValueResponse
};
