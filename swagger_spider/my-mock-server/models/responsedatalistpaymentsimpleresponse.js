
// ResponseDataListPaymentSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListPaymentSimpleResponse模型的模拟数据
 * @returns {ResponseDataListPaymentSimpleResponse} 模拟数据
 */
function generateResponseDataListPaymentSimpleResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 722, "maxPage": 23, "total": 6774, "data": [{"id": 524, "status": "1", "paymentType": "history", "paymentName": "kitchen", "paymentLogo": "others"}]}, "result": {"code": "pages_type_id_can_not_be_null", "data": {"curPage": 442, "maxPage": 660, "total": 7909, "data": [{"id": 48, "status": "1", "paymentType": "family", "paymentName": "democratic", "paymentLogo": "include"}, {"id": 938, "status": "1", "paymentType": "rise", "paymentName": "conference", "paymentLogo": "care"}, {"id": 995, "status": "1", "paymentType": "account", "paymentName": "after", "paymentLogo": "system"}]}}, "errMessageOnly": "spend", "successMessage": "opportunity", "errMessage": "half"};
}

module.exports = {
  generateResponseDataListPaymentSimpleResponse
};
