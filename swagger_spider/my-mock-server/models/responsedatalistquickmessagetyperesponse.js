
// ResponseDataListQuickMessageTypeResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListQuickMessageTypeResponse模型的模拟数据
 * @returns {ResponseDataListQuickMessageTypeResponse} 模拟数据
 */
function generateResponseDataListQuickMessageTypeResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 841, "maxPage": 484, "total": 2094, "data": [{"id": 954, "status": "1", "typeName": "appear", "mark": "condition", "createTime": 1753433976}, {"id": 305, "status": "1", "typeName": "wonder", "mark": "first", "createTime": 1753433976}, {"id": 177, "status": "1", "typeName": "kitchen", "mark": "sort", "createTime": 1753433976}]}, "result": {"code": "http_method_not_support", "data": {"curPage": 261, "maxPage": 893, "total": 4004, "data": [{"id": 976, "status": "1", "typeName": "change", "mark": "single", "createTime": 1753433976}, {"id": 1000, "status": "1", "typeName": "security", "mark": "back", "createTime": 1753433976}]}}, "errMessageOnly": "certainly", "successMessage": "his", "errMessage": "several"};
}

module.exports = {
  generateResponseDataListQuickMessageTypeResponse
};
