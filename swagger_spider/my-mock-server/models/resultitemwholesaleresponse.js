
// ResultItemWholesaleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultItemWholesaleResponse模型的模拟数据
 * @returns {ResultItemWholesaleResponse} 模拟数据
 */
function generateResultItemWholesaleResponse() {
  return {"code": "password_valid_error", "data": {"id": 384, "item": {"id": 345, "name": "fear", "url": "behind", "avatar": "why"}, "status": "1", "quantity": 845, "discount": 756, "unitPrice": 565, "createTime": 1753433976, "updateTime": 1753433976}};
}

module.exports = {
  generateResultItemWholesaleResponse
};
