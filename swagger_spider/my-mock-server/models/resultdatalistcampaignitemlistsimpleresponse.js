
// ResultDataListCampaignItemListSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListCampaignItemListSimpleResponse模型的模拟数据
 * @returns {ResultDataListCampaignItemListSimpleResponse} 模拟数据
 */
function generateResultDataListCampaignItemListSimpleResponse() {
  return {"code": "service_parameter_error", "data": {"curPage": 10, "maxPage": 827, "total": 2019, "data": [{"id": 43, "campaign": {"id": 165, "name": "method", "url": "skin", "avatar": "reveal"}, "item": {"id": 340, "name": "major", "url": "quality", "avatar": "land"}, "store": {"id": 931, "name": "arrive", "url": "prove", "avatar": "city"}, "createTime": 1753433976}, {"id": 1, "campaign": {"id": 835, "name": "entire", "url": "state", "avatar": "boy"}, "item": {"id": 418, "name": "meeting", "url": "claim", "avatar": "body"}, "store": {"id": 877, "name": "quickly", "url": "present", "avatar": "seven"}, "createTime": 1753433976}, {"id": 82, "campaign": {"id": 577, "name": "federal", "url": "ready", "avatar": "some"}, "item": {"id": 364, "name": "race", "url": "say", "avatar": "later"}, "store": {"id": 88, "name": "style", "url": "off", "avatar": "painting"}, "createTime": 1753433976}]}};
}

module.exports = {
  generateResultDataListCampaignItemListSimpleResponse
};
