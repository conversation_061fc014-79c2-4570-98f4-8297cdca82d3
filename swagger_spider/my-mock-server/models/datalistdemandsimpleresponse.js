
// DataListDemandSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListDemandSimpleResponse模型的模拟数据
 * @returns {DataListDemandSimpleResponse} 模拟数据
 */
function generateDataListDemandSimpleResponse() {
  return {"curPage": 354, "maxPage": 15, "total": 8594, "data": [{"id": 570, "user": {"id": 878, "name": "the", "url": "beyond", "avatar": "prepare"}, "brand": {"id": 963, "name": "reach", "url": "environment", "avatar": "consumer"}, "services": {"id": 147, "name": "ten", "url": "name", "avatar": "detail"}, "attrList": [{"attrId": 586, "attrName": "think", "attrValueId": 276, "attrValueName": "while"}], "status": "1", "language": "en_US", "demandName": "other", "priceFrom": 859, "priceTo": 303}, {"id": 603, "user": {"id": 127, "name": "then", "url": "hope", "avatar": "site"}, "brand": {"id": 923, "name": "against", "url": "between", "avatar": "to"}, "services": {"id": 837, "name": "relationship", "url": "sing", "avatar": "value"}, "attrList": [{"attrId": 32, "attrName": "several", "attrValueId": 866, "attrValueName": "remember"}, {"attrId": 874, "attrName": "campaign", "attrValueId": 366, "attrValueName": "point"}, {"attrId": 364, "attrName": "foreign", "attrValueId": 967, "attrValueName": "staff"}], "status": "1", "language": "en_US", "demandName": "argue", "priceFrom": 646, "priceTo": 56}, {"id": 402, "user": {"id": 563, "name": "to", "url": "nearly", "avatar": "PM"}, "brand": {"id": 664, "name": "grow", "url": "modern", "avatar": "name"}, "services": {"id": 254, "name": "how", "url": "sing", "avatar": "wonder"}, "attrList": [{"attrId": 873, "attrName": "include", "attrValueId": 484, "attrValueName": "according"}, {"attrId": 929, "attrName": "upon", "attrValueId": 469, "attrValueName": "remain"}, {"attrId": 674, "attrName": "network", "attrValueId": 898, "attrValueName": "should"}], "status": "1", "language": "en_US", "demandName": "ahead", "priceFrom": 500, "priceTo": 702}]};
}

module.exports = {
  generateDataListDemandSimpleResponse
};
