
// ResponseUserSaveListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseUserSaveListResponse模型的模拟数据
 * @returns {ResponseUserSaveListResponse} 模拟数据
 */
function generateResponseUserSaveListResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 353, "user": {"id": 422, "name": "such", "url": "role", "avatar": "research"}, "saveType": "1", "relationId": 870, "createTime": 1753433975}, "result": {"code": "add_fail", "data": {"id": 498, "user": {"id": 581, "name": "window", "url": "body", "avatar": "as"}, "saveType": "1", "relationId": 722, "createTime": 1753433975}}, "errMessageOnly": "general", "successMessage": "someone", "errMessage": "then"};
}

module.exports = {
  generateResponseUserSaveListResponse
};
