
// ResultUserSanctionsResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultUserSanctionsResponse模型的模拟数据
 * @returns {ResultUserSanctionsResponse} 模拟数据
 */
function generateResultUserSanctionsResponse() {
  return {"code": "demand_publish_attr_list_not_empty", "data": {"id": 668, "violationsEventId": 716, "user": {"id": 505, "name": "remember", "url": "have", "avatar": "safe"}, "status": "1", "sanctionType": "1", "actionClass": "finish", "sanctionData": "authority", "sanctionStart": 444, "sanctionEnd": 375, "mark": "six"}};
}

module.exports = {
  generateResultUserSanctionsResponse
};
