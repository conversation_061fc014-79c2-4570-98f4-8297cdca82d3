
// ResultFaqResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultFaqResponse模型的模拟数据
 * @returns {ResultFaqResponse} 模拟数据
 */
function generateResultFaqResponse() {
  return {"code": "refund_failure", "data": {"id": 468, "useTypeName": "from", "manage": {"id": 939, "name": "performance", "url": "least", "avatar": "bill"}, "brand": {"id": 97, "name": "enter", "url": "world", "avatar": "service"}, "statusName": "policy", "language": "en_US", "title": "green", "coverPic": "dark", "createTime": 1753433976, "useType": "1"}};
}

module.exports = {
  generateResultFaqResponse
};
