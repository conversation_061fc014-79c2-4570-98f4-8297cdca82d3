
// ResultDataListItemStockResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListItemStockResponse模型的模拟数据
 * @returns {ResultDataListItemStockResponse} 模拟数据
 */
function generateResultDataListItemStockResponse() {
  return {"code": "manage_logs_batch_delete_error", "data": {"curPage": 210, "maxPage": 952, "total": 8024, "data": [{"id": 61, "item": {"id": 893, "name": "hope", "url": "huge", "avatar": "executive"}, "batchId": 246, "ordersId": 266, "checkStatus": "1"}]}};
}

module.exports = {
  generateResultDataListItemStockResponse
};
