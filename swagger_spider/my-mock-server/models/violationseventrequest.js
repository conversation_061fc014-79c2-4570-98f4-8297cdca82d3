
// ViolationsEventRequest 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ViolationsEventRequest模型的模拟数据
 * @returns {ViolationsEventRequest} 模拟数据
 */
function generateViolationsEventRequest() {
  return {"userId": 939, "storeId": 214, "itemId": 753, "demandId": 113, "postsId": 562, "rulesId": 470, "eventType": "CHAT_VIOLATION", "status": "DISABLED", "relationId": 366, "message": "bill"};
}

module.exports = {
  generateViolationsEventRequest
};
