
// UserKycSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成UserKycSimpleResponse模型的模拟数据
 * @returns {UserKycSimpleResponse} 模拟数据
 */
function generateUserKycSimpleResponse() {
  return {"id": 188, "status": "1", "user": {"id": 832, "name": "eye", "url": "red", "avatar": "power"}, "version": 741, "kycType": "1", "firstName": "draw", "lastName": "sense", "birthDate": "2007-04-04T15:41:44.882650", "idPic": "water", "handsPic": "wide"};
}

module.exports = {
  generateUserKycSimpleResponse
};
