
// ServicesAttrListSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ServicesAttrListSimpleResponse模型的模拟数据
 * @returns {ServicesAttrListSimpleResponse} 模拟数据
 */
function generateServicesAttrListSimpleResponse() {
  return {"id": 981, "services": {"id": 246, "name": "pay", "url": "because", "avatar": "sure"}, "attr": {"id": 450, "name": "to", "url": "these", "avatar": "century"}, "createTime": 1753433976};
}

module.exports = {
  generateServicesAttrListSimpleResponse
};
