
// ResultStoreWithdrawResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultStoreWithdrawResponse模型的模拟数据
 * @returns {ResultStoreWithdrawResponse} 模拟数据
 */
function generateResultStoreWithdrawResponse() {
  return {"code": "user_save_list_id_not_exists", "data": {"id": 800, "user": {"id": 997, "name": "quite", "url": "base", "avatar": "opportunity"}, "store": {"id": 194, "name": "attention", "url": "song", "avatar": "recent"}, "userWallet": {"id": 864, "name": "rather", "url": "travel", "avatar": "among"}, "status": "1", "currentRate": 701, "withdrawAmount": 93, "transferAmount": 675, "feeFixed": 765, "feePercent": 896}};
}

module.exports = {
  generateResultStoreWithdrawResponse
};
