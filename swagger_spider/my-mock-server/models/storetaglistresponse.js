
// StoreTagListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成StoreTagListResponse模型的模拟数据
 * @returns {StoreTagListResponse} 模拟数据
 */
function generateStoreTagListResponse() {
  return {"id": 849, "store": {"id": 355, "name": "air", "url": "too", "avatar": "mean"}, "tag": {"id": 467, "name": "training", "url": "where", "avatar": "leg"}, "createTime": 1753433976};
}

module.exports = {
  generateStoreTagListResponse
};
