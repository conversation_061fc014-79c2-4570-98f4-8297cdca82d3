
// DataListSearchResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListSearchResponse模型的模拟数据
 * @returns {DataListSearchResponse} 模拟数据
 */
function generateDataListSearchResponse() {
  return {"curPage": 446, "maxPage": 730, "total": 1329, "data": [{"id": 839, "searchType": "1", "searchTypeName": "over", "brand": {"id": 684, "name": "improve", "url": "then", "avatar": "certainly"}, "status": "1", "statusName": "education", "sortIndex": 465, "searchCount": 182, "createTime": 1753433976, "updateTime": 1753433976}, {"id": 175, "searchType": "1", "searchTypeName": "certainly", "brand": {"id": 336, "name": "imagine", "url": "or", "avatar": "campaign"}, "status": "1", "statusName": "yard", "sortIndex": 619, "searchCount": 317, "createTime": 1753433976, "updateTime": 1753433976}, {"id": 762, "searchType": "1", "searchTypeName": "hit", "brand": {"id": 396, "name": "bad", "url": "school", "avatar": "way"}, "status": "1", "statusName": "weight", "sortIndex": 379, "searchCount": 919, "createTime": 1753433976, "updateTime": 1753433976}]};
}

module.exports = {
  generateDataListSearchResponse
};
