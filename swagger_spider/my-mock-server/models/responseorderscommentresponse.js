
// ResponseOrdersCommentResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseOrdersCommentResponse模型的模拟数据
 * @returns {ResponseOrdersCommentResponse} 模拟数据
 */
function generateResponseOrdersCommentResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 718, "user": {"id": 477, "name": "seek", "url": "every", "avatar": "order"}, "item": {"id": 497, "name": "way", "url": "meet", "avatar": "area"}, "ordersId": 893, "store": {"id": 737, "name": "child", "url": "level", "avatar": "adult"}, "status": "1", "rating": 512, "comment": "young", "createTime": 1753433976, "sortIndex": 331}, "result": {"code": "aftersales_id_not_null", "data": {"id": 670, "user": {"id": 93, "name": "cup", "url": "guy", "avatar": "bed"}, "item": {"id": 588, "name": "attention", "url": "send", "avatar": "start"}, "ordersId": 988, "store": {"id": 500, "name": "top", "url": "low", "avatar": "order"}, "status": "1", "rating": 62, "comment": "save", "createTime": 1753433976, "sortIndex": 364}}, "errMessageOnly": "shoulder", "successMessage": "office", "errMessage": "impact"};
}

module.exports = {
  generateResponseOrdersCommentResponse
};
