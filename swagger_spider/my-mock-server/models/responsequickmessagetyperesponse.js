
// ResponseQuickMessageTypeResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseQuickMessageTypeResponse模型的模拟数据
 * @returns {ResponseQuickMessageTypeResponse} 模拟数据
 */
function generateResponseQuickMessageTypeResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 849, "status": "1", "typeName": "cut", "mark": "his", "createTime": 1753433976}, "result": {"code": "aftersales_type_batch_update_error", "data": {"id": 626, "status": "1", "typeName": "which", "mark": "street", "createTime": 1753433976}}, "errMessageOnly": "usually", "successMessage": "interview", "errMessage": "money"};
}

module.exports = {
  generateResponseQuickMessageTypeResponse
};
