
// ResultRiskControlClassResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultRiskControlClassResponse模型的模拟数据
 * @returns {ResultRiskControlClassResponse} 模拟数据
 */
function generateResultRiskControlClassResponse() {
  return {"code": "pages_type_custom_url_exists", "data": {"id": 259, "status": "1", "className": "know", "riskInit": {}, "riskExecuteClass": "remember", "mark": "city", "createTime": 1753433976}};
}

module.exports = {
  generateResultRiskControlClassResponse
};
