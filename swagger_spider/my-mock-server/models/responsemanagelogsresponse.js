
// ResponseManageLogsResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseManageLogsResponse模型的模拟数据
 * @returns {ResponseManageLogsResponse} 模拟数据
 */
function generateResponseManageLogsResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 764, "manageId": 544, "ip": 2155, "ua": "movie", "method": "contain", "module": "site", "action": "fall", "code": 0, "params": {}, "response": {}}, "result": {"code": "search_id_not_exists", "data": {"id": 152, "manageId": 353, "ip": 1097, "ua": "building", "method": "space", "module": "win", "action": "war", "code": 0, "params": {}, "response": {}}}, "errMessageOnly": "cut", "successMessage": "term", "errMessage": "training"};
}

module.exports = {
  generateResponseManageLogsResponse
};
