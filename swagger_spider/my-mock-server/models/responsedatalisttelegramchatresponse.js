
// ResponseDataListTelegramChatResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListTelegramChatResponse模型的模拟数据
 * @returns {ResponseDataListTelegramChatResponse} 模拟数据
 */
function generateResponseDataListTelegramChatResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 20, "maxPage": 159, "total": 5762, "data": [{"id": 955, "telegramRobot": {"id": 180, "name": "campaign", "url": "positive", "avatar": "serious"}, "status": "1", "username": "run", "chatId": 210}]}, "result": {"code": "user_api_key_update_failed", "data": {"curPage": 56, "maxPage": 98, "total": 1322, "data": [{"id": 851, "telegramRobot": {"id": 262, "name": "left", "url": "push", "avatar": "allow"}, "status": "1", "username": "research", "chatId": 342}, {"id": 979, "telegramRobot": {"id": 37, "name": "upon", "url": "decade", "avatar": "democratic"}, "status": "1", "username": "rich", "chatId": 488}]}}, "errMessageOnly": "mouth", "successMessage": "fact", "errMessage": "read"};
}

module.exports = {
  generateResponseDataListTelegramChatResponse
};
