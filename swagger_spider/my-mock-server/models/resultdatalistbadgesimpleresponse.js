
// ResultDataListBadgeSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListBadgeSimpleResponse模型的模拟数据
 * @returns {ResultDataListBadgeSimpleResponse} 模拟数据
 */
function generateResultDataListBadgeSimpleResponse() {
  return {"code": "pages_type_delete_error", "data": {"curPage": 509, "maxPage": 379, "total": 9356, "data": [{"id": 934, "status": "1", "language": "en_US", "badgeType": "1", "badgeName": "parent"}, {"id": 224, "status": "1", "language": "en_US", "badgeType": "1", "badgeName": "ready"}, {"id": 52, "status": "1", "language": "en_US", "badgeType": "1", "badgeName": "believe"}]}};
}

module.exports = {
  generateResultDataListBadgeSimpleResponse
};
