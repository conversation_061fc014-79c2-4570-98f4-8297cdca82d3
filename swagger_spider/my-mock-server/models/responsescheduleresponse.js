
// ResponseScheduleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseScheduleResponse模型的模拟数据
 * @returns {ResponseScheduleResponse} 模拟数据
 */
function generateResponseScheduleResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 437, "status": "1", "statusName": "protect", "date": "example", "from": "upon", "to": "no", "manage": {"id": 787, "name": "budget", "url": "perform", "avatar": "food"}, "trueManage": {"id": 684, "name": "phone", "url": "fall", "avatar": "move"}, "workStart": "century", "workEnd": "change"}, "result": {"code": "brand_lang_id_not_exists", "data": {"id": 3, "status": "1", "statusName": "mouth", "date": "school", "from": "senior", "to": "me", "manage": {"id": 733, "name": "resource", "url": "hand", "avatar": "draw"}, "trueManage": {"id": 229, "name": "building", "url": "hospital", "avatar": "mouth"}, "workStart": "cup", "workEnd": "low"}}, "errMessageOnly": "many", "successMessage": "security", "errMessage": "tough"};
}

module.exports = {
  generateResponseScheduleResponse
};
