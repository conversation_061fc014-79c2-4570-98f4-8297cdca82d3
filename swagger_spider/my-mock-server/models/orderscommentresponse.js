
// OrdersCommentResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成OrdersCommentResponse模型的模拟数据
 * @returns {OrdersCommentResponse} 模拟数据
 */
function generateOrdersCommentResponse() {
  return {"id": 476, "user": {"id": 650, "name": "she", "url": "station", "avatar": "read"}, "item": {"id": 361, "name": "field", "url": "than", "avatar": "whose"}, "ordersId": 350, "store": {"id": 740, "name": "natural", "url": "election", "avatar": "garden"}, "status": "1", "rating": 622, "comment": "environment", "createTime": 1753433976, "sortIndex": 785};
}

module.exports = {
  generateOrdersCommentResponse
};
