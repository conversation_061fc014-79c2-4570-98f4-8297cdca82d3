
// ResultTelegramRobotResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultTelegramRobotResponse模型的模拟数据
 * @returns {ResultTelegramRobotResponse} 模拟数据
 */
function generateResultTelegramRobotResponse() {
  return {"code": "schedule_handover_log_id_not_exists", "data": {"id": 808, "status": "1", "name": "almost", "botName": "sing", "username": "unit", "token": "past", "mark": "late", "createTime": 1753433976}};
}

module.exports = {
  generateResultTelegramRobotResponse
};
