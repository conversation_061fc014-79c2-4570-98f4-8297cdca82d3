
// ResponseDataListServicesAttrListSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListServicesAttrListSimpleResponse模型的模拟数据
 * @returns {ResponseDataListServicesAttrListSimpleResponse} 模拟数据
 */
function generateResponseDataListServicesAttrListSimpleResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 611, "maxPage": 296, "total": 7523, "data": [{"id": 395, "services": {"id": 379, "name": "people", "url": "organization", "avatar": "necessary"}, "attr": {"id": 163, "name": "also", "url": "will", "avatar": "turn"}, "createTime": 1753433976}]}, "result": {"code": "posts_comment_update_failed", "data": {"curPage": 751, "maxPage": 650, "total": 6241, "data": [{"id": 479, "services": {"id": 398, "name": "wall", "url": "often", "avatar": "main"}, "attr": {"id": 393, "name": "rule", "url": "author", "avatar": "stock"}, "createTime": 1753433976}, {"id": 163, "services": {"id": 998, "name": "simply", "url": "stage", "avatar": "board"}, "attr": {"id": 32, "name": "maybe", "url": "space", "avatar": "always"}, "createTime": 1753433976}, {"id": 6, "services": {"id": 521, "name": "debate", "url": "brother", "avatar": "recently"}, "attr": {"id": 176, "name": "husband", "url": "stuff", "avatar": "responsibility"}, "createTime": 1753433976}]}}, "errMessageOnly": "hotel", "successMessage": "agreement", "errMessage": "home"};
}

module.exports = {
  generateResponseDataListServicesAttrListSimpleResponse
};
