
// ResponseDataListUserTransactionResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListUserTransactionResponse模型的模拟数据
 * @returns {ResponseDataListUserTransactionResponse} 模拟数据
 */
function generateResponseDataListUserTransactionResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 827, "maxPage": 495, "total": 8164, "data": [{"id": 161, "user": {"id": 267, "name": "per", "url": "information", "avatar": "rather"}, "store": {"id": 944, "name": "tell", "url": "shake", "avatar": "point"}, "changeType": "1", "changeMoney": 512}, {"id": 291, "user": {"id": 804, "name": "fund", "url": "he", "avatar": "common"}, "store": {"id": 230, "name": "always", "url": "sort", "avatar": "white"}, "changeType": "1", "changeMoney": 137}, {"id": 662, "user": {"id": 227, "name": "responsibility", "url": "religious", "avatar": "staff"}, "store": {"id": 350, "name": "whom", "url": "half", "avatar": "beat"}, "changeType": "1", "changeMoney": 104}]}, "result": {"code": "services_attr_id_invalid", "data": {"curPage": 404, "maxPage": 264, "total": 1801, "data": [{"id": 735, "user": {"id": 627, "name": "budget", "url": "point", "avatar": "treatment"}, "store": {"id": 499, "name": "technology", "url": "side", "avatar": "wear"}, "changeType": "1", "changeMoney": 251}, {"id": 497, "user": {"id": 613, "name": "there", "url": "wonder", "avatar": "not"}, "store": {"id": 934, "name": "beat", "url": "guess", "avatar": "agreement"}, "changeType": "1", "changeMoney": 883}, {"id": 506, "user": {"id": 756, "name": "Congress", "url": "financial", "avatar": "deal"}, "store": {"id": 689, "name": "bag", "url": "half", "avatar": "expect"}, "changeType": "1", "changeMoney": 536}]}}, "errMessageOnly": "draw", "successMessage": "short", "errMessage": "three"};
}

module.exports = {
  generateResponseDataListUserTransactionResponse
};
