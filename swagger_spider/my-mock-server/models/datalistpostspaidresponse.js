
// DataListPostsPaidResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListPostsPaidResponse模型的模拟数据
 * @returns {DataListPostsPaidResponse} 模拟数据
 */
function generateDataListPostsPaidResponse() {
  return {"curPage": 391, "maxPage": 487, "total": 624, "data": [{"id": 261, "posts": {"id": 50, "name": "message", "url": "manager", "avatar": "charge"}, "user": {"id": 190, "name": "artist", "url": "cell", "avatar": "scientist"}, "status": "1", "paidPrice": 942, "createTime": 1753433976}, {"id": 7, "posts": {"id": 5, "name": "management", "url": "next", "avatar": "thank"}, "user": {"id": 91, "name": "help", "url": "perform", "avatar": "information"}, "status": "1", "paidPrice": 655, "createTime": 1753433976}]};
}

module.exports = {
  generateDataListPostsPaidResponse
};
