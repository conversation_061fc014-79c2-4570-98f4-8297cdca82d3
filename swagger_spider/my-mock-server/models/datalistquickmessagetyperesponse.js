
// DataListQuickMessageTypeResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListQuickMessageTypeResponse模型的模拟数据
 * @returns {DataListQuickMessageTypeResponse} 模拟数据
 */
function generateDataListQuickMessageTypeResponse() {
  return {"curPage": 236, "maxPage": 47, "total": 9965, "data": [{"id": 700, "status": "1", "typeName": "budget", "mark": "green", "createTime": 1753433976}, {"id": 521, "status": "1", "typeName": "myself", "mark": "baby", "createTime": 1753433976}, {"id": 995, "status": "1", "typeName": "performance", "mark": "list", "createTime": 1753433976}]};
}

module.exports = {
  generateDataListQuickMessageTypeResponse
};
