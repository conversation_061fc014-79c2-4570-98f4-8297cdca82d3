
// ResponseUserLogsResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseUserLogsResponse模型的模拟数据
 * @returns {ResponseUserLogsResponse} 模拟数据
 */
function generateResponseUserLogsResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 982, "user": {"id": 429, "name": "beautiful", "url": "not", "avatar": "main"}, "method": "station", "module": "reach", "action": "eight", "ip": 537, "createTime": **********, "code": 0, "ua": "occur", "params": "might"}, "result": {"code": "comment_sort_by_invalid", "data": {"id": 93, "user": {"id": 746, "name": "discuss", "url": "pull", "avatar": "executive"}, "method": "kitchen", "module": "method", "action": "business", "ip": 109, "createTime": **********, "code": 0, "ua": "cause", "params": "operation"}}, "errMessageOnly": "discuss", "successMessage": "dog", "errMessage": "health"};
}

module.exports = {
  generateResponseUserLogsResponse
};
