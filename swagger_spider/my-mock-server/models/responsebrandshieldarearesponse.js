
// ResponseBrandShieldAreaResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseBrandShieldAreaResponse模型的模拟数据
 * @returns {ResponseBrandShieldAreaResponse} 模拟数据
 */
function generateResponseBrandShieldAreaResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 367, "brandId": 257, "status": "1", "country": "city", "mark": "recently", "createTime": 1753433976, "updateTime": 1753433976}, "result": {"code": "file_type_not_allow", "data": {"id": 342, "brandId": 155, "status": "1", "country": "half", "mark": "police", "createTime": 1753433976, "updateTime": 1753433976}}, "errMessageOnly": "baby", "successMessage": "policy", "errMessage": "could"};
}

module.exports = {
  generateResponseBrandShieldAreaResponse
};
