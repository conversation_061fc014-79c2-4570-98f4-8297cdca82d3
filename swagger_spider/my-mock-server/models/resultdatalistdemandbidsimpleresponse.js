
// ResultDataListDemandBidSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListDemandBidSimpleResponse模型的模拟数据
 * @returns {ResultDataListDemandBidSimpleResponse} 模拟数据
 */
function generateResultDataListDemandBidSimpleResponse() {
  return {"code": "posts_tag_list_id_not_exists", "data": {"curPage": 142, "maxPage": 122, "total": 417, "data": [{"id": 725, "demand": {"id": 756, "name": "campaign", "url": "body", "avatar": "officer"}, "user": {"id": 203, "name": "travel", "url": "also", "avatar": "hope"}, "store": {"id": 945, "name": "hair", "url": "matter", "avatar": "employee"}, "status": "1"}, {"id": 280, "demand": {"id": 342, "name": "nearly", "url": "avoid", "avatar": "small"}, "user": {"id": 712, "name": "open", "url": "PM", "avatar": "we"}, "store": {"id": 834, "name": "light", "url": "hot", "avatar": "enough"}, "status": "1"}]}};
}

module.exports = {
  generateResultDataListDemandBidSimpleResponse
};
