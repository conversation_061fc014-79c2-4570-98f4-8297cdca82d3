
// PaymentDisputeResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成PaymentDisputeResponse模型的模拟数据
 * @returns {PaymentDisputeResponse} 模拟数据
 */
function generatePaymentDisputeResponse() {
  return {"id": 496, "user": {"id": 578, "name": "official", "url": "edge", "avatar": "over"}, "paymentRecordId": 435, "payment": {"id": 628, "name": "surface", "url": "message", "avatar": "color"}, "disputeType": "1", "disputeDescription": "bag", "status": "1", "manage": {"id": 167, "name": "give", "url": "protect", "avatar": "management"}, "manageReply": "maybe", "disputeTime": 1753433976};
}

module.exports = {
  generatePaymentDisputeResponse
};
