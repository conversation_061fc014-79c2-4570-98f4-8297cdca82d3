
// ResultRateConfigResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultRateConfigResponse模型的模拟数据
 * @returns {ResultRateConfigResponse} 模拟数据
 */
function generateResultRateConfigResponse() {
  return {"code": "schedule_sign_log_status_not_null", "data": {"id": 903, "country": "herself", "currency": "especially", "currencyName": "receive", "symbolFloat": 166, "currencySymbol": "off", "rate": 869, "mark": "scene", "createTime": 1753433976, "updateTime": 1753433976}};
}

module.exports = {
  generateResultRateConfigResponse
};
