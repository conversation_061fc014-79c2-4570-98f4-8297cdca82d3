
// ResultDataListAftersalesTypeResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListAftersalesTypeResponse模型的模拟数据
 * @returns {ResultDataListAftersalesTypeResponse} 模拟数据
 */
function generateResultDataListAftersalesTypeResponse() {
  return {"code": "google_auth_code_missing", "data": {"curPage": 580, "maxPage": 699, "total": 7553, "data": [{"id": 693, "parentId": 482, "parentType": {"id": 306, "name": "citizen", "url": "security", "avatar": "appear"}, "status": "1", "statusName": "relationship"}]}};
}

module.exports = {
  generateResultDataListAftersalesTypeResponse
};
