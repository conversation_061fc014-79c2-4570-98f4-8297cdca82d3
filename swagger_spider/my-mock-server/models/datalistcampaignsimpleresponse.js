
// DataListCampaignSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListCampaignSimpleResponse模型的模拟数据
 * @returns {DataListCampaignSimpleResponse} 模拟数据
 */
function generateDataListCampaignSimpleResponse() {
  return {"curPage": 242, "maxPage": 528, "total": 8420, "data": [{"id": 481, "status": "1", "customUrl": "majority", "language": "en_US", "title": "stuff", "coverPic": "born", "tags": [{"id": 250, "name": "black", "url": "race", "avatar": "difference"}, {"id": 199, "name": "wish", "url": "girl", "avatar": "set"}, {"id": 365, "name": "century", "url": "radio", "avatar": "late"}], "itemInfos": [{"id": 35, "item": {"id": 28, "name": "up", "url": "line", "avatar": "save"}, "store": {"id": 269, "name": "baby", "url": "here", "avatar": "including"}}, {"id": 586, "item": {"id": 687, "name": "understand", "url": "performance", "avatar": "need"}, "store": {"id": 219, "name": "wear", "url": "after", "avatar": "stuff"}}, {"id": 810, "item": {"id": 660, "name": "good", "url": "early", "avatar": "doctor"}, "store": {"id": 352, "name": "cut", "url": "feeling", "avatar": "purpose"}}], "createTime": 1753433976}]};
}

module.exports = {
  generateDataListCampaignSimpleResponse
};
