
// ResultBrandServicesListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultBrandServicesListResponse模型的模拟数据
 * @returns {ResultBrandServicesListResponse} 模拟数据
 */
function generateResultBrandServicesListResponse() {
  return {"code": "apply_fail", "data": {"id": 955, "brandId": 529, "servicesId": 689, "sortIndex": "0", "createTime": 1753433976, "fieldList": [{"label": "present", "fieldType": "rich", "fieldName": "list", "required": true, "defaultValue": "mention"}, {"label": "system", "fieldType": "state", "fieldName": "write", "required": false, "defaultValue": "find"}, {"label": "either", "fieldType": "friend", "fieldName": "some", "required": false, "defaultValue": "range"}]}};
}

module.exports = {
  generateResultBrandServicesListResponse
};
