
// ResponseCampaignStatResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseCampaignStatResponse模型的模拟数据
 * @returns {ResponseCampaignStatResponse} 模拟数据
 */
function generateResponseCampaignStatResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 71, "campaignId": 187, "visitCount": 141, "ordersCount": 109, "salesAmount": 465, "salesCount": 155, "saveCount": 285, "updateTime": 1753433976}, "result": {"code": "store_withdraw_submit_failed", "data": {"id": 484, "campaignId": 470, "visitCount": 482, "ordersCount": 294, "salesAmount": 861, "salesCount": 443, "saveCount": 183, "updateTime": 1753433976}}, "errMessageOnly": "other", "successMessage": "send", "errMessage": "mother"};
}

module.exports = {
  generateResponseCampaignStatResponse
};
