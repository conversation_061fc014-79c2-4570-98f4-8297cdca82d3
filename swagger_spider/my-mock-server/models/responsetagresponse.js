
// ResponseTagResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseTagResponse模型的模拟数据
 * @returns {ResponseTagResponse} 模拟数据
 */
function generateResponseTagResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 602, "status": "1", "sortIndex": "0", "useType": "1", "language": "en_US", "tagName": "so", "customUrl": "south", "createTime": 1753433976, "seoKeywords": "cover", "seoDescription": "close"}, "result": {"code": "user_email_confirm_failed", "data": {"id": 297, "status": "1", "sortIndex": "0", "useType": "1", "language": "en_US", "tagName": "discover", "customUrl": "reality", "createTime": 1753433976, "seoKeywords": "whether", "seoDescription": "talk"}}, "errMessageOnly": "story", "successMessage": "deep", "errMessage": "vote"};
}

module.exports = {
  generateResponseTagResponse
};
