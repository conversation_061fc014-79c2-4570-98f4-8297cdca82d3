
// ResultDataListAftersalesResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListAftersalesResponse模型的模拟数据
 * @returns {ResultDataListAftersalesResponse} 模拟数据
 */
function generateResultDataListAftersalesResponse() {
  return {"code": "demand_pay_deposit_failed", "data": {"curPage": 121, "maxPage": 710, "total": 8079, "data": [{"id": 156, "brand": {"id": 613, "name": "despite", "url": "Congress", "avatar": "rock"}, "aftersalesType": {"id": 988, "name": "pretty", "url": "artist", "avatar": "visit"}, "status": "1", "statusName": "color"}]}};
}

module.exports = {
  generateResultDataListAftersalesResponse
};
