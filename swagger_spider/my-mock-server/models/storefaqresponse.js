
// StoreFaqResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成StoreFaqResponse模型的模拟数据
 * @returns {StoreFaqResponse} 模拟数据
 */
function generateStoreFaqResponse() {
  return {"id": 411, "store": {"id": 424, "name": "become", "url": "police", "avatar": "career"}, "item": {"id": 174, "name": "open", "url": "yet", "avatar": "message"}, "status": "1", "language": "en_US", "question": "fly", "answer": "environment", "sortIndex": 859, "createTime": 1753433976, "version": 543};
}

module.exports = {
  generateStoreFaqResponse
};
