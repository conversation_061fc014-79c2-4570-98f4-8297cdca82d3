
// AttrSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成AttrSimpleResponse模型的模拟数据
 * @returns {AttrSimpleResponse} 模拟数据
 */
function generateAttrSimpleResponse() {
  return {"id": 169, "parent": {"id": 787, "name": "decide", "url": "blue", "avatar": "past"}, "status": "1", "attrType": "1", "language": "en_US", "attrName": "on", "customUrl": "memory", "createTime": 1753433976, "updateTime": 1753433976};
}

module.exports = {
  generateAttrSimpleResponse
};
