
// CampaignItemListSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成CampaignItemListSimpleResponse模型的模拟数据
 * @returns {CampaignItemListSimpleResponse} 模拟数据
 */
function generateCampaignItemListSimpleResponse() {
  return {"id": 122, "campaign": {"id": 777, "name": "brother", "url": "audience", "avatar": "position"}, "item": {"id": 73, "name": "identify", "url": "detail", "avatar": "exist"}, "store": {"id": 464, "name": "better", "url": "would", "avatar": "try"}, "createTime": 1753433976};
}

module.exports = {
  generateCampaignItemListSimpleResponse
};
