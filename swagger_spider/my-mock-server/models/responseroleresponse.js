
// ResponseRoleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseRoleResponse模型的模拟数据
 * @returns {ResponseRoleResponse} 模拟数据
 */
function generateResponseRoleResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 261, "roleName": "nice", "mark": "policy", "createTime": 1753433976, "resourcesIds": [287, 246, 45]}, "result": {"code": "pages_type_lang_status_invalid", "data": {"id": 919, "roleName": "other", "mark": "when", "createTime": 1753433976, "resourcesIds": [561, 833]}}, "errMessageOnly": "boy", "successMessage": "forward", "errMessage": "girl"};
}

module.exports = {
  generateResponseRoleResponse
};
