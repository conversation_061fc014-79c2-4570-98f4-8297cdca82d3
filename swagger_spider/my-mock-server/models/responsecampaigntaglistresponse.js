
// ResponseCampaignTagListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseCampaignTagListResponse模型的模拟数据
 * @returns {ResponseCampaignTagListResponse} 模拟数据
 */
function generateResponseCampaignTagListResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 222, "campaignId": 5, "tagId": 766, "createTime": 1753433976}, "result": {"code": "brand_batch_delete_error", "data": {"id": 207, "campaignId": 597, "tagId": 248, "createTime": 1753433976}}, "errMessageOnly": "mind", "successMessage": "modern", "errMessage": "project"};
}

module.exports = {
  generateResponseCampaignTagListResponse
};
