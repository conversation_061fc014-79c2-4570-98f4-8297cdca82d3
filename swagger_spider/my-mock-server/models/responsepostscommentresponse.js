
// ResponsePostsCommentResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponsePostsCommentResponse模型的模拟数据
 * @returns {ResponsePostsCommentResponse} 模拟数据
 */
function generateResponsePostsCommentResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 533, "posts": {"id": 640, "name": "by", "url": "very", "avatar": "discussion"}, "user": {"id": 350, "name": "give", "url": "society", "avatar": "moment"}, "status": "1", "paidStatus": "1", "level": "1", "comment": "network", "createTime": 1753433976, "replyTime": 1753433976, "rating": 612}, "result": {"code": "tag_lang_id_not_exists", "data": {"id": 598, "posts": {"id": 259, "name": "fight", "url": "science", "avatar": "too"}, "user": {"id": 99, "name": "than", "url": "especially", "avatar": "me"}, "status": "1", "paidStatus": "1", "level": "1", "comment": "again", "createTime": 1753433976, "replyTime": 1753433976, "rating": 455}}, "errMessageOnly": "office", "successMessage": "ability", "errMessage": "common"};
}

module.exports = {
  generateResponsePostsCommentResponse
};
