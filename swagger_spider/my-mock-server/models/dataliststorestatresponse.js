
// DataListStoreStatResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListStoreStatResponse模型的模拟数据
 * @returns {DataListStoreStatResponse} 模拟数据
 */
function generateDataListStoreStatResponse() {
  return {"curPage": 988, "maxPage": 903, "total": 4438, "data": [{"id": 753, "store": {"id": 701, "name": "eight", "url": "control", "avatar": "enough"}, "visitCount": 449, "ordersCount": 800, "salesAmount": 918, "salesCount": 813, "salesProfit": 62, "refundAmount": 888, "withdrawAmount": 670, "riskChatCount": 400}]};
}

module.exports = {
  generateDataListStoreStatResponse
};
