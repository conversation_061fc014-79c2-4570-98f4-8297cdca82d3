
// ResultRatingResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultRatingResponse模型的模拟数据
 * @returns {ResultRatingResponse} 模拟数据
 */
function generateResultRatingResponse() {
  return {"code": "vip_name_exists", "data": {"id": 172, "status": "1", "statusName": "enough", "ratingType": "1", "ratingTypeName": "enough", "createTime": 1753433976, "language": "en_US", "langStatus": "1", "langStatusName": "right", "ratingName": "so"}};
}

module.exports = {
  generateResultRatingResponse
};
