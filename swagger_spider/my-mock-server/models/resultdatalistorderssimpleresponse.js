
// ResultDataListOrdersSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListOrdersSimpleResponse模型的模拟数据
 * @returns {ResultDataListOrdersSimpleResponse} 模拟数据
 */
function generateResultDataListOrdersSimpleResponse() {
  return {"code": "badge_custom_url_exists", "data": {"curPage": 662, "maxPage": 681, "total": 2430, "data": [{"id": 96, "user": {"id": 457, "name": "prevent", "url": "news", "avatar": "leg"}, "item": {"id": 75, "name": "scene", "url": "indeed", "avatar": "wish"}, "posts": {"id": 485, "name": "where", "url": "majority", "avatar": "much"}, "demand": {"id": 639, "name": "read", "url": "situation", "avatar": "black"}}, {"id": 903, "user": {"id": 456, "name": "spring", "url": "relationship", "avatar": "long"}, "item": {"id": 740, "name": "politics", "url": "religious", "avatar": "who"}, "posts": {"id": 339, "name": "share", "url": "exactly", "avatar": "make"}, "demand": {"id": 806, "name": "site", "url": "your", "avatar": "too"}}]}};
}

module.exports = {
  generateResultDataListOrdersSimpleResponse
};
