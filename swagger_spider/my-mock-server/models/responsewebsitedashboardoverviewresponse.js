
// ResponseWebsiteDashboardOverviewResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseWebsiteDashboardOverviewResponse模型的模拟数据
 * @returns {ResponseWebsiteDashboardOverviewResponse} 模拟数据
 */
function generateResponseWebsiteDashboardOverviewResponse() {
  return {"code": 0, "msg": "success", "data": {"websiteFlowResponse": {"websiteFlowCount": 6261, "websiteAccessCount": 3424, "loginDeviceCount": 9251, "onlineUserCount": 1731}, "websiteUserCountResponse": {"websiteUserCount": 3390, "websitePageBounceRate": 949.09, "websiteSessionDurationCount": 6801, "websiteConversionRate": 860.68}, "websiteNewUserCountResponse": {"newUserCount": 5414, "activeUserCount": 7012, "todayPayUserCount": 9931, "yesterdayPayUserCount": 3972}, "websiteUserKYCCountResponse": {"todayTotalUserKYCCount": 1377, "authenticatedTodayTotalUserKYCCount": 2300, "refuseAuthenticatedTodayTotalUserKYCCount": 4722, "newUserTodayTotalUserKYCCount": 613}, "demandAuditingCountResponse": {"todayDemandAuditingCount": 7811, "todayNewDemandAuditingCount": 1376, "todayDemandAuditingPassCount": 2644, "todayDemandWinningCount": 6805}, "ordersCountResponse": {"todayOrdersCount": 1749, "todayOrdersPendingCount": 45, "todayOrdersAverageUnitPrice": 959, "todayOrdersAverageQuantity": 330}, "ordersRefundCountResponse": {"todayOrderRefundCount": 1224, "todayNewOrderRefundCount": 5716, "todayOrderRefundCompletedCount": 7695, "todayOrderRefundRefusedCount": 9621}, "ordersExchangeCountResponse": {"todayOrderExchangeCount": 2306, "todayNewOrdersExchangeCount": 2332, "todayCompletedOrderExchangeCount": 4799, "todayRefuseOrderExchangeCount": 5903}, "ordersTicketCountResponse": {"todayOrdersTicketCount": 315, "todayNewOrdersTicketCount": 7392, "todayCompletedOrdersTicketCount": 9676, "todayTimeoutOrdersTicketCount": 1753433976}, "customerComplaintCountResponse": {"todayComplaintCount": 4395, "todayNewComplaintCount": 3708, "todayCompletedComplaintCount": 3912}}, "result": {"code": "schedule_not_exists", "data": {"websiteFlowResponse": {"websiteFlowCount": 9746, "websiteAccessCount": 3684, "loginDeviceCount": 1050, "onlineUserCount": 9633}, "websiteUserCountResponse": {"websiteUserCount": 7652, "websitePageBounceRate": 433.15, "websiteSessionDurationCount": 2514, "websiteConversionRate": 447.86}, "websiteNewUserCountResponse": {"newUserCount": 903, "activeUserCount": 1373, "todayPayUserCount": 9375, "yesterdayPayUserCount": 9074}, "websiteUserKYCCountResponse": {"todayTotalUserKYCCount": 7517, "authenticatedTodayTotalUserKYCCount": 5775, "refuseAuthenticatedTodayTotalUserKYCCount": 6743, "newUserTodayTotalUserKYCCount": 5878}, "demandAuditingCountResponse": {"todayDemandAuditingCount": 8292, "todayNewDemandAuditingCount": 5760, "todayDemandAuditingPassCount": 128, "todayDemandWinningCount": 6168}, "ordersCountResponse": {"todayOrdersCount": 8373, "todayOrdersPendingCount": 532, "todayOrdersAverageUnitPrice": 313, "todayOrdersAverageQuantity": 451}, "ordersRefundCountResponse": {"todayOrderRefundCount": 4101, "todayNewOrderRefundCount": 7684, "todayOrderRefundCompletedCount": 4064, "todayOrderRefundRefusedCount": 5914}, "ordersExchangeCountResponse": {"todayOrderExchangeCount": 4987, "todayNewOrdersExchangeCount": 1416, "todayCompletedOrderExchangeCount": 9746, "todayRefuseOrderExchangeCount": 248}, "ordersTicketCountResponse": {"todayOrdersTicketCount": 3044, "todayNewOrdersTicketCount": 7154, "todayCompletedOrdersTicketCount": 3189, "todayTimeoutOrdersTicketCount": 1753433976}, "customerComplaintCountResponse": {"todayComplaintCount": 4398, "todayNewComplaintCount": 6786, "todayCompletedComplaintCount": 5650}}}, "errMessageOnly": "trial", "successMessage": "investment", "errMessage": "cultural"};
}

module.exports = {
  generateResponseWebsiteDashboardOverviewResponse
};
