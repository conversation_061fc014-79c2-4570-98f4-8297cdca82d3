
// PostsTypeRequest 模型
// 由SwaggerCrawler自动生成

/**
 * 生成PostsTypeRequest模型的模拟数据
 * @returns {PostsTypeRequest} 模拟数据
 */
function generatePostsTypeRequest() {
  return {"parentId": 677, "customUrl": "operation", "status": "DISABLED", "language": "en_US", "seoKeywords": "reach", "seoDescription": "Democrat", "typeName": "about", "coverPic": "campaign", "summary": "particularly", "typeContent": "American"};
}

module.exports = {
  generatePostsTypeRequest
};
