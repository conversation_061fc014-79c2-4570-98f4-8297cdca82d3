
// ResultPostsCommentRatingResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultPostsCommentRatingResponse模型的模拟数据
 * @returns {ResultPostsCommentRatingResponse} 模拟数据
 */
function generateResultPostsCommentRatingResponse() {
  return {"code": "orders_comment_update_failed", "data": {"id": 62, "posts": {"id": 104, "name": "why", "url": "camera", "avatar": "professor"}, "user": {"id": 611, "name": "marriage", "url": "special", "avatar": "watch"}, "rating": {"id": 741, "name": "bring", "url": "game", "avatar": "north"}, "score": "1", "createTime": 1753433976, "updateTime": 1753433976}};
}

module.exports = {
  generateResultPostsCommentRatingResponse
};
