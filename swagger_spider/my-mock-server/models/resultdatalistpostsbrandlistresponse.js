
// ResultDataListPostsBrandListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListPostsBrandListResponse模型的模拟数据
 * @returns {ResultDataListPostsBrandListResponse} 模拟数据
 */
function generateResultDataListPostsBrandListResponse() {
  return {"code": "manage_permissions_add_failed", "data": {"curPage": 615, "maxPage": 872, "total": 2374, "data": [{"id": 728, "posts": {"id": 538, "name": "piece", "url": "film", "avatar": "live"}, "brand": {"id": 504, "name": "fund", "url": "fly", "avatar": "create"}, "createTime": 1753433976}]}};
}

module.exports = {
  generateResultDataListPostsBrandListResponse
};
