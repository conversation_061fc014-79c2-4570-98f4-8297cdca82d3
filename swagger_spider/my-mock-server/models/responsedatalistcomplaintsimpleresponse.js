
// ResponseDataListComplaintSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListComplaintSimpleResponse模型的模拟数据
 * @returns {ResponseDataListComplaintSimpleResponse} 模拟数据
 */
function generateResponseDataListComplaintSimpleResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 252, "maxPage": 686, "total": 5735, "data": [{"id": 543, "reportUser": {"id": 107, "name": "wife", "url": "both", "avatar": "director"}, "store": {"id": 108, "name": "price", "url": "hope", "avatar": "yes"}, "item": {"id": 470, "name": "summer", "url": "speech", "avatar": "decide"}, "status": "1"}]}, "result": {"code": "badge_custom_url_exists", "data": {"curPage": 793, "maxPage": 106, "total": 9712, "data": [{"id": 169, "reportUser": {"id": 837, "name": "might", "url": "project", "avatar": "enough"}, "store": {"id": 125, "name": "business", "url": "before", "avatar": "within"}, "item": {"id": 918, "name": "able", "url": "anyone", "avatar": "short"}, "status": "1"}]}}, "errMessageOnly": "guess", "successMessage": "partner", "errMessage": "Republican"};
}

module.exports = {
  generateResponseDataListComplaintSimpleResponse
};
