
// ResultDataListUserBindResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListUserBindResponse模型的模拟数据
 * @returns {ResultDataListUserBindResponse} 模拟数据
 */
function generateResultDataListUserBindResponse() {
  return {"code": "chat_can_not_start_by_self", "data": {"curPage": 329, "maxPage": 60, "total": 222, "data": [{"id": 469, "user": {"id": 552, "name": "enjoy", "url": "huge", "avatar": "along"}, "platform": "1", "platformId": "risk", "bindTime": 1753433975}, {"id": 573, "user": {"id": 872, "name": "he", "url": "through", "avatar": "woman"}, "platform": "1", "platformId": "so", "bindTime": 1753433975}]}};
}

module.exports = {
  generateResultDataListUserBindResponse
};
