
// ResponseDataListPostsPaidResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListPostsPaidResponse模型的模拟数据
 * @returns {ResponseDataListPostsPaidResponse} 模拟数据
 */
function generateResponseDataListPostsPaidResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 220, "maxPage": 554, "total": 9146, "data": [{"id": 994, "posts": {"id": 245, "name": "nearly", "url": "over", "avatar": "nation"}, "user": {"id": 328, "name": "full", "url": "beautiful", "avatar": "follow"}, "status": "1", "paidPrice": 946}, {"id": 921, "posts": {"id": 496, "name": "dinner", "url": "art", "avatar": "successful"}, "user": {"id": 591, "name": "early", "url": "store", "avatar": "every"}, "status": "1", "paidPrice": 393}, {"id": 614, "posts": {"id": 743, "name": "compare", "url": "hotel", "avatar": "which"}, "user": {"id": 520, "name": "last", "url": "morning", "avatar": "medical"}, "status": "1", "paidPrice": 491}]}, "result": {"code": "service_no_change_error", "data": {"curPage": 170, "maxPage": 973, "total": 8914, "data": [{"id": 331, "posts": {"id": 324, "name": "table", "url": "allow", "avatar": "prevent"}, "user": {"id": 701, "name": "away", "url": "attack", "avatar": "brother"}, "status": "1", "paidPrice": 138}, {"id": 470, "posts": {"id": 720, "name": "head", "url": "soldier", "avatar": "trouble"}, "user": {"id": 502, "name": "eight", "url": "stock", "avatar": "provide"}, "status": "1", "paidPrice": 832}]}}, "errMessageOnly": "lot", "successMessage": "movement", "errMessage": "pass"};
}

module.exports = {
  generateResponseDataListPostsPaidResponse
};
