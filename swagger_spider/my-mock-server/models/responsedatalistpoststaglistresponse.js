
// ResponseDataListPostsTagListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListPostsTagListResponse模型的模拟数据
 * @returns {ResponseDataListPostsTagListResponse} 模拟数据
 */
function generateResponseDataListPostsTagListResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 102, "maxPage": 317, "total": 8530, "data": [{"id": 352, "posts": {"id": 632, "name": "staff", "url": "politics", "avatar": "other"}, "tag": {"id": 351, "name": "top", "url": "local", "avatar": "often"}, "createTime": 1753433975}, {"id": 236, "posts": {"id": 881, "name": "now", "url": "hard", "avatar": "case"}, "tag": {"id": 110, "name": "hundred", "url": "attention", "avatar": "in"}, "createTime": 1753433975}]}, "result": {"code": "chat_can_not_start_by_self", "data": {"curPage": 463, "maxPage": 620, "total": 105, "data": [{"id": 191, "posts": {"id": 437, "name": "approach", "url": "continue", "avatar": "indeed"}, "tag": {"id": 50, "name": "pattern", "url": "PM", "avatar": "that"}, "createTime": 1753433975}]}}, "errMessageOnly": "consider", "successMessage": "maybe", "errMessage": "bring"};
}

module.exports = {
  generateResponseDataListPostsTagListResponse
};
