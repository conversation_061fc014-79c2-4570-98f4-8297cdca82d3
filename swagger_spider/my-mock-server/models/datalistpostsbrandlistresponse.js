
// DataListPostsBrandListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListPostsBrandListResponse模型的模拟数据
 * @returns {DataListPostsBrandListResponse} 模拟数据
 */
function generateDataListPostsBrandListResponse() {
  return {"curPage": 497, "maxPage": 939, "total": 9144, "data": [{"id": 570, "posts": {"id": 7, "name": "room", "url": "reveal", "avatar": "away"}, "brand": {"id": 328, "name": "performance", "url": "within", "avatar": "offer"}, "createTime": 1753433976}, {"id": 441, "posts": {"id": 541, "name": "yet", "url": "perhaps", "avatar": "step"}, "brand": {"id": 247, "name": "amount", "url": "just", "avatar": "southern"}, "createTime": 1753433976}]};
}

module.exports = {
  generateDataListPostsBrandListResponse
};
