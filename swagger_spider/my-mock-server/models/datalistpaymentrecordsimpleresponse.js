
// DataListPaymentRecordSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListPaymentRecordSimpleResponse模型的模拟数据
 * @returns {DataListPaymentRecordSimpleResponse} 模拟数据
 */
function generateDataListPaymentRecordSimpleResponse() {
  return {"curPage": 649, "maxPage": 571, "total": 3467, "data": [{"id": 767, "user": {"id": 839, "name": "reflect", "url": "eat", "avatar": "career"}, "payment": {"id": 795, "name": "later", "url": "even", "avatar": "structure"}, "transactionId": "magazine", "amount": 861, "currency": "audience", "status": "1", "paymentTime": 1753433976}]};
}

module.exports = {
  generateDataListPaymentRecordSimpleResponse
};
