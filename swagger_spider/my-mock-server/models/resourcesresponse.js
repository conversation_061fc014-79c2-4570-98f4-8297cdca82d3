
// ResourcesResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResourcesResponse模型的模拟数据
 * @returns {ResourcesResponse} 模拟数据
 */
function generateResourcesResponse() {
  return {"id": 404, "parentId": 831, "level": "1", "status": "1", "sortIndex": 859, "showStatus": "1", "resourcesKey": "church", "menuName": "others", "menuPath": "fact", "createTime": 1753433976};
}

module.exports = {
  generateResourcesResponse
};
