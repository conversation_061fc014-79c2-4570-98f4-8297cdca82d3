// DynamicswaggerResources_2Response 模型
// 由SwaggerCrawler动态生成

/**
 * 生成DynamicswaggerResources_2Response模型的模拟数据
 * @returns {DynamicswaggerResources_2Response} 模拟数据
 */
function generateDynamicswaggerResources_2Response() {
  return [{"name": "develop","url": "range","swaggerVersion": "per","location": "himself"},{"name": "skill","url": "positive","swaggerVersion": "similar","location": "language"},{"name": "across","url": "soldier","swaggerVersion": "decade","location": "hundred"}];
}

module.exports = {
  generateDynamicswaggerResources_2Response
};
