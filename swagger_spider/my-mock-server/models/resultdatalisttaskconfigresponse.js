
// ResultDataListTaskConfigResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListTaskConfigResponse模型的模拟数据
 * @returns {ResultDataListTaskConfigResponse} 模拟数据
 */
function generateResultDataListTaskConfigResponse() {
  return {"code": "pages_id_can_not_be_null", "data": {"curPage": 161, "maxPage": 885, "total": 8991, "data": [{"id": 961, "status": "1", "taskName": "machine", "mark": "break", "taskClass": "certainly"}, {"id": 515, "status": "1", "taskName": "event", "mark": "finally", "taskClass": "already"}, {"id": 268, "status": "1", "taskName": "way", "mark": "attack", "taskClass": "media"}]}};
}

module.exports = {
  generateResultDataListTaskConfigResponse
};
