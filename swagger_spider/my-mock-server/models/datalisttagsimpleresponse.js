
// DataListTagSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListTagSimpleResponse模型的模拟数据
 * @returns {DataListTagSimpleResponse} 模拟数据
 */
function generateDataListTagSimpleResponse() {
  return {"curPage": 467, "maxPage": 234, "total": 2979, "data": [{"id": 847, "status": "1", "sortIndex": "0", "useType": "1", "language": "en_US", "tagName": "lot", "customUrl": "research", "createTime": 1753433976}, {"id": 170, "status": "1", "sortIndex": "0", "useType": "1", "language": "en_US", "tagName": "unit", "customUrl": "what", "createTime": 1753433976}, {"id": 303, "status": "1", "sortIndex": "0", "useType": "1", "language": "en_US", "tagName": "indeed", "customUrl": "with", "createTime": 1753433976}]};
}

module.exports = {
  generateDataListTagSimpleResponse
};
