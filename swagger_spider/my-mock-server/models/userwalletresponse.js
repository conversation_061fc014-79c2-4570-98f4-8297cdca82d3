
// UserWalletResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成UserWalletResponse模型的模拟数据
 * @returns {UserWalletResponse} 模拟数据
 */
function generateUserWalletResponse() {
  return {"id": 94, "status": "1", "walletName": "someone", "user": {"id": 209, "name": "financial", "url": "hand", "avatar": "project"}, "walletType": "1", "country": "suggest", "firstName": "without", "receiveAccount": "good", "createTime": **********, "isDefault": "1"};
}

module.exports = {
  generateUserWalletResponse
};
