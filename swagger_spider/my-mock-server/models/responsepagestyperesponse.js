
// ResponsePagesTypeResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponsePagesTypeResponse模型的模拟数据
 * @returns {ResponsePagesTypeResponse} 模拟数据
 */
function generateResponsePagesTypeResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 525, "status": "1", "statusName": "often", "langStatus": "1", "langStatusName": "marriage", "customUrl": "team", "createTime": 1753433976, "updateTime": 1753433976, "language": "en_US", "typeName": "opportunity"}, "result": {"code": "schedule_from_invalid", "data": {"id": 401, "status": "1", "statusName": "attack", "langStatus": "1", "langStatusName": "economy", "customUrl": "clear", "createTime": 1753433976, "updateTime": 1753433976, "language": "en_US", "typeName": "social"}}, "errMessageOnly": "off", "successMessage": "guess", "errMessage": "race"};
}

module.exports = {
  generateResponsePagesTypeResponse
};
