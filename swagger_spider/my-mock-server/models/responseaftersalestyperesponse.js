
// ResponseAftersalesTypeResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseAftersalesTypeResponse模型的模拟数据
 * @returns {ResponseAftersalesTypeResponse} 模拟数据
 */
function generateResponseAftersalesTypeResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 173, "parentId": 872, "parentType": {"id": 125, "name": "away", "url": "now", "avatar": "new"}, "status": "1", "statusName": "tough", "customUrl": "yeah", "createTime": 1753433976, "updateTime": 1753433976, "language": "en_US", "typeName": "point"}, "result": {"code": "login_first", "data": {"id": 498, "parentId": 526, "parentType": {"id": 541, "name": "pull", "url": "pattern", "avatar": "pass"}, "status": "1", "statusName": "simply", "customUrl": "million", "createTime": 1753433976, "updateTime": 1753433976, "language": "en_US", "typeName": "number"}}, "errMessageOnly": "enter", "successMessage": "region", "errMessage": "professor"};
}

module.exports = {
  generateResponseAftersalesTypeResponse
};
