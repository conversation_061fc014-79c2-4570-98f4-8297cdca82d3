
// ResponsePostsBrandListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponsePostsBrandListResponse模型的模拟数据
 * @returns {ResponsePostsBrandListResponse} 模拟数据
 */
function generateResponsePostsBrandListResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 418, "posts": {"id": 395, "name": "commercial", "url": "read", "avatar": "experience"}, "brand": {"id": 578, "name": "fight", "url": "decade", "avatar": "voice"}, "createTime": 1753433976}, "result": {"code": "phone_exists", "data": {"id": 400, "posts": {"id": 89, "name": "thousand", "url": "daughter", "avatar": "carry"}, "brand": {"id": 503, "name": "chance", "url": "kid", "avatar": "policy"}, "createTime": 1753433976}}, "errMessageOnly": "foreign", "successMessage": "will", "errMessage": "each"};
}

module.exports = {
  generateResponsePostsBrandListResponse
};
