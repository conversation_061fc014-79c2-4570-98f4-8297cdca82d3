
// SearchResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成SearchResponse模型的模拟数据
 * @returns {SearchResponse} 模拟数据
 */
function generateSearchResponse() {
  return {"id": 752, "searchType": "1", "searchTypeName": "suffer", "brand": {"id": 329, "name": "again", "url": "visit", "avatar": "cost"}, "status": "1", "statusName": "phone", "sortIndex": 187, "searchCount": 557, "createTime": 1753433976, "updateTime": 1753433976};
}

module.exports = {
  generateSearchResponse
};
