
// ResultDataListServicesSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListServicesSimpleResponse模型的模拟数据
 * @returns {ResultDataListServicesSimpleResponse} 模拟数据
 */
function generateResultDataListServicesSimpleResponse() {
  return {"code": "schedule_handover_log_add_failed", "data": {"curPage": 797, "maxPage": 514, "total": 1840, "data": [{"id": 787, "status": "1", "language": "en_US", "servicesName": "training", "customUrl": "culture"}, {"id": 433, "status": "1", "language": "en_US", "servicesName": "either", "customUrl": "control"}, {"id": 986, "status": "1", "language": "en_US", "servicesName": "capital", "customUrl": "find"}]}};
}

module.exports = {
  generateResultDataListServicesSimpleResponse
};
