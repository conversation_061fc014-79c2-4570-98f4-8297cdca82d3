
// PostsCommentRequest 模型
// 由SwaggerCrawler自动生成

/**
 * 生成PostsCommentRequest模型的模拟数据
 * @returns {PostsCommentRequest} 模拟数据
 */
function generatePostsCommentRequest() {
  return {"postsId": 371, "userId": 87, "paidStatus": "DEFAULT", "status": "DELETED", "level": "1", "replyTime": 1753433975, "rating": 750, "likeCount": 429, "unLikeCount": 16, "comment": "chair"};
}

module.exports = {
  generatePostsCommentRequest
};
