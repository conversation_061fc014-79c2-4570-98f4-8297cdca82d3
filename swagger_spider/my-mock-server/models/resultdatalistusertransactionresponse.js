
// ResultDataListUserTransactionResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListUserTransactionResponse模型的模拟数据
 * @returns {ResultDataListUserTransactionResponse} 模拟数据
 */
function generateResultDataListUserTransactionResponse() {
  return {"code": "file_upload_fail", "data": {"curPage": 675, "maxPage": 424, "total": 3321, "data": [{"id": 518, "user": {"id": 559, "name": "adult", "url": "town", "avatar": "blue"}, "store": {"id": 513, "name": "all", "url": "pick", "avatar": "watch"}, "changeType": "1", "changeMoney": 854}]}};
}

module.exports = {
  generateResultDataListUserTransactionResponse
};
