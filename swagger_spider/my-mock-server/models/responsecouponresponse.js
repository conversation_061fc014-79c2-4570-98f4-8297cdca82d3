
// ResponseCouponResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseCouponResponse模型的模拟数据
 * @returns {ResponseCouponResponse} 模拟数据
 */
function generateResponseCouponResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 693, "brandId": 841, "storeId": 98, "servicesId": 317, "itemId": 236, "actName": "check", "couponStatus": "1", "couponCode": "letter", "discount": 63, "moneyMin": 376}, "result": {"code": "chat_not_exists", "data": {"id": 673, "brandId": 837, "storeId": 895, "servicesId": 254, "itemId": 998, "actName": "land", "couponStatus": "1", "couponCode": "coach", "discount": 516, "moneyMin": 255}}, "errMessageOnly": "chance", "successMessage": "role", "errMessage": "thing"};
}

module.exports = {
  generateResponseCouponResponse
};
