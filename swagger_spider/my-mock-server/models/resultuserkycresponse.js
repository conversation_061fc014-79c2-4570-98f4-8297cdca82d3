
// ResultUserKycResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultUserKycResponse模型的模拟数据
 * @returns {ResultUserKycResponse} 模拟数据
 */
function generateResultUserKycResponse() {
  return {"code": "quick_message_update_error", "data": {"id": 319, "status": "1", "user": {"id": 898, "name": "wall", "url": "challenge", "avatar": "thousand"}, "version": 233, "kycType": "1", "firstName": "as", "lastName": "film", "birthDate": "2008-08-03T03:11:10.336503", "idPic": "road", "handsPic": "behavior"}};
}

module.exports = {
  generateResultUserKycResponse
};
