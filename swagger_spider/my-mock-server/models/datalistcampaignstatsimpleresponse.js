
// DataListCampaignStatSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListCampaignStatSimpleResponse模型的模拟数据
 * @returns {DataListCampaignStatSimpleResponse} 模拟数据
 */
function generateDataListCampaignStatSimpleResponse() {
  return {"curPage": 256, "maxPage": 188, "total": 9742, "data": [{"id": 226, "campaign": {"id": 192, "name": "ready", "url": "check", "avatar": "good"}, "visitCount": 604, "ordersCount": 148, "salesAmount": 826, "salesCount": 123, "saveCount": 658, "updateTime": 1753433976}]};
}

module.exports = {
  generateDataListCampaignStatSimpleResponse
};
