
// StoreAuditingCountResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成StoreAuditingCountResponse模型的模拟数据
 * @returns {StoreAuditingCountResponse} 模拟数据
 */
function generateStoreAuditingCountResponse() {
  return {"todayStoreAuditingCount": 580, "todayNewStoreAuditingCount": 5332, "todayPassStoreAuditingCount": 8478, "todayRefuseStoreAuditingCount": 389};
}

module.exports = {
  generateStoreAuditingCountResponse
};
