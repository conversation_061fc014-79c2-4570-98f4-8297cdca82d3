
// BrandServicesListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成BrandServicesListResponse模型的模拟数据
 * @returns {BrandServicesListResponse} 模拟数据
 */
function generateBrandServicesListResponse() {
  return {"id": 273, "brandId": 759, "servicesId": 482, "sortIndex": "0", "createTime": 1753433976, "fieldList": [{"label": "data", "fieldType": "market", "fieldName": "painting", "required": true, "defaultValue": "cultural", "placeholder": "debate", "valueList": [{}, {}, {}]}, {"label": "south", "fieldType": "hear", "fieldName": "during", "required": false, "defaultValue": "now", "placeholder": "of", "valueList": [{}, {}, {}]}, {"label": "test", "fieldType": "manage", "fieldName": "truth", "required": true, "defaultValue": "add", "placeholder": "room", "valueList": [{}, {}, {}]}]};
}

module.exports = {
  generateBrandServicesListResponse
};
