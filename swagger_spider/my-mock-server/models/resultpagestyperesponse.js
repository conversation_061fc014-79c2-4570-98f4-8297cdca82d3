
// ResultPagesTypeResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultPagesTypeResponse模型的模拟数据
 * @returns {ResultPagesTypeResponse} 模拟数据
 */
function generateResultPagesTypeResponse() {
  return {"code": "resources_add_error", "data": {"id": 391, "status": "1", "statusName": "manage", "langStatus": "1", "langStatusName": "fight", "customUrl": "born", "createTime": 1753433976, "updateTime": 1753433976, "language": "en_US", "typeName": "trouble"}};
}

module.exports = {
  generateResultPagesTypeResponse
};
