
// DataListAftersalesTypeResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListAftersalesTypeResponse模型的模拟数据
 * @returns {DataListAftersalesTypeResponse} 模拟数据
 */
function generateDataListAftersalesTypeResponse() {
  return {"curPage": 462, "maxPage": 898, "total": 8470, "data": [{"id": 133, "parentId": 797, "parentType": {"id": 414, "name": "office", "url": "kind", "avatar": "only"}, "status": "1", "statusName": "live", "customUrl": "condition", "createTime": 1753433976, "updateTime": 1753433976, "language": "en_US", "typeName": "remain"}]};
}

module.exports = {
  generateDataListAftersalesTypeResponse
};
