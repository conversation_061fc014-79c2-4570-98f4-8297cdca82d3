
// ResponseItemWholesaleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseItemWholesaleResponse模型的模拟数据
 * @returns {ResponseItemWholesaleResponse} 模拟数据
 */
function generateResponseItemWholesaleResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 639, "item": {"id": 681, "name": "be", "url": "director", "avatar": "author"}, "status": "1", "quantity": 724, "discount": 931, "unitPrice": 160, "createTime": 1753433976, "updateTime": 1753433976}, "result": {"code": "attr_value_exists", "data": {"id": 625, "item": {"id": 942, "name": "where", "url": "season", "avatar": "talk"}, "status": "1", "quantity": 749, "discount": 761, "unitPrice": 133, "createTime": 1753433976, "updateTime": 1753433976}}, "errMessageOnly": "boy", "successMessage": "experience", "errMessage": "coach"};
}

module.exports = {
  generateResponseItemWholesaleResponse
};
