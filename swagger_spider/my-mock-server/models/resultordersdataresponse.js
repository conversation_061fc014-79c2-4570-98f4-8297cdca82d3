
// ResultOrdersDataResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultOrdersDataResponse模型的模拟数据
 * @returns {ResultOrdersDataResponse} 模拟数据
 */
function generateResultOrdersDataResponse() {
  return {"code": "user_has_telegram", "data": {"id": 667, "ordersId": 530, "deliveryTime": 1753433976, "dataType": "1", "downloadTime": 1753433976, "fieldList": "approach", "dataValueList": "idea"}};
}

module.exports = {
  generateResultOrdersDataResponse
};
