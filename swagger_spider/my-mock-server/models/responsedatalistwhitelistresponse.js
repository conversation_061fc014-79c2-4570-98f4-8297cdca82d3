
// ResponseDataListWhitelistResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListWhitelistResponse模型的模拟数据
 * @returns {ResponseDataListWhitelistResponse} 模拟数据
 */
function generateResponseDataListWhitelistResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 296, "maxPage": 653, "total": 805, "data": [{"id": 984, "status": "1", "language": "en_US", "itemType": "1", "itemValue": "because"}, {"id": 416, "status": "1", "language": "en_US", "itemType": "1", "itemValue": "magazine"}, {"id": 685, "status": "1", "language": "en_US", "itemType": "1", "itemValue": "nearly"}]}, "result": {"code": "brand_lang_add_brand_id_invalid", "data": {"curPage": 882, "maxPage": 974, "total": 2575, "data": [{"id": 660, "status": "1", "language": "en_US", "itemType": "1", "itemValue": "treat"}, {"id": 509, "status": "1", "language": "en_US", "itemType": "1", "itemValue": "say"}]}}, "errMessageOnly": "newspaper", "successMessage": "happen", "errMessage": "response"};
}

module.exports = {
  generateResponseDataListWhitelistResponse
};
