
// ResultDataListItemTagListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListItemTagListResponse模型的模拟数据
 * @returns {ResultDataListItemTagListResponse} 模拟数据
 */
function generateResultDataListItemTagListResponse() {
  return {"code": "schedule_sign_log_review_status_invalid", "data": {"curPage": 934, "maxPage": 822, "total": 3309, "data": [{"id": 859, "item": {"id": 166, "name": "establish", "url": "wish", "avatar": "cultural"}, "tag": {"id": 158, "name": "appear", "url": "relationship", "avatar": "reach"}, "createTime": 1753433976}, {"id": 845, "item": {"id": 506, "name": "Democrat", "url": "thank", "avatar": "power"}, "tag": {"id": 663, "name": "market", "url": "move", "avatar": "total"}, "createTime": 1753433976}]}};
}

module.exports = {
  generateResultDataListItemTagListResponse
};
