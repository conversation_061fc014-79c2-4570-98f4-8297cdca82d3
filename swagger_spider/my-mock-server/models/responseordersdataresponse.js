
// ResponseOrdersDataResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseOrdersDataResponse模型的模拟数据
 * @returns {ResponseOrdersDataResponse} 模拟数据
 */
function generateResponseOrdersDataResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 291, "ordersId": 692, "deliveryTime": 1753433976, "dataType": "1", "downloadTime": 1753433976, "fieldList": "stay", "dataValueList": "establish"}, "result": {"code": "faq_id_not_null", "data": {"id": 335, "ordersId": 977, "deliveryTime": 1753433976, "dataType": "1", "downloadTime": 1753433976, "fieldList": "media", "dataValueList": "share"}}, "errMessageOnly": "report", "successMessage": "yard", "errMessage": "pull"};
}

module.exports = {
  generateResponseOrdersDataResponse
};
