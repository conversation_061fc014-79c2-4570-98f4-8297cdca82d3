
// ItemAftersalesStatResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ItemAftersalesStatResponse模型的模拟数据
 * @returns {ItemAftersalesStatResponse} 模拟数据
 */
function generateItemAftersalesStatResponse() {
  return {"id": 17, "aftersalesId": 974, "store": {"id": 861, "name": "door", "url": "career", "avatar": "themselves"}, "item": {"id": 119, "name": "certainly", "url": "affect", "avatar": "good"}, "hitCount": 60};
}

module.exports = {
  generateItemAftersalesStatResponse
};
