
// ResponseSystemConfigResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseSystemConfigResponse模型的模拟数据
 * @returns {ResponseSystemConfigResponse} 模拟数据
 */
function generateResponseSystemConfigResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 803, "configKey": "much", "configClass": "growth", "configValue": "player", "mark": "treat", "createTime": 1753433976, "updateTime": 1753433976}, "result": {"code": "schedule_month_not_null", "data": {"id": 130, "configKey": "low", "configClass": "shake", "configValue": "international", "mark": "mission", "createTime": 1753433976, "updateTime": 1753433976}}, "errMessageOnly": "stage", "successMessage": "size", "errMessage": "he"};
}

module.exports = {
  generateResponseSystemConfigResponse
};
