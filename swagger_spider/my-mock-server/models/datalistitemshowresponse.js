
// DataListItemShowResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListItemShowResponse模型的模拟数据
 * @returns {DataListItemShowResponse} 模拟数据
 */
function generateDataListItemShowResponse() {
  return {"curPage": 112, "maxPage": 810, "total": 7139, "data": [{"id": 173, "item": {"id": 177, "name": "home", "url": "follow", "avatar": "a"}, "status": "1", "sortIndex": 317, "version": 378, "language": "en_US", "title": "travel", "summary": "that", "showData": "my", "createTime": 1753433976}, {"id": 321, "item": {"id": 307, "name": "answer", "url": "should", "avatar": "surface"}, "status": "1", "sortIndex": 987, "version": 198, "language": "en_US", "title": "accept", "summary": "serve", "showData": "whose", "createTime": 1753433976}]};
}

module.exports = {
  generateDataListItemShowResponse
};
