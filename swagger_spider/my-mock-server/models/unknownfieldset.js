
// UnknownFieldSet 模型
// 由SwaggerCrawler自动生成

/**
 * 生成UnknownFieldSet模型的模拟数据
 * @returns {UnknownFieldSet} 模拟数据
 */
function generateUnknownFieldSet() {
  return {"initialized": true, "defaultInstanceForType": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 111, "serializedSizeAsMessageSet": 827, "empty": true}, "parserForType": {}, "serializedSize": 354, "serializedSizeAsMessageSet": 785, "empty": false};
}

module.exports = {
  generateUnknownFieldSet
};
