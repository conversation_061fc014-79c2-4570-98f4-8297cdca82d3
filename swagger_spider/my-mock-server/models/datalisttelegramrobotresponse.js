
// DataListTelegramRobotResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListTelegramRobotResponse模型的模拟数据
 * @returns {DataListTelegramRobotResponse} 模拟数据
 */
function generateDataListTelegramRobotResponse() {
  return {"curPage": 19, "maxPage": 99, "total": 7693, "data": [{"id": 329, "status": "1", "name": "you", "botName": "skill", "username": "police", "token": "stay", "mark": "imagine", "createTime": 1753433976}, {"id": 619, "status": "1", "name": "size", "botName": "take", "username": "sit", "token": "benefit", "mark": "if", "createTime": 1753433976}, {"id": 408, "status": "1", "name": "suffer", "botName": "consider", "username": "president", "token": "sometimes", "mark": "leg", "createTime": 1753433976}]};
}

module.exports = {
  generateDataListTelegramRobotResponse
};
