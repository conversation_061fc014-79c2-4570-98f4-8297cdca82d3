
// ResultDataListDemandSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListDemandSimpleResponse模型的模拟数据
 * @returns {ResultDataListDemandSimpleResponse} 模拟数据
 */
function generateResultDataListDemandSimpleResponse() {
  return {"code": "posts_comment_rating_id_not_exists", "data": {"curPage": 2, "maxPage": 842, "total": 9266, "data": [{"id": 2, "user": {"id": 738, "name": "oil", "url": "why", "avatar": "able"}, "brand": {"id": 712, "name": "feeling", "url": "recent", "avatar": "toward"}, "services": {"id": 155, "name": "check", "url": "sort", "avatar": "baby"}, "attrList": [{"attrId": 157, "attrName": "fine", "attrValueId": 68, "attrValueName": "power"}]}, {"id": 427, "user": {"id": 293, "name": "pay", "url": "price", "avatar": "fire"}, "brand": {"id": 454, "name": "probably", "url": "director", "avatar": "land"}, "services": {"id": 893, "name": "amount", "url": "perform", "avatar": "police"}, "attrList": [{"attrId": 862, "attrName": "director", "attrValueId": 611, "attrValueName": "leg"}, {"attrId": 298, "attrName": "fight", "attrValueId": 282, "attrValueName": "south"}]}, {"id": 98, "user": {"id": 776, "name": "threat", "url": "majority", "avatar": "name"}, "brand": {"id": 909, "name": "charge", "url": "local", "avatar": "red"}, "services": {"id": 194, "name": "task", "url": "brother", "avatar": "affect"}, "attrList": [{"attrId": 336, "attrName": "night", "attrValueId": 454, "attrValueName": "challenge"}]}]}};
}

module.exports = {
  generateResultDataListDemandSimpleResponse
};
