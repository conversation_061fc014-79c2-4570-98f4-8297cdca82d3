
// ItemStockBatchResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ItemStockBatchResponse模型的模拟数据
 * @returns {ItemStockBatchResponse} 模拟数据
 */
function generateItemStockBatchResponse() {
  return {"id": 449, "store": {"id": 51, "name": "air", "url": "be", "avatar": "they"}, "item": {"id": 334, "name": "bag", "url": "perhaps", "avatar": "since"}, "batchCount": 381, "salesCount": 86, "replacementCount": 228, "refundCount": 134, "restCount": 940, "createTime": 1753433976, "status": "1"};
}

module.exports = {
  generateItemStockBatchResponse
};
