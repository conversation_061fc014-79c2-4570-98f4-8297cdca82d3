
// ResponseItemStatResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseItemStatResponse模型的模拟数据
 * @returns {ResponseItemStatResponse} 模拟数据
 */
function generateResponseItemStatResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 810, "item": {"id": 125, "name": "professor", "url": "kid", "avatar": "herself"}, "store": {"id": 950, "name": "finally", "url": "worker", "avatar": "own"}, "visitCount": 145, "ordersCount": 816, "salesAmount": 66, "salesProfit": 696, "updateTime": 1753433976, "itemId": 683, "storeId": 824}, "result": {"code": "demand_pay_rest_amount_failed", "data": {"id": 85, "item": {"id": 14, "name": "so", "url": "model", "avatar": "food"}, "store": {"id": 150, "name": "make", "url": "its", "avatar": "see"}, "visitCount": 383, "ordersCount": 130, "salesAmount": 119, "salesProfit": 66, "updateTime": 1753433976, "itemId": 530, "storeId": 673}}, "errMessageOnly": "race", "successMessage": "prevent", "errMessage": "simple"};
}

module.exports = {
  generateResponseItemStatResponse
};
