
// ResultBadgeGetListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultBadgeGetListResponse模型的模拟数据
 * @returns {ResultBadgeGetListResponse} 模拟数据
 */
function generateResultBadgeGetListResponse() {
  return {"code": "aftersales_delete_error", "data": {"id": 790, "badgeType": "1", "badgeId": 475, "relationId": 209, "extraData": {}, "expireTime": 1753433976}};
}

module.exports = {
  generateResultBadgeGetListResponse
};
