
// UserKycResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成UserKycResponse模型的模拟数据
 * @returns {UserKycResponse} 模拟数据
 */
function generateUserKycResponse() {
  return {"id": 755, "status": "1", "user": {"id": 982, "name": "exactly", "url": "according", "avatar": "pattern"}, "version": 14, "kycType": "1", "firstName": "pay", "lastName": "future", "birthDate": "1985-07-22T07:21:56.959798", "idPic": "prepare", "handsPic": "night"};
}

module.exports = {
  generateUserKycResponse
};
