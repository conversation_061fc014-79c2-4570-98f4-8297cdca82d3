
// StoreStatLogResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成StoreStatLogResponse模型的模拟数据
 * @returns {StoreStatLogResponse} 模拟数据
 */
function generateStoreStatLogResponse() {
  return {"id": 264, "date": "2007-08-12T23:34:09.514117", "store": {"id": 912, "name": "American", "url": "crime", "avatar": "else"}, "visitCount": 716, "ordersCount": 720, "salesAmount": 167, "updateTime": 1753433976, "salesCount": 150, "refundAmount": 586, "withdrawAmount": 642};
}

module.exports = {
  generateStoreStatLogResponse
};
