
// CampaignTagListSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成CampaignTagListSimpleResponse模型的模拟数据
 * @returns {CampaignTagListSimpleResponse} 模拟数据
 */
function generateCampaignTagListSimpleResponse() {
  return {"id": 578, "campaign": {"id": 392, "name": "ball", "url": "north", "avatar": "economic"}, "tag": {"id": 702, "name": "here", "url": "particular", "avatar": "trade"}, "createTime": 1753433976};
}

module.exports = {
  generateCampaignTagListSimpleResponse
};
