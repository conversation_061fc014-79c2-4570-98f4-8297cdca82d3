
// ResponseOrdersLogsResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseOrdersLogsResponse模型的模拟数据
 * @returns {ResponseOrdersLogsResponse} 模拟数据
 */
function generateResponseOrdersLogsResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 914, "ordersId": 711, "item": {"id": 114, "name": "finish", "url": "compare", "avatar": "new"}, "posts": {"id": 550, "name": "thought", "url": "exactly", "avatar": "arm"}, "logType": "1", "manage": {"id": 715, "name": "suffer", "url": "middle", "avatar": "almost"}, "store": {"id": 928, "name": "fear", "url": "character", "avatar": "us"}, "sourceType": "1", "quantity": 649, "refundAmount": 732}, "result": {"code": "black_list_id_not_exists", "data": {"id": 207, "ordersId": 727, "item": {"id": 684, "name": "mind", "url": "measure", "avatar": "full"}, "posts": {"id": 703, "name": "bill", "url": "account", "avatar": "black"}, "logType": "1", "manage": {"id": 239, "name": "sort", "url": "force", "avatar": "them"}, "store": {"id": 836, "name": "day", "url": "stock", "avatar": "keep"}, "sourceType": "1", "quantity": 945, "refundAmount": 557}}, "errMessageOnly": "garden", "successMessage": "paper", "errMessage": "somebody"};
}

module.exports = {
  generateResponseOrdersLogsResponse
};
