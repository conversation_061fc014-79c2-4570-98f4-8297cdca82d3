
// UserWalletRequest 模型
// 由SwaggerCrawler自动生成

/**
 * 生成UserWalletRequest模型的模拟数据
 * @returns {UserWalletRequest} 模拟数据
 */
function generateUserWalletRequest() {
  return {"status": "ENABLED", "isDefault": "1", "walletName": "wish", "userId": 900, "walletType": "VENMO", "country": "dog", "firstName": "research", "middleName": "address", "lastName": "process", "receiveAccount": "town"};
}

module.exports = {
  generateUserWalletRequest
};
