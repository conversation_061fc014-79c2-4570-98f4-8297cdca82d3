// DynamicswaggerResources_3Response 模型
// 由SwaggerCrawler动态生成

/**
 * 生成DynamicswaggerResources_3Response模型的模拟数据
 * @returns {DynamicswaggerResources_3Response} 模拟数据
 */
function generateDynamicswaggerResources_3Response() {
  return [{"name": "experience","url": "method","swaggerVersion": "sit","location": "give"},{"name": "throughout","url": "man","swaggerVersion": "support","location": "note"}];
}

module.exports = {
  generateDynamicswaggerResources_3Response
};
