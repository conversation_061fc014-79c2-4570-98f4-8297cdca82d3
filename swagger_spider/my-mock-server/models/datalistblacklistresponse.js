
// DataListBlackListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListBlackListResponse模型的模拟数据
 * @returns {DataListBlackListResponse} 模拟数据
 */
function generateDataListBlackListResponse() {
  return {"curPage": 71, "maxPage": 481, "total": 4064, "data": [{"id": 998, "user": {"id": 424, "name": "note", "url": "along", "avatar": "professor"}, "limitType": "1", "reason": "it", "startTime": 1753433976, "endTime": 1753433976, "createTime": 1753433976}, {"id": 108, "user": {"id": 532, "name": "usually", "url": "money", "avatar": "either"}, "limitType": "1", "reason": "address", "startTime": 1753433976, "endTime": 1753433976, "createTime": 1753433976}]};
}

module.exports = {
  generateDataListBlackListResponse
};
