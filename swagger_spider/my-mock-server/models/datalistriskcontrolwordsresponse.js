
// DataListRiskControlWordsResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListRiskControlWordsResponse模型的模拟数据
 * @returns {DataListRiskControlWordsResponse} 模拟数据
 */
function generateDataListRiskControlWordsResponse() {
  return {"curPage": 758, "maxPage": 74, "total": 919, "data": [{"id": 453, "status": "1", "level": "1", "weight": 777, "action": "1", "language": "en_US", "words": "entire", "createTime": 1753433976}, {"id": 486, "status": "1", "level": "1", "weight": 297, "action": "1", "language": "en_US", "words": "why", "createTime": 1753433976}]};
}

module.exports = {
  generateDataListRiskControlWordsResponse
};
