
// DataListPaymentSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListPaymentSimpleResponse模型的模拟数据
 * @returns {DataListPaymentSimpleResponse} 模拟数据
 */
function generateDataListPaymentSimpleResponse() {
  return {"curPage": 409, "maxPage": 654, "total": 6317, "data": [{"id": 85, "status": "1", "paymentType": "live", "paymentName": "Republican", "paymentLogo": "call", "dayAmount": 574, "monthAmount": 265, "commissionPercent": 634, "createTime": 1753433976}, {"id": 373, "status": "1", "paymentType": "owner", "paymentName": "phone", "paymentLogo": "rate", "dayAmount": 65, "monthAmount": 261, "commissionPercent": 18, "createTime": 1753433976}, {"id": 237, "status": "1", "paymentType": "PM", "paymentName": "word", "paymentLogo": "Republican", "dayAmount": 909, "monthAmount": 9, "commissionPercent": 868, "createTime": 1753433976}]};
}

module.exports = {
  generateDataListPaymentSimpleResponse
};
