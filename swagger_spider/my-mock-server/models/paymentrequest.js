
// PaymentRequest 模型
// 由SwaggerCrawler自动生成

/**
 * 生成PaymentRequest模型的模拟数据
 * @returns {PaymentRequest} 模拟数据
 */
function generatePaymentRequest() {
  return {"status": "FAILED", "sortIndex": "0", "paymentKey": "upon", "paymentType": "PAYPAL", "paymentName": "direction", "paymentLogo": "upon", "dayAmount": 923, "dayLimitCount": 265, "dayLimitAmount": 356, "monthAmount": 49};
}

module.exports = {
  generatePaymentRequest
};
