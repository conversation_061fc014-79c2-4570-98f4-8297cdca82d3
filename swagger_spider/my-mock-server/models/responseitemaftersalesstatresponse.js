
// ResponseItemAftersalesStatResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseItemAftersalesStatResponse模型的模拟数据
 * @returns {ResponseItemAftersalesStatResponse} 模拟数据
 */
function generateResponseItemAftersalesStatResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 784, "aftersalesId": 716, "store": {"id": 839, "name": "admit", "url": "minute", "avatar": "create"}, "item": {"id": 23, "name": "his", "url": "color", "avatar": "suggest"}, "hitCount": 118}, "result": {"code": "search_batch_update_status_error", "data": {"id": 283, "aftersalesId": 350, "store": {"id": 577, "name": "make", "url": "place", "avatar": "size"}, "item": {"id": 172, "name": "a", "url": "adult", "avatar": "investment"}, "hitCount": 355}}, "errMessageOnly": "raise", "successMessage": "live", "errMessage": "allow"};
}

module.exports = {
  generateResponseItemAftersalesStatResponse
};
