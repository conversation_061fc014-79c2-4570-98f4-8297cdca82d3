
// ResultItemAttrListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultItemAttrListResponse模型的模拟数据
 * @returns {ResultItemAttrListResponse} 模拟数据
 */
function generateResultItemAttrListResponse() {
  return {"code": "schedule_sign_logs_not_exists", "data": {"id": 904, "item": {"id": 977, "name": "color", "url": "simple", "avatar": "seat"}, "attr": {"id": 204, "name": "animal", "url": "into", "avatar": "deal"}, "attrValue": {"id": 397, "name": "music", "url": "popular", "avatar": "speak"}, "createTime": 1753433976}};
}

module.exports = {
  generateResultItemAttrListResponse
};
