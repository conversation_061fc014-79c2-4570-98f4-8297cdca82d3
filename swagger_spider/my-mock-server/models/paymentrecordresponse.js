
// PaymentRecordResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成PaymentRecordResponse模型的模拟数据
 * @returns {PaymentRecordResponse} 模拟数据
 */
function generatePaymentRecordResponse() {
  return {"id": 101, "user": {"id": 717, "name": "believe", "url": "time", "avatar": "executive"}, "payment": {"id": 595, "name": "level", "url": "child", "avatar": "throughout"}, "transactionId": "impact", "amount": 875, "currency": "commercial", "status": "1", "paymentTime": 1753433976, "refundAmount": 623, "refundTime": 1753433976};
}

module.exports = {
  generatePaymentRecordResponse
};
