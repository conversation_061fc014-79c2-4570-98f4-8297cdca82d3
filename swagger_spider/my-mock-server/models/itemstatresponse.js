
// ItemStatResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ItemStatResponse模型的模拟数据
 * @returns {ItemStatResponse} 模拟数据
 */
function generateItemStatResponse() {
  return {"id": 728, "item": {"id": 498, "name": "conference", "url": "different", "avatar": "outside"}, "store": {"id": 85, "name": "option", "url": "could", "avatar": "interesting"}, "visitCount": 26, "ordersCount": 503, "salesAmount": 240, "salesProfit": 21, "updateTime": 1753433976, "itemId": 272, "storeId": 542};
}

module.exports = {
  generateItemStatResponse
};
