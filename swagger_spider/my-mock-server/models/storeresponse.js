
// StoreResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成StoreResponse模型的模拟数据
 * @returns {StoreResponse} 模拟数据
 */
function generateStoreResponse() {
  return {"id": 414, "user": {"id": 793, "name": "newspaper", "url": "again", "avatar": "oil"}, "status": "1", "language": "en_US", "name": "reduce", "brandList": [{"id": 266, "name": "suggest", "url": "option", "avatar": "tax"}, {"id": 513, "name": "every", "url": "stand", "avatar": "card"}], "tagList": [{"id": 612, "name": "above", "url": "open", "avatar": "feeling"}], "servicesList": [{"id": 492, "name": "marriage", "url": "care", "avatar": "research"}, {"id": 752, "name": "education", "url": "major", "avatar": "national"}], "logo": "trip", "country": "baby"};
}

module.exports = {
  generateStoreResponse
};
