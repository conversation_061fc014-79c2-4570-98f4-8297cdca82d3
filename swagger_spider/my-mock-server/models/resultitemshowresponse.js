
// ResultItemShowResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultItemShowResponse模型的模拟数据
 * @returns {ResultItemShowResponse} 模拟数据
 */
function generateResultItemShowResponse() {
  return {"code": "tag_name_exists", "data": {"id": 287, "item": {"id": 426, "name": "size", "url": "a", "avatar": "woman"}, "status": "1", "sortIndex": 745, "version": 154, "language": "en_US", "title": "amount", "summary": "rate", "showData": "small", "createTime": 1753433976}};
}

module.exports = {
  generateResultItemShowResponse
};
