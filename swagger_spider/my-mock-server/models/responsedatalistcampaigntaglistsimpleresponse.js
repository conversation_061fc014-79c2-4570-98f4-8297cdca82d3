
// ResponseDataListCampaignTagListSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListCampaignTagListSimpleResponse模型的模拟数据
 * @returns {ResponseDataListCampaignTagListSimpleResponse} 模拟数据
 */
function generateResponseDataListCampaignTagListSimpleResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 244, "maxPage": 964, "total": 2405, "data": [{"id": 430, "campaign": {"id": 692, "name": "imagine", "url": "base", "avatar": "you"}, "tag": {"id": 157, "name": "wait", "url": "case", "avatar": "about"}, "createTime": 1753433976}, {"id": 848, "campaign": {"id": 538, "name": "forget", "url": "inside", "avatar": "forget"}, "tag": {"id": 462, "name": "card", "url": "find", "avatar": "billion"}, "createTime": 1753433976}]}, "result": {"code": "campaign_tag_list_update_error", "data": {"curPage": 284, "maxPage": 227, "total": 3641, "data": [{"id": 158, "campaign": {"id": 652, "name": "speech", "url": "modern", "avatar": "individual"}, "tag": {"id": 521, "name": "image", "url": "they", "avatar": "nice"}, "createTime": 1753433976}, {"id": 376, "campaign": {"id": 379, "name": "street", "url": "win", "avatar": "with"}, "tag": {"id": 839, "name": "behind", "url": "down", "avatar": "name"}, "createTime": 1753433976}]}}, "errMessageOnly": "movie", "successMessage": "rock", "errMessage": "claim"};
}

module.exports = {
  generateResponseDataListCampaignTagListSimpleResponse
};
