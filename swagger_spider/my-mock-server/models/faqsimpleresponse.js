
// FaqSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成FaqSimpleResponse模型的模拟数据
 * @returns {FaqSimpleResponse} 模拟数据
 */
function generateFaqSimpleResponse() {
  return {"id": 530, "useTypeName": "image", "manage": {"id": 397, "name": "first", "url": "baby", "avatar": "property"}, "brand": {"id": 710, "name": "significant", "url": "daughter", "avatar": "official"}, "statusName": "indicate", "language": "en_US", "title": "detail", "coverPic": "should", "createTime": 1753433976};
}

module.exports = {
  generateFaqSimpleResponse
};
