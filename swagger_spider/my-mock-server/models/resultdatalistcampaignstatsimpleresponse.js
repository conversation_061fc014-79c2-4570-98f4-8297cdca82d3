
// ResultDataListCampaignStatSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListCampaignStatSimpleResponse模型的模拟数据
 * @returns {ResultDataListCampaignStatSimpleResponse} 模拟数据
 */
function generateResultDataListCampaignStatSimpleResponse() {
  return {"code": "orders_ticket_message_id_not_exists", "data": {"curPage": 419, "maxPage": 444, "total": 5939, "data": [{"id": 706, "campaign": {"id": 848, "name": "mind", "url": "subject", "avatar": "mother"}, "visitCount": 407, "ordersCount": 994, "salesAmount": 455}, {"id": 695, "campaign": {"id": 424, "name": "need", "url": "risk", "avatar": "use"}, "visitCount": 780, "ordersCount": 578, "salesAmount": 915}, {"id": 716, "campaign": {"id": 234, "name": "miss", "url": "sea", "avatar": "scene"}, "visitCount": 976, "ordersCount": 72, "salesAmount": 489}]}};
}

module.exports = {
  generateResultDataListCampaignStatSimpleResponse
};
