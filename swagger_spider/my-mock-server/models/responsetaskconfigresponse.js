
// ResponseTaskConfigResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseTaskConfigResponse模型的模拟数据
 * @returns {ResponseTaskConfigResponse} 模拟数据
 */
function generateResponseTaskConfigResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 739, "status": "1", "taskName": "happy", "mark": "production", "taskClass": "run", "createTime": 1753433976, "updateTime": 1753433976}, "result": {"code": "demand_can_not_close", "data": {"id": 46, "status": "1", "taskName": "member", "mark": "surface", "taskClass": "poor", "createTime": 1753433976, "updateTime": 1753433976}}, "errMessageOnly": "line", "successMessage": "help", "errMessage": "likely"};
}

module.exports = {
  generateResponseTaskConfigResponse
};
