
// SourceCodeInfo 模型
// 由SwaggerCrawler自动生成

/**
 * 生成SourceCodeInfo模型的模拟数据
 * @returns {SourceCodeInfo} 模拟数据
 */
function generateSourceCodeInfo() {
  return {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 278, "serializedSizeAsMessageSet": 715, "empty": false}, "initialized": true, "defaultInstanceForType": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 451, "serializedSizeAsMessageSet": 436, "empty": false}, "initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_SourceCodeInfo"}, "parserForType": {}, "serializedSize": 48, "locationList": [{"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 414, "serializedSizeAsMessageSet": 579}, "initialized": false, "trailingCommentsBytes": {"validUtf8": false, "empty": true}, "leadingDetachedCommentsCount": 469, "leadingComments": "for"}, {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 596, "serializedSizeAsMessageSet": 733}, "initialized": true, "trailingCommentsBytes": {"validUtf8": false, "empty": true}, "leadingDetachedCommentsCount": 380, "leadingComments": "main"}, {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 458, "serializedSizeAsMessageSet": 659}, "initialized": true, "trailingCommentsBytes": {"validUtf8": true, "empty": false}, "leadingDetachedCommentsCount": 201, "leadingComments": "region"}], "locationCount": 928, "locationOrBuilderList": [{"trailingCommentsBytes": {"validUtf8": true, "empty": true}, "leadingDetachedCommentsCount": 222, "leadingComments": "protect", "spanCount": 308, "trailingComments": "nation"}, {"trailingCommentsBytes": {"validUtf8": true, "empty": true}, "leadingDetachedCommentsCount": 589, "leadingComments": "until", "spanCount": 376, "trailingComments": "involve"}, {"trailingCommentsBytes": {"validUtf8": false, "empty": true}, "leadingDetachedCommentsCount": 759, "leadingComments": "page", "spanCount": 491, "trailingComments": "take"}], "allFields": {}, "allFieldsRaw": {}}, "parserForType": {}, "serializedSize": 964, "locationList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 492, "serializedSizeAsMessageSet": 360}, "initialized": true, "trailingCommentsBytes": {"validUtf8": false, "empty": true}, "leadingDetachedCommentsCount": 370, "leadingComments": "structure", "spanCount": 295, "trailingComments": "help", "spanList": [851, 965, 468], "leadingDetachedCommentsList": ["to", "hear", "role"], "leadingCommentsBytes": {"validUtf8": false, "empty": false}}], "locationCount": 600, "locationOrBuilderList": [{"trailingCommentsBytes": {"validUtf8": false, "empty": false}, "leadingDetachedCommentsCount": 89, "leadingComments": "alone", "spanCount": 327, "trailingComments": "term", "spanList": [334, 719], "leadingDetachedCommentsList": ["number"], "leadingCommentsBytes": {"validUtf8": false, "empty": false}, "pathCount": 750, "pathList": [265]}, {"trailingCommentsBytes": {"validUtf8": false, "empty": true}, "leadingDetachedCommentsCount": 849, "leadingComments": "entire", "spanCount": 166, "trailingComments": "newspaper", "spanList": [416, 358], "leadingDetachedCommentsList": ["election"], "leadingCommentsBytes": {"validUtf8": false, "empty": false}, "pathCount": 136, "pathList": [378]}, {"trailingCommentsBytes": {"validUtf8": false, "empty": true}, "leadingDetachedCommentsCount": 864, "leadingComments": "per", "spanCount": 685, "trailingComments": "decision", "spanList": [542, 367, 302], "leadingDetachedCommentsList": ["article", "response"], "leadingCommentsBytes": {"validUtf8": true, "empty": false}, "pathCount": 166, "pathList": [526]}], "allFields": {}, "allFieldsRaw": {}};
}

module.exports = {
  generateSourceCodeInfo
};
