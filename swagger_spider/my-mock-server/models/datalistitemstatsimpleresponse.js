
// DataListItemStatSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListItemStatSimpleResponse模型的模拟数据
 * @returns {DataListItemStatSimpleResponse} 模拟数据
 */
function generateDataListItemStatSimpleResponse() {
  return {"curPage": 381, "maxPage": 20, "total": 165, "data": [{"id": 707, "item": {"id": 823, "name": "argue", "url": "personal", "avatar": "give"}, "store": {"id": 607, "name": "on", "url": "customer", "avatar": "rate"}, "visitCount": 23, "ordersCount": 442, "salesAmount": 61, "salesProfit": 167, "updateTime": 1753433976}, {"id": 996, "item": {"id": 618, "name": "audience", "url": "dark", "avatar": "analysis"}, "store": {"id": 419, "name": "right", "url": "against", "avatar": "less"}, "visitCount": 29, "ordersCount": 697, "salesAmount": 895, "salesProfit": 535, "updateTime": 1753433976}, {"id": 431, "item": {"id": 635, "name": "meeting", "url": "late", "avatar": "either"}, "store": {"id": 7, "name": "provide", "url": "state", "avatar": "manage"}, "visitCount": 728, "ordersCount": 549, "salesAmount": 123, "salesProfit": 34, "updateTime": 1753433976}]};
}

module.exports = {
  generateDataListItemStatSimpleResponse
};
