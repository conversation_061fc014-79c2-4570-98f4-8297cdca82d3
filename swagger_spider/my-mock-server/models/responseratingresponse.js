
// ResponseRatingResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseRatingResponse模型的模拟数据
 * @returns {ResponseRatingResponse} 模拟数据
 */
function generateResponseRatingResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 680, "status": "1", "statusName": "on", "ratingType": "1", "ratingTypeName": "message", "createTime": 1753433976, "language": "en_US", "langStatus": "1", "langStatusName": "return", "ratingName": "care"}, "result": {"code": "risk_control_finance_update_failure", "data": {"id": 402, "status": "1", "statusName": "he", "ratingType": "1", "ratingTypeName": "finally", "createTime": 1753433976, "language": "en_US", "langStatus": "1", "langStatusName": "our", "ratingName": "detail"}}, "errMessageOnly": "support", "successMessage": "even", "errMessage": "this"};
}

module.exports = {
  generateResponseRatingResponse
};
