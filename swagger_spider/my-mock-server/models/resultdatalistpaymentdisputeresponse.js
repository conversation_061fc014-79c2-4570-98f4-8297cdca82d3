
// ResultDataListPaymentDisputeResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListPaymentDisputeResponse模型的模拟数据
 * @returns {ResultDataListPaymentDisputeResponse} 模拟数据
 */
function generateResultDataListPaymentDisputeResponse() {
  return {"code": "services_status_invalid", "data": {"curPage": 291, "maxPage": 358, "total": 2685, "data": [{"id": 23, "user": {"id": 116, "name": "record", "url": "enter", "avatar": "party"}, "paymentRecordId": 165, "payment": {"id": 686, "name": "brother", "url": "understand", "avatar": "close"}, "disputeType": "1"}, {"id": 381, "user": {"id": 314, "name": "reduce", "url": "bed", "avatar": "detail"}, "paymentRecordId": 829, "payment": {"id": 151, "name": "behind", "url": "security", "avatar": "whole"}, "disputeType": "1"}]}};
}

module.exports = {
  generateResultDataListPaymentDisputeResponse
};
