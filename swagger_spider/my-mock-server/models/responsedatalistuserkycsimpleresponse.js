
// ResponseDataListUserKycSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListUserKycSimpleResponse模型的模拟数据
 * @returns {ResponseDataListUserKycSimpleResponse} 模拟数据
 */
function generateResponseDataListUserKycSimpleResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 290, "maxPage": 177, "total": 4303, "data": [{"id": 332, "status": "1", "user": {"id": 392, "name": "seek", "url": "capital", "avatar": "lawyer"}, "version": 771, "kycType": "1"}, {"id": 685, "status": "1", "user": {"id": 629, "name": "nothing", "url": "actually", "avatar": "second"}, "version": 38, "kycType": "1"}]}, "result": {"code": "schedule_status_not_valid", "data": {"curPage": 467, "maxPage": 59, "total": 7975, "data": [{"id": 348, "status": "1", "user": {"id": 941, "name": "west", "url": "teach", "avatar": "trip"}, "version": 287, "kycType": "1"}]}}, "errMessageOnly": "position", "successMessage": "film", "errMessage": "ask"};
}

module.exports = {
  generateResponseDataListUserKycSimpleResponse
};
