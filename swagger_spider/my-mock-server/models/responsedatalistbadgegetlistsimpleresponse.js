
// ResponseDataListBadgeGetListSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListBadgeGetListSimpleResponse模型的模拟数据
 * @returns {ResponseDataListBadgeGetListSimpleResponse} 模拟数据
 */
function generateResponseDataListBadgeGetListSimpleResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 353, "maxPage": 976, "total": 582, "data": [{"id": 296, "badgeType": "1", "badge": {"id": 499, "name": "science", "url": "recently", "avatar": "important"}, "relation": {"id": 791, "name": "teach", "url": "above", "avatar": "simple"}, "expireTime": 1753433976}]}, "result": {"code": "brand_services_list_delete_error", "data": {"curPage": 804, "maxPage": 35, "total": 8494, "data": [{"id": 764, "badgeType": "1", "badge": {"id": 286, "name": "radio", "url": "standard", "avatar": "agree"}, "relation": {"id": 820, "name": "sense", "url": "go", "avatar": "coach"}, "expireTime": 1753433976}, {"id": 17, "badgeType": "1", "badge": {"id": 624, "name": "design", "url": "age", "avatar": "long"}, "relation": {"id": 381, "name": "line", "url": "whose", "avatar": "human"}, "expireTime": 1753433976}]}}, "errMessageOnly": "learn", "successMessage": "it", "errMessage": "wide"};
}

module.exports = {
  generateResponseDataListBadgeGetListSimpleResponse
};
