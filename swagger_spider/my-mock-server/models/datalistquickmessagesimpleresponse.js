
// DataListQuickMessageSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListQuickMessageSimpleResponse模型的模拟数据
 * @returns {DataListQuickMessageSimpleResponse} 模拟数据
 */
function generateDataListQuickMessageSimpleResponse() {
  return {"curPage": 688, "maxPage": 941, "total": 6089, "data": [{"id": 276, "quickMessageTypeId": 362, "quickMessageTypeName": "assume", "manageId": 820, "manageName": "cause", "quickMessageLangId": 17, "language": "en_US", "message": "meeting"}, {"id": 249, "quickMessageTypeId": 544, "quickMessageTypeName": "subject", "manageId": 61, "manageName": "become", "quickMessageLangId": 846, "language": "en_US", "message": "tend"}, {"id": 119, "quickMessageTypeId": 185, "quickMessageTypeName": "use", "manageId": 963, "manageName": "house", "quickMessageLangId": 766, "language": "en_US", "message": "church"}]};
}

module.exports = {
  generateDataListQuickMessageSimpleResponse
};
