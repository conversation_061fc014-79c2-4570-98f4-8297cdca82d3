
// ResponseDataListResourcesSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListResourcesSimpleResponse模型的模拟数据
 * @returns {ResponseDataListResourcesSimpleResponse} 模拟数据
 */
function generateResponseDataListResourcesSimpleResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 353, "maxPage": 414, "total": 4928, "data": [{"id": 276, "parentMenuName": "approach", "level": "1", "status": "1", "sortIndex": 848}]}, "result": {"code": "user_api_key_update_failed", "data": {"curPage": 30, "maxPage": 632, "total": 4455, "data": [{"id": 614, "parentMenuName": "year", "level": "1", "status": "1", "sortIndex": 930}, {"id": 657, "parentMenuName": "Democrat", "level": "1", "status": "1", "sortIndex": 812}, {"id": 540, "parentMenuName": "none", "level": "1", "status": "1", "sortIndex": 891}]}}, "errMessageOnly": "walk", "successMessage": "speak", "errMessage": "season"};
}

module.exports = {
  generateResponseDataListResourcesSimpleResponse
};
