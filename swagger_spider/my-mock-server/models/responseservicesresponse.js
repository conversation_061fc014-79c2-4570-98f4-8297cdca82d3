
// ResponseServicesResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseServicesResponse模型的模拟数据
 * @returns {ResponseServicesResponse} 模拟数据
 */
function generateResponseServicesResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 726, "parentId": 41, "status": "1", "sortIndex": "0", "customUrl": "probably", "servicesLogo": "mouth", "servicesKey": "agree", "createTime": 1753433976, "updateTime": 1753433976, "serviceLangId": 628}, "result": {"code": "search_search_type_invalid", "data": {"id": 84, "parentId": 681, "status": "1", "sortIndex": "0", "customUrl": "let", "servicesLogo": "clear", "servicesKey": "thought", "createTime": 1753433976, "updateTime": 1753433976, "serviceLangId": 765}}, "errMessageOnly": "theory", "successMessage": "character", "errMessage": "seven"};
}

module.exports = {
  generateResponseServicesResponse
};
