
// DataListServicesSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListServicesSimpleResponse模型的模拟数据
 * @returns {DataListServicesSimpleResponse} 模拟数据
 */
function generateDataListServicesSimpleResponse() {
  return {"curPage": 143, "maxPage": 315, "total": 6234, "data": [{"id": 790, "status": "1", "language": "en_US", "servicesName": "news", "customUrl": "become", "servicesKey": "growth", "servicesLogo": "begin", "serviceAttributes": [{"id": 706, "name": "night", "url": "picture", "avatar": "individual"}], "createTime": 1753433976}]};
}

module.exports = {
  generateDataListServicesSimpleResponse
};
