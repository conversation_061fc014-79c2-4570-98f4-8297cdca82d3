
// ResponseBlackListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseBlackListResponse模型的模拟数据
 * @returns {ResponseBlackListResponse} 模拟数据
 */
function generateResponseBlackListResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 599, "user": {"id": 404, "name": "moment", "url": "large", "avatar": "cut"}, "limitType": "1", "reason": "too", "startTime": 1753433976, "endTime": 1753433976, "createTime": 1753433976}, "result": {"code": "user_email_code_incorrect", "data": {"id": 248, "user": {"id": 248, "name": "beyond", "url": "specific", "avatar": "foreign"}, "limitType": "1", "reason": "especially", "startTime": 1753433976, "endTime": 1753433976, "createTime": 1753433976}}, "errMessageOnly": "guy", "successMessage": "president", "errMessage": "recently"};
}

module.exports = {
  generateResponseBlackListResponse
};
