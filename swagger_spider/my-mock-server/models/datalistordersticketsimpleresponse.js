
// DataListOrdersTicketSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListOrdersTicketSimpleResponse模型的模拟数据
 * @returns {DataListOrdersTicketSimpleResponse} 模拟数据
 */
function generateDataListOrdersTicketSimpleResponse() {
  return {"curPage": 914, "maxPage": 697, "total": 6140, "data": [{"id": 287, "status": "1", "ordersId": 273, "user": {"id": 218, "name": "little", "url": "up", "avatar": "dog"}, "store": {"id": 149, "name": "home", "url": "standard", "avatar": "model"}, "manage": {"id": 427, "name": "finally", "url": "structure", "avatar": "security"}, "aftersalesId": 945, "aftersalesTxt": "too", "createTime": 1753433976}, {"id": 988, "status": "1", "ordersId": 539, "user": {"id": 464, "name": "table", "url": "memory", "avatar": "thing"}, "store": {"id": 768, "name": "respond", "url": "list", "avatar": "often"}, "manage": {"id": 312, "name": "trade", "url": "fine", "avatar": "toward"}, "aftersalesId": 650, "aftersalesTxt": "relate", "createTime": 1753433976}, {"id": 95, "status": "1", "ordersId": 432, "user": {"id": 981, "name": "key", "url": "believe", "avatar": "entire"}, "store": {"id": 439, "name": "son", "url": "law", "avatar": "nor"}, "manage": {"id": 791, "name": "nor", "url": "executive", "avatar": "later"}, "aftersalesId": 508, "aftersalesTxt": "state", "createTime": 1753433976}]};
}

module.exports = {
  generateDataListOrdersTicketSimpleResponse
};
