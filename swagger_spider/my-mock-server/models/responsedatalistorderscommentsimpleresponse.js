
// ResponseDataListOrdersCommentSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListOrdersCommentSimpleResponse模型的模拟数据
 * @returns {ResponseDataListOrdersCommentSimpleResponse} 模拟数据
 */
function generateResponseDataListOrdersCommentSimpleResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 705, "maxPage": 474, "total": 6777, "data": [{"id": 737, "user": {"id": 772, "name": "set", "url": "fish", "avatar": "cover"}, "item": {"id": 868, "name": "result", "url": "rich", "avatar": "nation"}, "ordersId": 92, "store": {"id": 627, "name": "happy", "url": "consider", "avatar": "stay"}}, {"id": 804, "user": {"id": 494, "name": "meeting", "url": "seven", "avatar": "agency"}, "item": {"id": 416, "name": "market", "url": "minute", "avatar": "institution"}, "ordersId": 641, "store": {"id": 228, "name": "speak", "url": "rise", "avatar": "other"}}]}, "result": {"code": "refund_failure", "data": {"curPage": 608, "maxPage": 916, "total": 2424, "data": [{"id": 330, "user": {"id": 321, "name": "economic", "url": "energy", "avatar": "gas"}, "item": {"id": 346, "name": "chance", "url": "natural", "avatar": "pressure"}, "ordersId": 378, "store": {"id": 341, "name": "two", "url": "culture", "avatar": "there"}}, {"id": 254, "user": {"id": 521, "name": "kind", "url": "speak", "avatar": "force"}, "item": {"id": 68, "name": "entire", "url": "system", "avatar": "say"}, "ordersId": 645, "store": {"id": 487, "name": "cause", "url": "still", "avatar": "couple"}}, {"id": 981, "user": {"id": 361, "name": "yeah", "url": "plan", "avatar": "none"}, "item": {"id": 603, "name": "reason", "url": "history", "avatar": "education"}, "ordersId": 88, "store": {"id": 828, "name": "responsibility", "url": "modern", "avatar": "last"}}]}}, "errMessageOnly": "south", "successMessage": "argue", "errMessage": "improve"};
}

module.exports = {
  generateResponseDataListOrdersCommentSimpleResponse
};
