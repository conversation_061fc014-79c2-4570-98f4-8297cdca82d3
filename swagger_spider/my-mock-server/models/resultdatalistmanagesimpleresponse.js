
// ResultDataListManageSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListManageSimpleResponse模型的模拟数据
 * @returns {ResultDataListManageSimpleResponse} 模拟数据
 */
function generateResultDataListManageSimpleResponse() {
  return {"code": "services_lang_id_not_exists", "data": {"curPage": 128, "maxPage": 958, "total": 99, "data": [{"id": 353, "status": "1", "roleId": 197, "roleName": "those", "username": "day"}, {"id": 639, "status": "1", "roleId": 161, "roleName": "spend", "username": "against"}, {"id": 745, "status": "1", "roleId": 513, "roleName": "economy", "username": "type"}]}};
}

module.exports = {
  generateResultDataListManageSimpleResponse
};
