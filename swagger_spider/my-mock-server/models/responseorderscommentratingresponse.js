
// ResponseOrdersCommentRatingResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseOrdersCommentRatingResponse模型的模拟数据
 * @returns {ResponseOrdersCommentRatingResponse} 模拟数据
 */
function generateResponseOrdersCommentRatingResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 356, "ordersComment": {"id": 509, "name": "ball", "url": "air", "avatar": "red"}, "rating": {"id": 216, "name": "position", "url": "wall", "avatar": "together"}, "score": "1"}, "result": {"code": "brand_parent_id_invalid", "data": {"id": 18, "ordersComment": {"id": 755, "name": "activity", "url": "deal", "avatar": "film"}, "rating": {"id": 340, "name": "list", "url": "away", "avatar": "you"}, "score": "1"}}, "errMessageOnly": "relate", "successMessage": "become", "errMessage": "party"};
}

module.exports = {
  generateResponseOrdersCommentRatingResponse
};
