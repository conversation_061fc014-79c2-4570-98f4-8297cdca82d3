
// ResponsePagesResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponsePagesResponse模型的模拟数据
 * @returns {ResponsePagesResponse} 模拟数据
 */
function generateResponsePagesResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 353, "manageId": 983, "pagesType": 356, "status": "1", "language": "en_US", "title": "job", "customUrl": "operation", "coverPic": "north", "createTime": 1753433976, "seoKeywords": "race"}, "result": {"code": "upload_error", "data": {"id": 229, "manageId": 913, "pagesType": 250, "status": "1", "language": "en_US", "title": "over", "customUrl": "moment", "coverPic": "painting", "createTime": 1753433976, "seoKeywords": "picture"}}, "errMessageOnly": "let", "successMessage": "face", "errMessage": "bad"};
}

module.exports = {
  generateResponsePagesResponse
};
