
// ResultItemResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultItemResponse模型的模拟数据
 * @returns {ResultItemResponse} 模拟数据
 */
function generateResultItemResponse() {
  return {"code": "demand_can_not_select_bidder", "data": {"id": 748, "user": {"id": 289, "name": "family", "url": "citizen", "avatar": "quality"}, "store": {"id": 407, "name": "color", "url": "nothing", "avatar": "write"}, "brand": {"id": 714, "name": "tree", "url": "plant", "avatar": "protect"}, "services": {"id": 65, "name": "mother", "url": "character", "avatar": "poor"}, "tagList": [{"id": 634, "name": "major", "url": "suggest", "avatar": "challenge"}, {"id": 469, "name": "trial", "url": "sort", "avatar": "management"}, {"id": 488, "name": "science", "url": "sister", "avatar": "option"}], "attrList": [{"attrId": 266, "attrName": "itself", "attrValueId": 77, "attrValueName": "each"}, {"attrId": 146, "attrName": "personal", "attrValueId": 165, "attrValueName": "point"}, {"attrId": 389, "attrName": "staff", "attrValueId": 581, "attrValueName": "effort"}], "status": "1", "language": "en_US", "name": "federal"}};
}

module.exports = {
  generateResultItemResponse
};
