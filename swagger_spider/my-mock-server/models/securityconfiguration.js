
// SecurityConfiguration 模型
// 由SwaggerCrawler自动生成

/**
 * 生成SecurityConfiguration模型的模拟数据
 * @returns {SecurityConfiguration} 模拟数据
 */
function generateSecurityConfiguration() {
  return {"apiKey": "enough", "apiKeyVehicle": "cut", "apiKeyName": "physical", "clientId": "serious", "clientSecret": "know", "realm": "pretty", "appName": "rich", "scopeSeparator": "road", "additionalQueryStringParams": {}, "useBasicAuthenticationWithAccessCodeGrant": true};
}

module.exports = {
  generateSecurityConfiguration
};
