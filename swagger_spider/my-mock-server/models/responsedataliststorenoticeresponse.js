
// ResponseDataListStoreNoticeResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListStoreNoticeResponse模型的模拟数据
 * @returns {ResponseDataListStoreNoticeResponse} 模拟数据
 */
function generateResponseDataListStoreNoticeResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 901, "maxPage": 9, "total": 1993, "data": [{"id": 391, "store": {"id": 795, "name": "along", "url": "those", "avatar": "dinner"}, "noticeType": "1", "status": "1", "staffList": "pretty"}, {"id": 17, "store": {"id": 415, "name": "deep", "url": "seek", "avatar": "education"}, "noticeType": "1", "status": "1", "staffList": "hit"}, {"id": 720, "store": {"id": 130, "name": "explain", "url": "place", "avatar": "his"}, "noticeType": "1", "status": "1", "staffList": "look"}]}, "result": {"code": "manage_id_not_exists", "data": {"curPage": 415, "maxPage": 659, "total": 3904, "data": [{"id": 92, "store": {"id": 415, "name": "around", "url": "which", "avatar": "another"}, "noticeType": "1", "status": "1", "staffList": "last"}, {"id": 443, "store": {"id": 808, "name": "room", "url": "where", "avatar": "north"}, "noticeType": "1", "status": "1", "staffList": "color"}]}}, "errMessageOnly": "rest", "successMessage": "control", "errMessage": "market"};
}

module.exports = {
  generateResponseDataListStoreNoticeResponse
};
