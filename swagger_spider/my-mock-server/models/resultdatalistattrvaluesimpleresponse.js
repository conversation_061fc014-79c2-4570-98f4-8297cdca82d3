
// ResultDataListAttrValueSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListAttrValueSimpleResponse模型的模拟数据
 * @returns {ResultDataListAttrValueSimpleResponse} 模拟数据
 */
function generateResultDataListAttrValueSimpleResponse() {
  return {"code": "search_brand_id_invalid", "data": {"curPage": 179, "maxPage": 748, "total": 8555, "data": [{"id": 26, "attr": {"id": 248, "name": "water", "url": "shoulder", "avatar": "fine"}, "status": "1", "customUrl": "give", "language": "en_US"}]}};
}

module.exports = {
  generateResultDataListAttrValueSimpleResponse
};
