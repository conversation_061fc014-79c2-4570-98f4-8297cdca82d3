
// ResponseDataListSystemConfigResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListSystemConfigResponse模型的模拟数据
 * @returns {ResponseDataListSystemConfigResponse} 模拟数据
 */
function generateResponseDataListSystemConfigResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 982, "maxPage": 144, "total": 9142, "data": [{"id": 756, "configKey": "build", "configClass": "team", "configValue": "civil", "mark": "school"}, {"id": 28, "configKey": "education", "configClass": "probably", "configValue": "enter", "mark": "difficult"}, {"id": 47, "configKey": "listen", "configClass": "every", "configValue": "smile", "mark": "way"}]}, "result": {"code": "user_change_password_2fa_code_incorrect", "data": {"curPage": 847, "maxPage": 249, "total": 3393, "data": [{"id": 832, "configKey": "central", "configClass": "manager", "configValue": "year", "mark": "now"}, {"id": 203, "configKey": "would", "configClass": "floor", "configValue": "adult", "mark": "painting"}, {"id": 527, "configKey": "affect", "configClass": "life", "configValue": "ever", "mark": "pick"}]}}, "errMessageOnly": "establish", "successMessage": "skin", "errMessage": "others"};
}

module.exports = {
  generateResponseDataListSystemConfigResponse
};
