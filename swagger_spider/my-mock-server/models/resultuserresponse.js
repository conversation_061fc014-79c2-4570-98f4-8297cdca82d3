
// ResultUserResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultUserResponse模型的模拟数据
 * @returns {ResultUserResponse} 模拟数据
 */
function generateResultUserResponse() {
  return {"code": "promotion_id_not_exists", "data": {"id": 271, "username": "pay", "email": "then", "status": 923, "verified": 233, "level": 425, "avatar": "like", "createTime": 1753433975, "password": "different", "salt": "strong"}};
}

module.exports = {
  generateResultUserResponse
};
