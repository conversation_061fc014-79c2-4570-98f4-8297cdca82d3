
// ResultDataListRiskControlWordsResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListRiskControlWordsResponse模型的模拟数据
 * @returns {ResultDataListRiskControlWordsResponse} 模拟数据
 */
function generateResultDataListRiskControlWordsResponse() {
  return {"code": "rating_names_exist", "data": {"curPage": 106, "maxPage": 94, "total": 3927, "data": [{"id": 958, "status": "1", "level": "1", "weight": 696, "action": "1"}, {"id": 393, "status": "1", "level": "1", "weight": 906, "action": "1"}]}};
}

module.exports = {
  generateResultDataListRiskControlWordsResponse
};
