
// ResponseDataListStoreStatResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListStoreStatResponse模型的模拟数据
 * @returns {ResponseDataListStoreStatResponse} 模拟数据
 */
function generateResponseDataListStoreStatResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 593, "maxPage": 738, "total": 1164, "data": [{"id": 702, "store": {"id": 530, "name": "capital", "url": "statement", "avatar": "place"}, "visitCount": 270, "ordersCount": 87, "salesAmount": 709}, {"id": 839, "store": {"id": 49, "name": "view", "url": "sense", "avatar": "best"}, "visitCount": 321, "ordersCount": 283, "salesAmount": 853}, {"id": 176, "store": {"id": 554, "name": "explain", "url": "apply", "avatar": "run"}, "visitCount": 689, "ordersCount": 622, "salesAmount": 252}]}, "result": {"code": "manage_permissions_delete_failed", "data": {"curPage": 997, "maxPage": 549, "total": 2894, "data": [{"id": 977, "store": {"id": 424, "name": "approach", "url": "what", "avatar": "life"}, "visitCount": 489, "ordersCount": 67, "salesAmount": 625}, {"id": 871, "store": {"id": 317, "name": "dream", "url": "should", "avatar": "simply"}, "visitCount": 563, "ordersCount": 488, "salesAmount": 411}]}}, "errMessageOnly": "create", "successMessage": "want", "errMessage": "must"};
}

module.exports = {
  generateResponseDataListStoreStatResponse
};
