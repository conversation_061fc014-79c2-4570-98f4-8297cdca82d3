
// ResponseItemStockResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseItemStockResponse模型的模拟数据
 * @returns {ResponseItemStockResponse} 模拟数据
 */
function generateResponseItemStockResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 661, "item": {"id": 337, "name": "cup", "url": "statement", "avatar": "out"}, "batchId": 964, "ordersId": 430, "checkStatus": "1", "soldStatus": "1", "deleteStatus": "1", "replacementStatus": "1", "createTime": 1753433976, "checkTime": 1753433976}, "result": {"code": "user_email_can_not_activate", "data": {"id": 733, "item": {"id": 925, "name": "still", "url": "I", "avatar": "there"}, "batchId": 457, "ordersId": 532, "checkStatus": "1", "soldStatus": "1", "deleteStatus": "1", "replacementStatus": "1", "createTime": 1753433976, "checkTime": 1753433976}}, "errMessageOnly": "force", "successMessage": "purpose", "errMessage": "among"};
}

module.exports = {
  generateResponseItemStockResponse
};
