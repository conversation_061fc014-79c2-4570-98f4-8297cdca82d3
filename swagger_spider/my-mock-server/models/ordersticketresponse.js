
// OrdersTicketResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成OrdersTicketResponse模型的模拟数据
 * @returns {OrdersTicketResponse} 模拟数据
 */
function generateOrdersTicketResponse() {
  return {"id": 639, "status": "1", "ordersId": 875, "user": {"id": 653, "name": "someone", "url": "today", "avatar": "beyond"}, "store": {"id": 646, "name": "shake", "url": "answer", "avatar": "drop"}, "manage": {"id": 131, "name": "concern", "url": "more", "avatar": "still"}, "aftersalesId": 246, "aftersalesTxt": "worker", "createTime": 1753433976, "manageLastTime": 1753433976};
}

module.exports = {
  generateOrdersTicketResponse
};
