
// ItemStockResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ItemStockResponse模型的模拟数据
 * @returns {ItemStockResponse} 模拟数据
 */
function generateItemStockResponse() {
  return {"id": 626, "item": {"id": 423, "name": "position", "url": "reflect", "avatar": "difficult"}, "batchId": 6, "ordersId": 8, "checkStatus": "1", "soldStatus": "1", "deleteStatus": "1", "replacementStatus": "1", "createTime": 1753433976, "checkTime": 1753433976};
}

module.exports = {
  generateItemStockResponse
};
