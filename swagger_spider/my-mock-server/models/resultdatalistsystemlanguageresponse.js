
// ResultDataListSystemLanguageResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListSystemLanguageResponse模型的模拟数据
 * @returns {ResultDataListSystemLanguageResponse} 模拟数据
 */
function generateResultDataListSystemLanguageResponse() {
  return {"code": "orders_ticket_status_invalid", "data": {"curPage": 994, "maxPage": 408, "total": 6795, "data": [{"id": 510, "language": "en_US", "langKey": "others", "languageValue": "threat", "createTime": 1753433976}, {"id": 961, "language": "en_US", "langKey": "event", "languageValue": "education", "createTime": 1753433976}, {"id": 927, "language": "en_US", "langKey": "her", "languageValue": "lose", "createTime": 1753433976}]}};
}

module.exports = {
  generateResultDataListSystemLanguageResponse
};
