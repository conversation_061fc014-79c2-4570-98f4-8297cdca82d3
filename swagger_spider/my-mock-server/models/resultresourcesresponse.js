
// ResultResourcesResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultResourcesResponse模型的模拟数据
 * @returns {ResultResourcesResponse} 模拟数据
 */
function generateResultResourcesResponse() {
  return {"code": "refund_failure", "data": {"id": 888, "parentId": 245, "level": "1", "status": "1", "sortIndex": 74, "showStatus": "1", "resourcesKey": "single", "menuName": "other", "menuPath": "college", "createTime": 1753433976}};
}

module.exports = {
  generateResultResourcesResponse
};
