
// PagesTypeResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成PagesTypeResponse模型的模拟数据
 * @returns {PagesTypeResponse} 模拟数据
 */
function generatePagesTypeResponse() {
  return {"id": 268, "status": "1", "statusName": "administration", "langStatus": "1", "langStatusName": "owner", "customUrl": "candidate", "createTime": 1753433976, "updateTime": 1753433976, "language": "en_US", "typeName": "may"};
}

module.exports = {
  generatePagesTypeResponse
};
