
// ResponseDataListCampaignSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListCampaignSimpleResponse模型的模拟数据
 * @returns {ResponseDataListCampaignSimpleResponse} 模拟数据
 */
function generateResponseDataListCampaignSimpleResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 386, "maxPage": 748, "total": 3451, "data": [{"id": 539, "status": "1", "customUrl": "strategy", "language": "en_US", "title": "toward"}]}, "result": {"code": "phone_number_error", "data": {"curPage": 603, "maxPage": 436, "total": 9967, "data": [{"id": 356, "status": "1", "customUrl": "together", "language": "en_US", "title": "herself"}]}}, "errMessageOnly": "edge", "successMessage": "American", "errMessage": "exist"};
}

module.exports = {
  generateResponseDataListCampaignSimpleResponse
};
