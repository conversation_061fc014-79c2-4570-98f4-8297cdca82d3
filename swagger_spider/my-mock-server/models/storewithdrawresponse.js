
// StoreWithdrawResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成StoreWithdrawResponse模型的模拟数据
 * @returns {StoreWithdrawResponse} 模拟数据
 */
function generateStoreWithdrawResponse() {
  return {"id": 687, "user": {"id": 615, "name": "moment", "url": "really", "avatar": "nearly"}, "store": {"id": 233, "name": "specific", "url": "that", "avatar": "group"}, "userWallet": {"id": 862, "name": "cover", "url": "establish", "avatar": "bring"}, "status": "1", "currentRate": 79, "withdrawAmount": 923, "transferAmount": 170, "feeFixed": 363, "feePercent": 513};
}

module.exports = {
  generateStoreWithdrawResponse
};
