
// PromotionAuditingCountResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成PromotionAuditingCountResponse模型的模拟数据
 * @returns {PromotionAuditingCountResponse} 模拟数据
 */
function generatePromotionAuditingCountResponse() {
  return {"todayPromotionAuditingCount": 4527, "todayProcessingPromotionAuditingCount": 1715, "todayPassPromotionAuditingCount": 9572, "todayRefusePromotionAuditingCount": 3471};
}

module.exports = {
  generatePromotionAuditingCountResponse
};
