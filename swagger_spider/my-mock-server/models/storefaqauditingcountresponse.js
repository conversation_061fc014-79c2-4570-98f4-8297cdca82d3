
// StoreFaqAuditingCountResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成StoreFaqAuditingCountResponse模型的模拟数据
 * @returns {StoreFaqAuditingCountResponse} 模拟数据
 */
function generateStoreFaqAuditingCountResponse() {
  return {"todayStoreFaqCount": 4160, "todayNewStoreFaqCount": 5403, "todayPassStoreFaqAuditingCount": 7821, "todayRejectedStoreFaqAuditingCount": 2585};
}

module.exports = {
  generateStoreFaqAuditingCountResponse
};
