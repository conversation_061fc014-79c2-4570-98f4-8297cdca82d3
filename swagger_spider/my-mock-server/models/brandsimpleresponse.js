
// BrandSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成BrandSimpleResponse模型的模拟数据
 * @returns {BrandSimpleResponse} 模拟数据
 */
function generateBrandSimpleResponse() {
  return {"id": 432, "status": "1", "language": "en_US", "name": "take", "customUrl": "look", "brandLogo": "section", "createTime": 1753433976, "servicesList": [{"id": 324, "name": "happen", "url": "bill", "avatar": "effort"}]};
}

module.exports = {
  generateBrandSimpleResponse
};
