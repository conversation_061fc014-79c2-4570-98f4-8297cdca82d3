
// ItemTagListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ItemTagListResponse模型的模拟数据
 * @returns {ItemTagListResponse} 模拟数据
 */
function generateItemTagListResponse() {
  return {"id": 584, "item": {"id": 825, "name": "low", "url": "large", "avatar": "which"}, "tag": {"id": 159, "name": "follow", "url": "city", "avatar": "million"}, "createTime": 1753433976};
}

module.exports = {
  generateItemTagListResponse
};
