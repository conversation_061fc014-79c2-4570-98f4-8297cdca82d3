
// PostsCommentRatingResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成PostsCommentRatingResponse模型的模拟数据
 * @returns {PostsCommentRatingResponse} 模拟数据
 */
function generatePostsCommentRatingResponse() {
  return {"id": 796, "posts": {"id": 967, "name": "which", "url": "month", "avatar": "according"}, "user": {"id": 701, "name": "low", "url": "push", "avatar": "we"}, "rating": {"id": 591, "name": "under", "url": "financial", "avatar": "change"}, "score": "1", "createTime": 1753433976, "updateTime": 1753433976};
}

module.exports = {
  generatePostsCommentRatingResponse
};
