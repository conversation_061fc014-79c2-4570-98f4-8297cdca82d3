
// CampaignResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成CampaignResponse模型的模拟数据
 * @returns {CampaignResponse} 模拟数据
 */
function generateCampaignResponse() {
  return {"id": 901, "status": "1", "customUrl": "deep", "coverPic": "step", "createTime": 1753433976, "campaignLangId": 305, "language": "en_US", "title": "cost", "summary": "student", "seoKeywords": "market"};
}

module.exports = {
  generateCampaignResponse
};
