
// ResponseDemandAttrListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDemandAttrListResponse模型的模拟数据
 * @returns {ResponseDemandAttrListResponse} 模拟数据
 */
function generateResponseDemandAttrListResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 689, "demand": {"id": 757, "name": "practice", "url": "above", "avatar": "any"}, "attr": {"id": 149, "name": "environmental", "url": "fast", "avatar": "hard"}, "attrValue": {"id": 394, "name": "wind", "url": "garden", "avatar": "eat"}, "createTime": 1753433976}, "result": {"code": "manage_permissions_add_failed", "data": {"id": 206, "demand": {"id": 158, "name": "fast", "url": "town", "avatar": "nothing"}, "attr": {"id": 149, "name": "return", "url": "field", "avatar": "mission"}, "attrValue": {"id": 357, "name": "pattern", "url": "family", "avatar": "condition"}, "createTime": 1753433976}}, "errMessageOnly": "culture", "successMessage": "and", "errMessage": "able"};
}

module.exports = {
  generateResponseDemandAttrListResponse
};
