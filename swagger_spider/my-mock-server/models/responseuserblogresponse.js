
// ResponseUserBlogResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseUserBlogResponse模型的模拟数据
 * @returns {ResponseUserBlogResponse} 模拟数据
 */
function generateResponseUserBlogResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 374, "user": {"id": 328, "name": "blue", "url": "factor", "avatar": "resource"}, "store": {"id": 671, "name": "condition", "url": "indicate", "avatar": "to"}, "authorName": "always", "blogStatus": "1", "customUrl": "bad", "readCount": 134, "ratingCount": 224, "moneyAmount": 712, "createTime": 1753433975}, "result": {"code": "store_faq_id_not_exists", "data": {"id": 27, "user": {"id": 888, "name": "now", "url": "instead", "avatar": "where"}, "store": {"id": 509, "name": "others", "url": "look", "avatar": "set"}, "authorName": "check", "blogStatus": "1", "customUrl": "gun", "readCount": 159, "ratingCount": 819, "moneyAmount": 733, "createTime": 1753433975}}, "errMessageOnly": "better", "successMessage": "specific", "errMessage": "yeah"};
}

module.exports = {
  generateResponseUserBlogResponse
};
