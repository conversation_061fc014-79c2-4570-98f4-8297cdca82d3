
// BadgeGetListSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成BadgeGetListSimpleResponse模型的模拟数据
 * @returns {BadgeGetListSimpleResponse} 模拟数据
 */
function generateBadgeGetListSimpleResponse() {
  return {"id": 668, "badgeType": "1", "badge": {"id": 730, "name": "value", "url": "model", "avatar": "they"}, "relation": {"id": 206, "name": "mother", "url": "much", "avatar": "computer"}, "expireTime": 1753433976};
}

module.exports = {
  generateBadgeGetListSimpleResponse
};
