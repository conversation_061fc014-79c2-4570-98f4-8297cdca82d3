
// ResultQuickMessageTypeResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultQuickMessageTypeResponse模型的模拟数据
 * @returns {ResultQuickMessageTypeResponse} 模拟数据
 */
function generateResultQuickMessageTypeResponse() {
  return {"code": "attr_parent_id_invalid", "data": {"id": 287, "status": "1", "typeName": "make", "mark": "point", "createTime": 1753433976}};
}

module.exports = {
  generateResultQuickMessageTypeResponse
};
