
// ResponseDataListScheduleAttendanceReportDTO 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListScheduleAttendanceReportDTO模型的模拟数据
 * @returns {ResponseDataListScheduleAttendanceReportDTO} 模拟数据
 */
function generateResponseDataListScheduleAttendanceReportDTO() {
  return {"code": 0, "msg": "success", "data": {"curPage": 59, "maxPage": 641, "total": 2476, "data": [{"manage": {"id": 734, "name": "point", "url": "relate", "avatar": "none"}, "month": "2017-07-01T03:53:33.573350", "schedulingTimes": 1753433976, "scheduledWorkingHours": 173, "actualWorkingHours": 913}, {"manage": {"id": 618, "name": "beyond", "url": "main", "avatar": "cell"}, "month": "2015-06-10T08:03:10.509652", "schedulingTimes": 1753433976, "scheduledWorkingHours": 428, "actualWorkingHours": 399}, {"manage": {"id": 35, "name": "very", "url": "send", "avatar": "impact"}, "month": "1972-09-03T22:46:58.506621", "schedulingTimes": 1753433976, "scheduledWorkingHours": 644, "actualWorkingHours": 930}]}, "result": {"code": "demand_update_failed", "data": {"curPage": 861, "maxPage": 850, "total": 1558, "data": [{"manage": {"id": 448, "name": "reality", "url": "Republican", "avatar": "move"}, "month": "1982-01-13T08:11:13.378955", "schedulingTimes": 1753433976, "scheduledWorkingHours": 918, "actualWorkingHours": 672}]}}, "errMessageOnly": "worry", "successMessage": "stop", "errMessage": "far"};
}

module.exports = {
  generateResponseDataListScheduleAttendanceReportDTO
};
