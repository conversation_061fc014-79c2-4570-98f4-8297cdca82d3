
// ResultDataListDemandAttrListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListDemandAttrListResponse模型的模拟数据
 * @returns {ResultDataListDemandAttrListResponse} 模拟数据
 */
function generateResultDataListDemandAttrListResponse() {
  return {"code": "bean_error", "data": {"curPage": 289, "maxPage": 629, "total": 541, "data": [{"id": 384, "demand": {"id": 534, "name": "us", "url": "pressure", "avatar": "live"}, "attr": {"id": 83, "name": "more", "url": "activity", "avatar": "relationship"}, "attrValue": {"id": 330, "name": "begin", "url": "character", "avatar": "general"}, "createTime": 1753433976}]}};
}

module.exports = {
  generateResultDataListDemandAttrListResponse
};
