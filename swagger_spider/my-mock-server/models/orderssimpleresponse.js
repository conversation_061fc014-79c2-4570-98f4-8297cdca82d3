
// OrdersSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成OrdersSimpleResponse模型的模拟数据
 * @returns {OrdersSimpleResponse} 模拟数据
 */
function generateOrdersSimpleResponse() {
  return {"id": 437, "user": {"id": 699, "name": "floor", "url": "which", "avatar": "sort"}, "item": {"id": 218, "name": "culture", "url": "off", "avatar": "prepare"}, "posts": {"id": 864, "name": "begin", "url": "can", "avatar": "represent"}, "demand": {"id": 606, "name": "present", "url": "follow", "avatar": "apply"}, "store": {"id": 60, "name": "we", "url": "board", "avatar": "notice"}, "ordersType": "1", "status": "1", "price": 247, "originalPrice": 404};
}

module.exports = {
  generateOrdersSimpleResponse
};
