
// ResponseDataListBrandFieldSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListBrandFieldSimpleResponse模型的模拟数据
 * @returns {ResponseDataListBrandFieldSimpleResponse} 模拟数据
 */
function generateResponseDataListBrandFieldSimpleResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 203, "maxPage": 183, "total": 5260, "data": [{"id": 599, "brand": {"id": 70, "name": "customer", "url": "technology", "avatar": "they"}, "fieldName": "must", "fieldType": "1", "required": "1"}]}, "result": {"code": "orders_id_not_exists", "data": {"curPage": 117, "maxPage": 50, "total": 748, "data": [{"id": 64, "brand": {"id": 793, "name": "later", "url": "through", "avatar": "social"}, "fieldName": "artist", "fieldType": "1", "required": "1"}, {"id": 105, "brand": {"id": 607, "name": "new", "url": "race", "avatar": "others"}, "fieldName": "early", "fieldType": "1", "required": "1"}]}}, "errMessageOnly": "partner", "successMessage": "Mr", "errMessage": "data"};
}

module.exports = {
  generateResponseDataListBrandFieldSimpleResponse
};
