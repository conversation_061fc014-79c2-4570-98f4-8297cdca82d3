
// ResultStoreStatResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultStoreStatResponse模型的模拟数据
 * @returns {ResultStoreStatResponse} 模拟数据
 */
function generateResultStoreStatResponse() {
  return {"code": "rating_ids_not_null", "data": {"id": 266, "store": {"id": 434, "name": "then", "url": "Democrat", "avatar": "standard"}, "visitCount": 734, "ordersCount": 495, "salesAmount": 773, "salesCount": 370, "salesProfit": 250, "refundAmount": 95, "withdrawAmount": 480, "riskChatCount": 386}};
}

module.exports = {
  generateResultStoreStatResponse
};
