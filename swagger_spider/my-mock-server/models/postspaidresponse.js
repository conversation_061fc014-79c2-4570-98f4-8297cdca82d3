
// PostsPaidResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成PostsPaidResponse模型的模拟数据
 * @returns {PostsPaidResponse} 模拟数据
 */
function generatePostsPaidResponse() {
  return {"id": 116, "posts": {"id": 847, "name": "left", "url": "store", "avatar": "my"}, "user": {"id": 931, "name": "member", "url": "finally", "avatar": "short"}, "status": "1", "paidPrice": 636, "createTime": 1753433976};
}

module.exports = {
  generatePostsPaidResponse
};
