
// ResultCouponResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultCouponResponse模型的模拟数据
 * @returns {ResultCouponResponse} 模拟数据
 */
function generateResultCouponResponse() {
  return {"code": "user_wallet_type_exists", "data": {"id": 346, "brandId": 661, "storeId": 40, "servicesId": 263, "itemId": 307, "actName": "six", "couponStatus": "1", "couponCode": "institution", "discount": 930, "moneyMin": 98}};
}

module.exports = {
  generateResultCouponResponse
};
