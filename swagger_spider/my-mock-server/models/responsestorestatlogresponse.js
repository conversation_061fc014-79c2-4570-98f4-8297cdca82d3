
// ResponseStoreStatLogResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseStoreStatLogResponse模型的模拟数据
 * @returns {ResponseStoreStatLogResponse} 模拟数据
 */
function generateResponseStoreStatLogResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 110, "date": "2009-03-19T02:09:55.560984", "store": {"id": 263, "name": "product", "url": "audience", "avatar": "year"}, "visitCount": 307, "ordersCount": 130, "salesAmount": 438, "updateTime": 1753433976, "salesCount": 358, "refundAmount": 414, "withdrawAmount": 455}, "result": {"code": "rules_delete_error", "data": {"id": 345, "date": "1991-06-22T15:40:57.410248", "store": {"id": 62, "name": "too", "url": "serve", "avatar": "red"}, "visitCount": 935, "ordersCount": 641, "salesAmount": 747, "updateTime": 1753433976, "salesCount": 770, "refundAmount": 946, "withdrawAmount": 992}}, "errMessageOnly": "these", "successMessage": "look", "errMessage": "size"};
}

module.exports = {
  generateResponseStoreStatLogResponse
};
