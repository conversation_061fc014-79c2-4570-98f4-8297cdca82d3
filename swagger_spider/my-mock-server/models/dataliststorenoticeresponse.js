
// DataListStoreNoticeResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListStoreNoticeResponse模型的模拟数据
 * @returns {DataListStoreNoticeResponse} 模拟数据
 */
function generateDataListStoreNoticeResponse() {
  return {"curPage": 80, "maxPage": 412, "total": 8987, "data": [{"id": 638, "store": {"id": 240, "name": "view", "url": "health", "avatar": "majority"}, "noticeType": "1", "status": "1", "staffList": "prepare", "createTime": **********}]};
}

module.exports = {
  generateDataListStoreNoticeResponse
};
