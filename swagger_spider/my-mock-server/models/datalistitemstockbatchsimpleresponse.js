
// DataListItemStockBatchSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListItemStockBatchSimpleResponse模型的模拟数据
 * @returns {DataListItemStockBatchSimpleResponse} 模拟数据
 */
function generateDataListItemStockBatchSimpleResponse() {
  return {"curPage": 182, "maxPage": 801, "total": 3008, "data": [{"id": 102, "store": {"id": 958, "name": "way", "url": "speech", "avatar": "scene"}, "item": {"id": 395, "name": "hour", "url": "too", "avatar": "hope"}, "batchCount": 688, "salesCount": 459, "replacementCount": 840, "refundCount": 106, "restCount": 163, "createTime": 1753433976}]};
}

module.exports = {
  generateDataListItemStockBatchSimpleResponse
};
