
// ResponseDataListOrdersCommentRatingResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListOrdersCommentRatingResponse模型的模拟数据
 * @returns {ResponseDataListOrdersCommentRatingResponse} 模拟数据
 */
function generateResponseDataListOrdersCommentRatingResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 355, "maxPage": 817, "total": 2488, "data": [{"id": 617, "ordersComment": {"id": 836, "name": "indeed", "url": "idea", "avatar": "able"}, "rating": {"id": 661, "name": "forget", "url": "answer", "avatar": "probably"}, "score": "1"}]}, "result": {"code": "vip_name_exists", "data": {"curPage": 885, "maxPage": 151, "total": 3561, "data": [{"id": 343, "ordersComment": {"id": 633, "name": "free", "url": "bad", "avatar": "deep"}, "rating": {"id": 721, "name": "heavy", "url": "memory", "avatar": "let"}, "score": "1"}]}}, "errMessageOnly": "perhaps", "successMessage": "detail", "errMessage": "soon"};
}

module.exports = {
  generateResponseDataListOrdersCommentRatingResponse
};
