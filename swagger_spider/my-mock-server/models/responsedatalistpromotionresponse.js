
// ResponseDataListPromotionResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListPromotionResponse模型的模拟数据
 * @returns {ResponseDataListPromotionResponse} 模拟数据
 */
function generateResponseDataListPromotionResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 808, "maxPage": 455, "total": 6279, "data": [{"id": 484, "promotionType": "1", "brand": {"id": 627, "name": "mother", "url": "must", "avatar": "military"}, "search": {"id": 615, "name": "point", "url": "exist", "avatar": "approach"}, "user": {"id": 538, "name": "save", "url": "water", "avatar": "official"}}, {"id": 53, "promotionType": "1", "brand": {"id": 517, "name": "letter", "url": "during", "avatar": "especially"}, "search": {"id": 563, "name": "and", "url": "air", "avatar": "style"}, "user": {"id": 128, "name": "plant", "url": "appear", "avatar": "with"}}]}, "result": {"code": "schedule_update_error", "data": {"curPage": 261, "maxPage": 839, "total": 7184, "data": [{"id": 956, "promotionType": "1", "brand": {"id": 317, "name": "how", "url": "huge", "avatar": "rather"}, "search": {"id": 851, "name": "election", "url": "thus", "avatar": "from"}, "user": {"id": 765, "name": "national", "url": "development", "avatar": "easy"}}]}}, "errMessageOnly": "whole", "successMessage": "six", "errMessage": "air"};
}

module.exports = {
  generateResponseDataListPromotionResponse
};
