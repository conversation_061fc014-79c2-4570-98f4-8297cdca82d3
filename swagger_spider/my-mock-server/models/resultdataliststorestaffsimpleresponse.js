
// ResultDataListStoreStaffSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListStoreStaffSimpleResponse模型的模拟数据
 * @returns {ResultDataListStoreStaffSimpleResponse} 模拟数据
 */
function generateResultDataListStoreStaffSimpleResponse() {
  return {"code": "faq_batch_update_error", "data": {"curPage": 595, "maxPage": 509, "total": 9622, "data": [{"id": 134, "store": {"id": 685, "name": "best", "url": "born", "avatar": "bar"}, "user": {"id": 652, "name": "general", "url": "sell", "avatar": "once"}, "status": "1", "nickName": "know"}]}};
}

module.exports = {
  generateResultDataListStoreStaffSimpleResponse
};
