
// ResponseDataListViolationsEventResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListViolationsEventResponse模型的模拟数据
 * @returns {ResponseDataListViolationsEventResponse} 模拟数据
 */
function generateResponseDataListViolationsEventResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 277, "maxPage": 512, "total": 6044, "data": [{"id": 837, "user": {"id": 608, "name": "impact", "url": "be", "avatar": "get"}, "store": {"id": 867, "name": "include", "url": "thousand", "avatar": "develop"}, "item": {"id": 680, "name": "modern", "url": "child", "avatar": "mind"}, "demand": {"id": 522, "name": "whole", "url": "data", "avatar": "unit"}}]}, "result": {"code": "user_email_confirm_failed", "data": {"curPage": 241, "maxPage": 696, "total": 9737, "data": [{"id": 91, "user": {"id": 856, "name": "parent", "url": "success", "avatar": "before"}, "store": {"id": 56, "name": "service", "url": "next", "avatar": "hotel"}, "item": {"id": 214, "name": "hit", "url": "discussion", "avatar": "same"}, "demand": {"id": 652, "name": "foreign", "url": "stage", "avatar": "give"}}, {"id": 697, "user": {"id": 198, "name": "own", "url": "thing", "avatar": "same"}, "store": {"id": 568, "name": "one", "url": "no", "avatar": "answer"}, "item": {"id": 253, "name": "special", "url": "similar", "avatar": "discussion"}, "demand": {"id": 187, "name": "wonder", "url": "bill", "avatar": "property"}}]}}, "errMessageOnly": "society", "successMessage": "machine", "errMessage": "include"};
}

module.exports = {
  generateResponseDataListViolationsEventResponse
};
