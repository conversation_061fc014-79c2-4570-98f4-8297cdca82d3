
// QuickMessageResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成QuickMessageResponse模型的模拟数据
 * @returns {QuickMessageResponse} 模拟数据
 */
function generateQuickMessageResponse() {
  return {"id": 486, "quickMessageTypeId": 834, "manageId": 657, "status": "1", "useCount": 754, "mark": "marriage", "lastUseTime": 1753433976, "updateTime": 1753433976, "createTime": 1753433976, "quickMessageLangId": 581};
}

module.exports = {
  generateQuickMessageResponse
};
