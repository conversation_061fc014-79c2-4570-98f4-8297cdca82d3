
// DataListRatingResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListRatingResponse模型的模拟数据
 * @returns {DataListRatingResponse} 模拟数据
 */
function generateDataListRatingResponse() {
  return {"curPage": 942, "maxPage": 605, "total": 2297, "data": [{"id": 838, "status": "1", "statusName": "raise", "ratingType": "1", "ratingTypeName": "glass", "createTime": 1753433976, "language": "en_US", "langStatus": "1", "langStatusName": "her", "ratingName": "door"}, {"id": 645, "status": "1", "statusName": "live", "ratingType": "1", "ratingTypeName": "actually", "createTime": 1753433976, "language": "en_US", "langStatus": "1", "langStatusName": "rich", "ratingName": "Congress"}, {"id": 894, "status": "1", "statusName": "pay", "ratingType": "1", "ratingTypeName": "once", "createTime": 1753433976, "language": "en_US", "langStatus": "1", "langStatusName": "lot", "ratingName": "space"}]};
}

module.exports = {
  generateDataListRatingResponse
};
