
// AftersalesTypeResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成AftersalesTypeResponse模型的模拟数据
 * @returns {AftersalesTypeResponse} 模拟数据
 */
function generateAftersalesTypeResponse() {
  return {"id": 592, "parentId": 776, "parentType": {"id": 138, "name": "language", "url": "change", "avatar": "hold"}, "status": "1", "statusName": "future", "customUrl": "enjoy", "createTime": 1753433976, "updateTime": 1753433976, "language": "en_US", "typeName": "because"};
}

module.exports = {
  generateAftersalesTypeResponse
};
