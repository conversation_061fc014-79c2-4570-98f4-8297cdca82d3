
// ResponseStoreFaqResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseStoreFaqResponse模型的模拟数据
 * @returns {ResponseStoreFaqResponse} 模拟数据
 */
function generateResponseStoreFaqResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 25, "store": {"id": 727, "name": "put", "url": "sell", "avatar": "term"}, "item": {"id": 918, "name": "bill", "url": "individual", "avatar": "central"}, "status": "1", "language": "en_US", "question": "much", "answer": "discuss", "sortIndex": 441, "createTime": 1753433976, "version": 629}, "result": {"code": "item_orders_requirements_error", "data": {"id": 998, "store": {"id": 584, "name": "may", "url": "a", "avatar": "bag"}, "item": {"id": 108, "name": "staff", "url": "clear", "avatar": "where"}, "status": "1", "language": "en_US", "question": "yeah", "answer": "send", "sortIndex": 161, "createTime": 1753433976, "version": 466}}, "errMessageOnly": "stand", "successMessage": "show", "errMessage": "have"};
}

module.exports = {
  generateResponseStoreFaqResponse
};
