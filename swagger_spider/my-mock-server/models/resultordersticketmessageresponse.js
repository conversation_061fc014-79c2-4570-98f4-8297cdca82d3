
// ResultOrdersTicketMessageResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultOrdersTicketMessageResponse模型的模拟数据
 * @returns {ResultOrdersTicketMessageResponse} 模拟数据
 */
function generateResultOrdersTicketMessageResponse() {
  return {"code": "orders_ticket_message_can_not_read", "data": {"id": 525, "ordersTicketId": 658, "user": {"id": 519, "name": "against", "url": "attack", "avatar": "six"}, "store": {"id": 575, "name": "soldier", "url": "too", "avatar": "specific"}, "userReadTime": 1753433976, "storeReadTime": 1753433976, "replyId": 197, "replyMessageSummary": "less", "messageType": "1", "message": ["1"]}};
}

module.exports = {
  generateResultOrdersTicketMessageResponse
};
