
// ResultDataListServicesAttrListSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListServicesAttrListSimpleResponse模型的模拟数据
 * @returns {ResultDataListServicesAttrListSimpleResponse} 模拟数据
 */
function generateResultDataListServicesAttrListSimpleResponse() {
  return {"code": "schedule_handover_id_not_exists", "data": {"curPage": 382, "maxPage": 251, "total": 4507, "data": [{"id": 460, "services": {"id": 511, "name": "TV", "url": "region", "avatar": "free"}, "attr": {"id": 738, "name": "very", "url": "here", "avatar": "seem"}, "createTime": 1753433976}]}};
}

module.exports = {
  generateResultDataListServicesAttrListSimpleResponse
};
