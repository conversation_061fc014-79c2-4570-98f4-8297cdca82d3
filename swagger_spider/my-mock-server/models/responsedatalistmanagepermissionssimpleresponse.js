
// ResponseDataListManagePermissionsSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListManagePermissionsSimpleResponse模型的模拟数据
 * @returns {ResponseDataListManagePermissionsSimpleResponse} 模拟数据
 */
function generateResponseDataListManagePermissionsSimpleResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 63, "maxPage": 790, "total": 77, "data": [{"id": 978, "manageId": 807, "manageUsername": "unit", "resourceId": 808, "resourceMenuPath": "wish"}, {"id": 273, "manageId": 919, "manageUsername": "much", "resourceId": 487, "resourceMenuPath": "pull"}, {"id": 857, "manageId": 623, "manageUsername": "family", "resourceId": 773, "resourceMenuPath": "side"}]}, "result": {"code": "rating_names_exist", "data": {"curPage": 922, "maxPage": 103, "total": 1091, "data": [{"id": 778, "manageId": 22, "manageUsername": "worker", "resourceId": 921, "resourceMenuPath": "they"}]}}, "errMessageOnly": "main", "successMessage": "generation", "errMessage": "American"};
}

module.exports = {
  generateResponseDataListManagePermissionsSimpleResponse
};
