
// ItemSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ItemSimpleResponse模型的模拟数据
 * @returns {ItemSimpleResponse} 模拟数据
 */
function generateItemSimpleResponse() {
  return {"id": 283, "user": {"id": 674, "name": "off", "url": "often", "avatar": "marriage"}, "store": {"id": 74, "name": "stop", "url": "suggest", "avatar": "show"}, "brand": {"id": 290, "name": "wish", "url": "explain", "avatar": "local"}, "services": {"id": 819, "name": "second", "url": "since", "avatar": "avoid"}, "tagList": [{"id": 924, "name": "what", "url": "newspaper", "avatar": "hope"}, {"id": 787, "name": "that", "url": "pull", "avatar": "type"}], "attrList": [{"attrId": 82, "attrName": "recognize", "attrValueId": 39, "attrValueName": "TV"}, {"attrId": 280, "attrName": "lawyer", "attrValueId": 625, "attrValueName": "design"}, {"attrId": 609, "attrName": "process", "attrValueId": 100, "attrValueName": "night"}], "status": "1", "language": "en_US", "name": "his"};
}

module.exports = {
  generateItemSimpleResponse
};
