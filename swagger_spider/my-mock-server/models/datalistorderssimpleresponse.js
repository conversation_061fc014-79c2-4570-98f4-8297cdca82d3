
// DataListOrdersSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListOrdersSimpleResponse模型的模拟数据
 * @returns {DataListOrdersSimpleResponse} 模拟数据
 */
function generateDataListOrdersSimpleResponse() {
  return {"curPage": 521, "maxPage": 801, "total": 6887, "data": [{"id": 293, "user": {"id": 886, "name": "federal", "url": "could", "avatar": "poor"}, "item": {"id": 311, "name": "police", "url": "player", "avatar": "down"}, "posts": {"id": 832, "name": "write", "url": "two", "avatar": "measure"}, "demand": {"id": 561, "name": "institution", "url": "staff", "avatar": "truth"}, "store": {"id": 707, "name": "add", "url": "agreement", "avatar": "cut"}, "ordersType": "1", "status": "1", "price": 382, "originalPrice": 33}, {"id": 467, "user": {"id": 338, "name": "approach", "url": "in", "avatar": "security"}, "item": {"id": 959, "name": "game", "url": "letter", "avatar": "research"}, "posts": {"id": 142, "name": "station", "url": "watch", "avatar": "worker"}, "demand": {"id": 553, "name": "heavy", "url": "choose", "avatar": "card"}, "store": {"id": 606, "name": "small", "url": "worker", "avatar": "question"}, "ordersType": "1", "status": "1", "price": 910, "originalPrice": 751}, {"id": 69, "user": {"id": 702, "name": "avoid", "url": "between", "avatar": "itself"}, "item": {"id": 477, "name": "teach", "url": "never", "avatar": "deal"}, "posts": {"id": 317, "name": "ask", "url": "finally", "avatar": "rather"}, "demand": {"id": 857, "name": "perform", "url": "easy", "avatar": "account"}, "store": {"id": 695, "name": "them", "url": "moment", "avatar": "form"}, "ordersType": "1", "status": "1", "price": 995, "originalPrice": 533}]};
}

module.exports = {
  generateDataListOrdersSimpleResponse
};
