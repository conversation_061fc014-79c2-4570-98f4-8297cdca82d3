
// ResultDataListItemStockBatchSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListItemStockBatchSimpleResponse模型的模拟数据
 * @returns {ResultDataListItemStockBatchSimpleResponse} 模拟数据
 */
function generateResultDataListItemStockBatchSimpleResponse() {
  return {"code": "schedule_leave_log_leave_type_invalid", "data": {"curPage": 407, "maxPage": 234, "total": 5695, "data": [{"id": 548, "store": {"id": 655, "name": "fire", "url": "radio", "avatar": "eight"}, "item": {"id": 708, "name": "answer", "url": "receive", "avatar": "instead"}, "batchCount": 796, "salesCount": 689}, {"id": 992, "store": {"id": 735, "name": "argue", "url": "few", "avatar": "happy"}, "item": {"id": 598, "name": "degree", "url": "will", "avatar": "seven"}, "batchCount": 139, "salesCount": 302}, {"id": 223, "store": {"id": 379, "name": "may", "url": "teach", "avatar": "mind"}, "item": {"id": 875, "name": "firm", "url": "vote", "avatar": "herself"}, "batchCount": 309, "salesCount": 907}]}};
}

module.exports = {
  generateResultDataListItemStockBatchSimpleResponse
};
