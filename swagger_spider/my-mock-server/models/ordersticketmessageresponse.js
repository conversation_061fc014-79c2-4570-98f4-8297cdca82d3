
// OrdersTicketMessageResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成OrdersTicketMessageResponse模型的模拟数据
 * @returns {OrdersTicketMessageResponse} 模拟数据
 */
function generateOrdersTicketMessageResponse() {
  return {"id": 891, "ordersTicketId": 646, "user": {"id": 605, "name": "offer", "url": "between", "avatar": "job"}, "store": {"id": 57, "name": "bag", "url": "parent", "avatar": "our"}, "userReadTime": 1753433976, "storeReadTime": 1753433976, "replyId": 642, "replyMessageSummary": "if", "messageType": "1", "message": ["1", "1", "1"]};
}

module.exports = {
  generateOrdersTicketMessageResponse
};
