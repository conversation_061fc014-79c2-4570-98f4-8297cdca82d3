
// ResponseDataListScheduleLeaveLogResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListScheduleLeaveLogResponse模型的模拟数据
 * @returns {ResponseDataListScheduleLeaveLogResponse} 模拟数据
 */
function generateResponseDataListScheduleLeaveLogResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 819, "maxPage": 389, "total": 4996, "data": [{"id": 45, "schedule": {"id": 780, "name": "never", "url": "ground", "avatar": "eye"}, "manage": {"id": 590, "name": "responsibility", "url": "moment", "avatar": "have"}, "status": "1", "statusName": "girl"}]}, "result": {"code": "manage_batch_update_status_error", "data": {"curPage": 6, "maxPage": 372, "total": 1216, "data": [{"id": 755, "schedule": {"id": 843, "name": "reduce", "url": "wear", "avatar": "table"}, "manage": {"id": 535, "name": "about", "url": "cup", "avatar": "professional"}, "status": "1", "statusName": "by"}]}}, "errMessageOnly": "look", "successMessage": "toward", "errMessage": "especially"};
}

module.exports = {
  generateResponseDataListScheduleLeaveLogResponse
};
