
// ResponseDataListResourcesPermissionsSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListResourcesPermissionsSimpleResponse模型的模拟数据
 * @returns {ResponseDataListResourcesPermissionsSimpleResponse} 模拟数据
 */
function generateResponseDataListResourcesPermissionsSimpleResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 672, "maxPage": 588, "total": 1598, "data": [{"id": 870, "resourcesId": 558, "resourceMenuPath": "pressure", "resourceMenuName": "suffer", "sortIndex": "0"}]}, "result": {"code": "schedule_handover_log_delete_failed", "data": {"curPage": 114, "maxPage": 169, "total": 7371, "data": [{"id": 326, "resourcesId": 120, "resourceMenuPath": "line", "resourceMenuName": "officer", "sortIndex": "0"}]}}, "errMessageOnly": "reason", "successMessage": "up", "errMessage": "notice"};
}

module.exports = {
  generateResponseDataListResourcesPermissionsSimpleResponse
};
