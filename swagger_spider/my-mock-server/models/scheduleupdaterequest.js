
// ScheduleUpdateRequest 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ScheduleUpdateRequest模型的模拟数据
 * @returns {ScheduleUpdateRequest} 模拟数据
 */
function generateScheduleUpdateRequest() {
  return {"date": "politics", "from": "else", "to": "system", "manageId": 790, "trueManageId": 475, "scheduleName": "seem", "mark": "this", "workStart": "myself", "workEnd": "would"};
}

module.exports = {
  generateScheduleUpdateRequest
};
