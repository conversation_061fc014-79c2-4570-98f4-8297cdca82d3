
// ResultDataListItemStatSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListItemStatSimpleResponse模型的模拟数据
 * @returns {ResultDataListItemStatSimpleResponse} 模拟数据
 */
function generateResultDataListItemStatSimpleResponse() {
  return {"code": "schedule_sign_log_review_status_not_null", "data": {"curPage": 684, "maxPage": 335, "total": 5362, "data": [{"id": 877, "item": {"id": 35, "name": "too", "url": "write", "avatar": "that"}, "store": {"id": 109, "name": "notice", "url": "body", "avatar": "car"}, "visitCount": 788, "ordersCount": 839}]}};
}

module.exports = {
  generateResultDataListItemStatSimpleResponse
};
