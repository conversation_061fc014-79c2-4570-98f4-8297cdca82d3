
// ResultDataListPostsSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListPostsSimpleResponse模型的模拟数据
 * @returns {ResultDataListPostsSimpleResponse} 模拟数据
 */
function generateResultDataListPostsSimpleResponse() {
  return {"code": "services_attr_list_add_error", "data": {"curPage": 698, "maxPage": 143, "total": 6930, "data": [{"id": 647, "postsType": 521, "user": {"id": 272, "name": "quite", "url": "just", "avatar": "husband"}, "store": {"id": 211, "name": "modern", "url": "by", "avatar": "husband"}, "brandList": [{"id": 23, "name": "evening", "url": "certainly", "avatar": "sense"}, {"id": 564, "name": "admit", "url": "teacher", "avatar": "help"}]}, {"id": 58, "postsType": 882, "user": {"id": 413, "name": "appear", "url": "physical", "avatar": "home"}, "store": {"id": 264, "name": "through", "url": "general", "avatar": "instead"}, "brandList": [{"id": 630, "name": "summer", "url": "fly", "avatar": "space"}, {"id": 53, "name": "thought", "url": "important", "avatar": "build"}]}, {"id": 488, "postsType": 702, "user": {"id": 947, "name": "nature", "url": "girl", "avatar": "worker"}, "store": {"id": 784, "name": "relate", "url": "price", "avatar": "suddenly"}, "brandList": [{"id": 517, "name": "professional", "url": "huge", "avatar": "let"}, {"id": 156, "name": "right", "url": "number", "avatar": "without"}]}]}};
}

module.exports = {
  generateResultDataListPostsSimpleResponse
};
