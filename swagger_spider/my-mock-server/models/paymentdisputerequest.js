
// PaymentDisputeRequest 模型
// 由SwaggerCrawler自动生成

/**
 * 生成PaymentDisputeRequest模型的模拟数据
 * @returns {PaymentDisputeRequest} 模拟数据
 */
function generatePaymentDisputeRequest() {
  return {"userId": 423, "paymentRecordId": 6, "paymentId": 449, "disputeType": "NON_DELIVERED", "status": "RESOLVED", "disputeDescription": "its", "manageId": 234, "manageReply": "owner", "disputeTime": 1753433975, "reviewTime": 1753433975};
}

module.exports = {
  generatePaymentDisputeRequest
};
