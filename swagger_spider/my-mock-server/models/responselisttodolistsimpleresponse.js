
// ResponseListTodoListSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseListTodoListSimpleResponse模型的模拟数据
 * @returns {ResponseListTodoListSimpleResponse} 模拟数据
 */
function generateResponseListTodoListSimpleResponse() {
  return {"code": 0, "msg": "success", "data": [{"id": 986, "taskType": "1", "status": "1", "manage": {"id": 824, "name": "same", "url": "wife", "avatar": "usually"}, "prioritySort": 776, "taskName": "plan", "summary": "available", "createTime": 1753433975, "updateTime": 1753433975}], "result": {"code": "rating_type_invalid", "data": [{"id": 757, "taskType": "1", "status": "1", "manage": {"id": 379, "name": "nation", "url": "take", "avatar": "collection"}, "prioritySort": 32}, {"id": 379, "taskType": "1", "status": "1", "manage": {"id": 229, "name": "analysis", "url": "generation", "avatar": "turn"}, "prioritySort": 962}]}, "errMessageOnly": "able", "successMessage": "project", "errMessage": "world"};
}

module.exports = {
  generateResponseListTodoListSimpleResponse
};
