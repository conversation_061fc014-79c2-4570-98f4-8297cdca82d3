
// ResultDataListBadgeGetListSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListBadgeGetListSimpleResponse模型的模拟数据
 * @returns {ResultDataListBadgeGetListSimpleResponse} 模拟数据
 */
function generateResultDataListBadgeGetListSimpleResponse() {
  return {"code": "id_not_exists", "data": {"curPage": 174, "maxPage": 209, "total": 2284, "data": [{"id": 120, "badgeType": "1", "badge": {"id": 249, "name": "yet", "url": "final", "avatar": "serve"}, "relation": {"id": 246, "name": "official", "url": "but", "avatar": "close"}, "expireTime": 1753433976}, {"id": 255, "badgeType": "1", "badge": {"id": 108, "name": "education", "url": "prepare", "avatar": "provide"}, "relation": {"id": 978, "name": "some", "url": "clear", "avatar": "bit"}, "expireTime": 1753433976}, {"id": 866, "badgeType": "1", "badge": {"id": 571, "name": "course", "url": "director", "avatar": "watch"}, "relation": {"id": 74, "name": "claim", "url": "produce", "avatar": "try"}, "expireTime": 1753433976}]}};
}

module.exports = {
  generateResultDataListBadgeGetListSimpleResponse
};
