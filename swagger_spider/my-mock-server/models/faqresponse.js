
// FaqResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成FaqResponse模型的模拟数据
 * @returns {FaqResponse} 模拟数据
 */
function generateFaqResponse() {
  return {"id": 502, "useTypeName": "seven", "manage": {"id": 948, "name": "water", "url": "positive", "avatar": "born"}, "brand": {"id": 349, "name": "expert", "url": "all", "avatar": "protect"}, "statusName": "score", "language": "en_US", "title": "news", "coverPic": "forward", "createTime": 1753433976, "useType": "1"};
}

module.exports = {
  generateFaqResponse
};
