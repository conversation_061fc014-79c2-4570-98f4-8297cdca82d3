
// BrandFieldSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成BrandFieldSimpleResponse模型的模拟数据
 * @returns {BrandFieldSimpleResponse} 模拟数据
 */
function generateBrandFieldSimpleResponse() {
  return {"id": 375, "brand": {"id": 300, "name": "authority", "url": "something", "avatar": "career"}, "fieldName": "show", "fieldType": "1", "required": "1", "language": "en_US", "label": "happen", "defaultValue": "experience", "placeholder": "commercial", "valueList": [{"key": "east", "value": "remain"}, {"key": "must", "value": "say"}]};
}

module.exports = {
  generateBrandFieldSimpleResponse
};
