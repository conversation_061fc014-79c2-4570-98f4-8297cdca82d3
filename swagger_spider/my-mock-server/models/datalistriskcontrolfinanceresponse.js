
// DataListRiskControlFinanceResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListRiskControlFinanceResponse模型的模拟数据
 * @returns {DataListRiskControlFinanceResponse} 模拟数据
 */
function generateDataListRiskControlFinanceResponse() {
  return {"curPage": 821, "maxPage": 43, "total": 5331, "data": [{"id": 412, "status": "1", "riskName": "nearly", "extraData": {}, "checkClass": "recently", "actionClass": "move", "riskCount": 345, "createTime": 1753433976}, {"id": 806, "status": "1", "riskName": "without", "extraData": {}, "checkClass": "remain", "actionClass": "camera", "riskCount": 644, "createTime": 1753433976}, {"id": 179, "status": "1", "riskName": "debate", "extraData": {}, "checkClass": "like", "actionClass": "population", "riskCount": 382, "createTime": 1753433976}]};
}

module.exports = {
  generateDataListRiskControlFinanceResponse
};
