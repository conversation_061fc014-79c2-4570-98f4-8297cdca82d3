
// ResultDataListQuickMessageTypeResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListQuickMessageTypeResponse模型的模拟数据
 * @returns {ResultDataListQuickMessageTypeResponse} 模拟数据
 */
function generateResultDataListQuickMessageTypeResponse() {
  return {"code": "password_valid_error", "data": {"curPage": 71, "maxPage": 447, "total": 38, "data": [{"id": 430, "status": "1", "typeName": "address", "mark": "really", "createTime": 1753433976}, {"id": 50, "status": "1", "typeName": "team", "mark": "police", "createTime": 1753433976}]}};
}

module.exports = {
  generateResultDataListQuickMessageTypeResponse
};
