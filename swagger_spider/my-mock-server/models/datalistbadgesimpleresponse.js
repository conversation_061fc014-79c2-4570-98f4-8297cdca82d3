
// DataListBadgeSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListBadgeSimpleResponse模型的模拟数据
 * @returns {DataListBadgeSimpleResponse} 模拟数据
 */
function generateDataListBadgeSimpleResponse() {
  return {"curPage": 90, "maxPage": 660, "total": 9469, "data": [{"id": 604, "status": "1", "language": "en_US", "badgeType": "1", "badgeName": "north", "customUrl": "loss", "icon": "reality", "createTime": 1753433976}, {"id": 59, "status": "1", "language": "en_US", "badgeType": "1", "badgeName": "hundred", "customUrl": "I", "icon": "also", "createTime": 1753433976}, {"id": 66, "status": "1", "language": "en_US", "badgeType": "1", "badgeName": "wrong", "customUrl": "threat", "icon": "give", "createTime": 1753433976}]};
}

module.exports = {
  generateDataListBadgeSimpleResponse
};
