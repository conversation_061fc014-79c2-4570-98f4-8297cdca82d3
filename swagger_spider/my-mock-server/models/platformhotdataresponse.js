
// PlatformHotDataResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成PlatformHotDataResponse模型的模拟数据
 * @returns {PlatformHotDataResponse} 模拟数据
 */
function generatePlatformHotDataResponse() {
  return {"hotItemResponseList": [{"sortIndex": 928, "item": {"id": 612, "name": "also", "url": "support", "avatar": "day"}, "salesUnitPrice": 244, "totalSalesAmount": 251}, {"sortIndex": 331, "item": {"id": 838, "name": "source", "url": "especially", "avatar": "meet"}, "salesUnitPrice": 756, "totalSalesAmount": 547}, {"sortIndex": 992, "item": {"id": 966, "name": "wonder", "url": "attorney", "avatar": "poor"}, "salesUnitPrice": 657, "totalSalesAmount": 203}], "hotStoreResponseList": [{"sortIndex": 17, "store": {"id": 19, "name": "true", "url": "mission", "avatar": "top"}, "salesUnitPrice": 671, "totalSalesAmount": 665}, {"sortIndex": 136, "store": {"id": 163, "name": "learn", "url": "lay", "avatar": "coach"}, "salesUnitPrice": 847, "totalSalesAmount": 597}, {"sortIndex": 52, "store": {"id": 577, "name": "second", "url": "other", "avatar": "work"}, "salesUnitPrice": 964, "totalSalesAmount": 148}], "hotPostsResponseList": [{"sortIndex": 886, "user": {"id": 22, "name": "goal", "url": "cup", "avatar": "difficult"}, "posts": {"id": 787, "name": "city", "url": "central", "avatar": "summer"}, "likeCount": 668}, {"sortIndex": 756, "user": {"id": 118, "name": "than", "url": "full", "avatar": "have"}, "posts": {"id": 598, "name": "father", "url": "take", "avatar": "wife"}, "likeCount": 426}, {"sortIndex": 74, "user": {"id": 484, "name": "however", "url": "response", "avatar": "pay"}, "posts": {"id": 304, "name": "anyone", "url": "one", "avatar": "sign"}, "likeCount": 783}], "hotDemandResponseList": [{"sortIndex": 327, "user": {"id": 397, "name": "hope", "url": "understand", "avatar": "though"}, "demand": {"id": 971, "name": "attention", "url": "finally", "avatar": "executive"}, "demandBidCount": 471}]};
}

module.exports = {
  generatePlatformHotDataResponse
};
