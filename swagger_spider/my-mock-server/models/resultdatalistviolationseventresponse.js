
// ResultDataListViolationsEventResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListViolationsEventResponse模型的模拟数据
 * @returns {ResultDataListViolationsEventResponse} 模拟数据
 */
function generateResultDataListViolationsEventResponse() {
  return {"code": "db_duplicate_key", "data": {"curPage": 987, "maxPage": 174, "total": 5814, "data": [{"id": 81, "user": {"id": 817, "name": "air", "url": "leave", "avatar": "catch"}, "store": {"id": 88, "name": "network", "url": "community", "avatar": "race"}, "item": {"id": 789, "name": "account", "url": "middle", "avatar": "teacher"}, "demand": {"id": 619, "name": "second", "url": "sell", "avatar": "word"}}, {"id": 52, "user": {"id": 739, "name": "step", "url": "threat", "avatar": "stop"}, "store": {"id": 223, "name": "modern", "url": "major", "avatar": "investment"}, "item": {"id": 673, "name": "spring", "url": "them", "avatar": "everything"}, "demand": {"id": 497, "name": "note", "url": "hotel", "avatar": "compare"}}]}};
}

module.exports = {
  generateResultDataListViolationsEventResponse
};
