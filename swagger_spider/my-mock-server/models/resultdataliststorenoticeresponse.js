
// ResultDataListStoreNoticeResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListStoreNoticeResponse模型的模拟数据
 * @returns {ResultDataListStoreNoticeResponse} 模拟数据
 */
function generateResultDataListStoreNoticeResponse() {
  return {"code": "brand_shield_area_id_not_exists", "data": {"curPage": 227, "maxPage": 661, "total": 1909, "data": [{"id": 708, "store": {"id": 498, "name": "stand", "url": "director", "avatar": "edge"}, "noticeType": "1", "status": "1", "staffList": "development"}, {"id": 759, "store": {"id": 93, "name": "mention", "url": "product", "avatar": "election"}, "noticeType": "1", "status": "1", "staffList": "glass"}]}};
}

module.exports = {
  generateResultDataListStoreNoticeResponse
};
