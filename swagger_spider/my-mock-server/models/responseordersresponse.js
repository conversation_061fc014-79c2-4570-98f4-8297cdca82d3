
// ResponseOrdersResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseOrdersResponse模型的模拟数据
 * @returns {ResponseOrdersResponse} 模拟数据
 */
function generateResponseOrdersResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 759, "user": {"id": 354, "name": "term", "url": "guy", "avatar": "skin"}, "item": {"id": 995, "name": "despite", "url": "fish", "avatar": "experience"}, "posts": {"id": 976, "name": "religious", "url": "point", "avatar": "capital"}, "demand": {"id": 663, "name": "country", "url": "similar", "avatar": "yard"}, "store": {"id": 695, "name": "start", "url": "of", "avatar": "fish"}, "ordersType": "1", "status": "1", "price": 893, "originalPrice": 553}, "result": {"code": "schedule_leave_log_apply_error", "data": {"id": 604, "user": {"id": 23, "name": "inside", "url": "memory", "avatar": "north"}, "item": {"id": 86, "name": "whole", "url": "spend", "avatar": "mother"}, "posts": {"id": 755, "name": "education", "url": "example", "avatar": "yard"}, "demand": {"id": 565, "name": "school", "url": "manage", "avatar": "week"}, "store": {"id": 330, "name": "mind", "url": "but", "avatar": "television"}, "ordersType": "1", "status": "1", "price": 76, "originalPrice": 500}}, "errMessageOnly": "commercial", "successMessage": "age", "errMessage": "within"};
}

module.exports = {
  generateResponseOrdersResponse
};
