
// ResponseCampaignResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseCampaignResponse模型的模拟数据
 * @returns {ResponseCampaignResponse} 模拟数据
 */
function generateResponseCampaignResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 939, "status": "1", "customUrl": "total", "coverPic": "six", "createTime": 1753433976, "campaignLangId": 644, "language": "en_US", "title": "man", "summary": "involve", "seoKeywords": "particular"}, "result": {"code": "captcha_error", "data": {"id": 93, "status": "1", "customUrl": "over", "coverPic": "participant", "createTime": 1753433976, "campaignLangId": 378, "language": "en_US", "title": "final", "summary": "past", "seoKeywords": "however"}}, "errMessageOnly": "list", "successMessage": "military", "errMessage": "option"};
}

module.exports = {
  generateResponseCampaignResponse
};
