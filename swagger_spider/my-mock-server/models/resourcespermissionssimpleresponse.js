
// ResourcesPermissionsSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResourcesPermissionsSimpleResponse模型的模拟数据
 * @returns {ResourcesPermissionsSimpleResponse} 模拟数据
 */
function generateResourcesPermissionsSimpleResponse() {
  return {"id": 348, "resourcesId": 738, "resourceMenuPath": "bit", "resourceMenuName": "his", "sortIndex": "0", "showStatus": "1", "permissionName": "mean", "fields": ["five"], "buttons": ["item", "add", "I"], "urls": ["here"]};
}

module.exports = {
  generateResourcesPermissionsSimpleResponse
};
