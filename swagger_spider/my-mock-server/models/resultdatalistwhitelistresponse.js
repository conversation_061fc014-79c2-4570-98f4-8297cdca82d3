
// ResultDataListWhitelistResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListWhitelistResponse模型的模拟数据
 * @returns {ResultDataListWhitelistResponse} 模拟数据
 */
function generateResultDataListWhitelistResponse() {
  return {"code": "demand_select_bidder_failed", "data": {"curPage": 470, "maxPage": 70, "total": 2243, "data": [{"id": 620, "status": "1", "language": "en_US", "itemType": "1", "itemValue": "develop"}, {"id": 966, "status": "1", "language": "en_US", "itemType": "1", "itemValue": "model"}]}};
}

module.exports = {
  generateResultDataListWhitelistResponse
};
