
// ResponseDataListRulesSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListRulesSimpleResponse模型的模拟数据
 * @returns {ResponseDataListRulesSimpleResponse} 模拟数据
 */
function generateResponseDataListRulesSimpleResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 384, "maxPage": 523, "total": 264, "data": [{"id": 462, "status": "1", "rulesType": "1", "indexNumber": "at", "createTime": 1753433976}, {"id": 772, "status": "1", "rulesType": "1", "indexNumber": "arm", "createTime": 1753433976}, {"id": 501, "status": "1", "rulesType": "1", "indexNumber": "edge", "createTime": 1753433976}]}, "result": {"code": "store_stat_log_id_not_exists", "data": {"curPage": 222, "maxPage": 822, "total": 959, "data": [{"id": 962, "status": "1", "rulesType": "1", "indexNumber": "likely", "createTime": 1753433976}, {"id": 611, "status": "1", "rulesType": "1", "indexNumber": "knowledge", "createTime": 1753433976}]}}, "errMessageOnly": "full", "successMessage": "smile", "errMessage": "religious"};
}

module.exports = {
  generateResponseDataListRulesSimpleResponse
};
