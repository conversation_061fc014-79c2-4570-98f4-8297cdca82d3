
// ResultDataListPromotionResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListPromotionResponse模型的模拟数据
 * @returns {ResultDataListPromotionResponse} 模拟数据
 */
function generateResultDataListPromotionResponse() {
  return {"code": "pages_ids_can_not_be_null", "data": {"curPage": 929, "maxPage": 411, "total": 8619, "data": [{"id": 168, "promotionType": "1", "brand": {"id": 544, "name": "much", "url": "hit", "avatar": "move"}, "search": {"id": 240, "name": "three", "url": "cause", "avatar": "few"}, "user": {"id": 298, "name": "cost", "url": "very", "avatar": "make"}}, {"id": 295, "promotionType": "1", "brand": {"id": 980, "name": "whole", "url": "finally", "avatar": "at"}, "search": {"id": 819, "name": "outside", "url": "structure", "avatar": "sea"}, "user": {"id": 723, "name": "writer", "url": "soldier", "avatar": "last"}}, {"id": 612, "promotionType": "1", "brand": {"id": 707, "name": "after", "url": "eight", "avatar": "police"}, "search": {"id": 234, "name": "price", "url": "also", "avatar": "past"}, "user": {"id": 952, "name": "unit", "url": "hundred", "avatar": "yourself"}}]}};
}

module.exports = {
  generateResultDataListPromotionResponse
};
