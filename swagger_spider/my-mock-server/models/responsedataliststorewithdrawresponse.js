
// ResponseDataListStoreWithdrawResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListStoreWithdrawResponse模型的模拟数据
 * @returns {ResponseDataListStoreWithdrawResponse} 模拟数据
 */
function generateResponseDataListStoreWithdrawResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 100, "maxPage": 646, "total": 1561, "data": [{"id": 963, "user": {"id": 954, "name": "political", "url": "notice", "avatar": "meet"}, "store": {"id": 218, "name": "compare", "url": "responsibility", "avatar": "evidence"}, "userWallet": {"id": 738, "name": "wear", "url": "candidate", "avatar": "road"}, "status": "1"}]}, "result": {"code": "quick_message_add_failure", "data": {"curPage": 590, "maxPage": 996, "total": 3609, "data": [{"id": 394, "user": {"id": 426, "name": "new", "url": "expert", "avatar": "true"}, "store": {"id": 775, "name": "half", "url": "hear", "avatar": "politics"}, "userWallet": {"id": 194, "name": "later", "url": "national", "avatar": "window"}, "status": "1"}, {"id": 997, "user": {"id": 23, "name": "fact", "url": "language", "avatar": "human"}, "store": {"id": 338, "name": "difference", "url": "raise", "avatar": "conference"}, "userWallet": {"id": 93, "name": "back", "url": "discover", "avatar": "miss"}, "status": "1"}]}}, "errMessageOnly": "of", "successMessage": "camera", "errMessage": "against"};
}

module.exports = {
  generateResponseDataListStoreWithdrawResponse
};
