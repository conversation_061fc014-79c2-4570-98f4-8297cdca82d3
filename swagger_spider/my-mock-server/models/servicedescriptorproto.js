
// ServiceDescriptorProto 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ServiceDescriptorProto模型的模拟数据
 * @returns {ServiceDescriptorProto} 模拟数据
 */
function generateServiceDescriptorProto() {
  return {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 785, "serializedSizeAsMessageSet": 150, "empty": true}, "initialized": true, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 829, "serializedSizeAsMessageSet": 347, "empty": true}, "initialized": true, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 35, "serializedSizeAsMessageSet": 519}, "initialized": false, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}, "serializedSize": 128, "fieldPresence": "IMPLICIT", "messageEncoding": "MESSAGE_ENCODING_UNKNOWN", "utf8Validation": "VERIFY", "repeatedFieldEncoding": "PACKED"}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}, "parserForType": {}, "serializedSize": 813, "uninterpretedOptionOrBuilderList": [{"doubleValue": 810.95, "stringValue": {"validUtf8": true, "empty": true}, "nameList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 735, "serializedSizeAsMessageSet": 753}, "initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 636}, {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 716, "serializedSizeAsMessageSet": 456}, "initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 741}], "identifierValue": "machine", "positiveIntValue": 1173}, {"doubleValue": 316.15, "stringValue": {"validUtf8": false, "empty": true}, "nameList": [{"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 511, "serializedSizeAsMessageSet": 21}, "initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 495}, {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 293, "serializedSizeAsMessageSet": 820}, "initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 249}], "identifierValue": "style", "positiveIntValue": 6474}, {"doubleValue": 475.4, "stringValue": {"validUtf8": false, "empty": true}, "nameList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 404, "serializedSizeAsMessageSet": 619}, "initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 659}, {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 625, "serializedSizeAsMessageSet": 858}, "initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 219}], "identifierValue": "task", "positiveIntValue": 2234}], "uninterpretedOptionCount": 869, "uninterpretedOptionList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 854, "serializedSizeAsMessageSet": 476}, "initialized": true, "doubleValue": 780.82, "defaultInstanceForType": {"id": 1, "name": "mock_UninterpretedOption"}, "parserForType": {}}]}, "defaultInstanceForType": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 438, "serializedSizeAsMessageSet": 432, "empty": true}, "initialized": true, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 349, "serializedSizeAsMessageSet": 953}, "initialized": true, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 346, "serializedSizeAsMessageSet": 837}, "initialized": false, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}, "parserForType": {}, "serializedSize": 30, "uninterpretedOptionOrBuilderList": [{"doubleValue": 719.63, "stringValue": {"validUtf8": true, "empty": true}, "nameList": [{"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 520, "serializedSizeAsMessageSet": 334}, "initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 773}, {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 988, "serializedSizeAsMessageSet": 916}, "initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 600}], "identifierValue": "much", "positiveIntValue": 2514}, {"doubleValue": 860.3, "stringValue": {"validUtf8": true, "empty": false}, "nameList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 354, "serializedSizeAsMessageSet": 767}, "initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 696}], "identifierValue": "board", "positiveIntValue": 4129}], "uninterpretedOptionCount": 352, "uninterpretedOptionList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 529, "serializedSizeAsMessageSet": 424}, "initialized": true, "doubleValue": 28.34, "defaultInstanceForType": {"id": 1, "name": "mock_UninterpretedOption"}, "parserForType": {}}, {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 177, "serializedSizeAsMessageSet": 170}, "initialized": true, "doubleValue": 287.65, "defaultInstanceForType": {"id": 1, "name": "mock_UninterpretedOption"}, "parserForType": {}}, {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 260, "serializedSizeAsMessageSet": 933}, "initialized": true, "doubleValue": 408.51, "defaultInstanceForType": {"id": 1, "name": "mock_UninterpretedOption"}, "parserForType": {}}]}, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceDescriptorProto"}, "parserForType": {}, "serializedSize": 119, "methodList": [{"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 587, "serializedSizeAsMessageSet": 178}, "initialized": true, "outputType": "expect", "inputType": "modern", "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 296, "serializedSizeAsMessageSet": 114}, "initialized": true, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 87, "serializedSizeAsMessageSet": 352}, "initialized": false, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_MethodOptions"}}}, {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 802, "serializedSizeAsMessageSet": 654}, "initialized": false, "outputType": "morning", "inputType": "soon", "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 850, "serializedSizeAsMessageSet": 871}, "initialized": true, "features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 803, "serializedSizeAsMessageSet": 137}, "initialized": true, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_MethodOptions"}}}, {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 901, "serializedSizeAsMessageSet": 912}, "initialized": false, "outputType": "difference", "inputType": "specific", "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 256, "serializedSizeAsMessageSet": 267}, "initialized": false, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 764, "serializedSizeAsMessageSet": 440}, "initialized": true, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_MethodOptions"}}}], "nameBytes": {"validUtf8": false, "empty": true}, "methodOrBuilderList": [{"outputType": "mean", "inputType": "enough", "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 884, "serializedSizeAsMessageSet": 469}, "initialized": false, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 883, "serializedSizeAsMessageSet": 253}, "initialized": true, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_MethodOptions"}}, "clientStreaming": false, "serverStreaming": true}], "optionsOrBuilder": {"features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 537, "serializedSizeAsMessageSet": 227}, "initialized": false, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": true, "uninterpretedOptionOrBuilderList": [{"doubleValue": 161.47, "stringValue": {"validUtf8": true, "empty": false}, "nameList": [{"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 422, "serializedSizeAsMessageSet": 9}, "initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 980}, {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 645, "serializedSizeAsMessageSet": 356}, "initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 209}], "identifierValue": "someone", "positiveIntValue": 7483}, {"doubleValue": 321.19, "stringValue": {"validUtf8": false, "empty": true}, "nameList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 816, "serializedSizeAsMessageSet": 150}, "initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 735}, {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 702, "serializedSizeAsMessageSet": 135}, "initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 795}], "identifierValue": "time", "positiveIntValue": 4329}, {"doubleValue": 86.21, "stringValue": {"validUtf8": true, "empty": true}, "nameList": [{"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 165, "serializedSizeAsMessageSet": 813}, "initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 198}, {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 672, "serializedSizeAsMessageSet": 927}, "initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 346}], "identifierValue": "father", "positiveIntValue": 9210}], "uninterpretedOptionCount": 456, "uninterpretedOptionList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 838, "serializedSizeAsMessageSet": 934}, "initialized": false, "doubleValue": 368.48, "defaultInstanceForType": {"id": 1, "name": "mock_UninterpretedOption"}, "parserForType": {}}, {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 869, "serializedSizeAsMessageSet": 498}, "initialized": false, "doubleValue": 680.03, "defaultInstanceForType": {"id": 1, "name": "mock_UninterpretedOption"}, "parserForType": {}}, {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 993, "serializedSizeAsMessageSet": 203}, "initialized": true, "doubleValue": 537.21, "defaultInstanceForType": {"id": 1, "name": "mock_UninterpretedOption"}, "parserForType": {}}], "featuresOrBuilder": {"enumType": "OPEN", "fieldPresence": "LEGACY_REQUIRED", "messageEncoding": "MESSAGE_ENCODING_UNKNOWN", "utf8Validation": "UTF8_VALIDATION_UNKNOWN", "repeatedFieldEncoding": "EXPANDED"}, "defaultInstanceForType": {"parserForType": {}, "serializedSize": 651, "initialized": false, "defaultInstanceForType": {"parserForType": {}, "serializedSize": 543, "initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_MessageLite"}}, "descriptorForType": {"index": 197, "proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 381, "serializedSizeAsMessageSet": 907}, "initialized": false, "fieldCount": 471, "reservedRangeList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": false, "start": 569, "end": 733, "defaultInstanceForType": {"id": 1, "name": "mock_ReservedRange"}}], "reservedNameList": ["future"]}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 42, "serializedSizeAsMessageSet": 736}, "initialized": true, "features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 357, "serializedSizeAsMessageSet": 907}, "initialized": false, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_MessageOptions"}}, "fullName": "plant", "file": {"proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 619, "serializedSizeAsMessageSet": 634}, "initialized": false, "enumTypeCount": 92, "extensionCount": 707, "options": {"unknownFields": {"mock": true}, "initialized": false, "features": {"mock": true}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 375, "serializedSizeAsMessageSet": 288}, "initialized": true, "features": {"unknownFields": {"mock": true}, "initialized": false, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {"mock": true}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}, "messageTypes": [{"id": 1, "name": "mock_Descriptor"}], "enumTypes": [{"index": 89, "proto": {"unknownFields": {"mock": true}, "initialized": true, "reservedRangeList": [], "reservedNameList": [], "valueList": []}, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"mock": true}}, "fullName": "everything", "file": {"id": 1, "name": "mock_FileDescriptor"}}, {"index": 674, "proto": {"unknownFields": {"mock": true}, "initialized": true, "reservedRangeList": [], "reservedNameList": [], "valueList": []}, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"mock": true}}, "fullName": "effect", "file": {"id": 1, "name": "mock_FileDescriptor"}}], "services": [{"index": 757, "proto": {"id": 1, "name": "mock_ServiceDescriptorProto"}, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"mock": true}}, "fullName": "fly", "file": {"id": 1, "name": "mock_FileDescriptor"}}]}}}, "descriptorForType": {"index": 354, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 928, "serializedSizeAsMessageSet": 465}, "initialized": true, "fieldCount": 459, "reservedRangeList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 241, "serializedSizeAsMessageSet": 587}, "initialized": true, "start": 113, "end": 76, "defaultInstanceForType": {"id": 1, "name": "mock_ReservedRange"}}], "reservedNameList": ["teach", "into"]}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 800, "serializedSizeAsMessageSet": 894}, "initialized": true, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 181, "serializedSizeAsMessageSet": 620}, "initialized": false, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_MessageOptions"}}, "fullName": "begin", "file": {"proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 674, "serializedSizeAsMessageSet": 762}, "initialized": false, "enumTypeCount": 170, "extensionCount": 762, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 558, "serializedSizeAsMessageSet": 196}, "initialized": true, "features": {"unknownFields": {"mock": true}, "initialized": true, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {"mock": true}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 660, "serializedSizeAsMessageSet": 44}, "initialized": false, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 901, "serializedSizeAsMessageSet": 819}, "initialized": true, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}, "messageTypes": [{"id": 1, "name": "mock_Descriptor"}, {"id": 1, "name": "mock_Descriptor"}], "enumTypes": [{"index": 109, "proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": true, "reservedRangeList": [{"mock": true}], "reservedNameList": ["game"], "valueList": [{"mock": true}, {"mock": true}]}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": false, "features": {"unknownFields": {"mock": true}, "initialized": true, "enumType": "mock_string", "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_EnumOptions"}}, "fullName": "laugh", "file": {"id": 1, "name": "mock_FileDescriptor"}}, {"index": 31, "proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": false, "reservedRangeList": [{"mock": true}], "reservedNameList": ["condition", "white"], "valueList": [{"mock": true}, {"mock": true}]}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": true, "features": {"unknownFields": {"mock": true}, "initialized": true, "enumType": "mock_string", "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_EnumOptions"}}, "fullName": "democratic", "file": {"id": 1, "name": "mock_FileDescriptor"}}], "services": [{"index": 865, "proto": {"id": 1, "name": "mock_ServiceDescriptorProto"}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": false, "features": {"unknownFields": {"mock": true}, "initialized": true, "enumType": "mock_string", "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}}, "fullName": "race", "file": {"id": 1, "name": "mock_FileDescriptor"}}]}}, "allFields": {}, "initializationErrorString": "order"}}, "parserForType": {}, "serializedSize": 77, "methodList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 447, "serializedSizeAsMessageSet": 452}, "initialized": true, "outputType": "common", "inputType": "health", "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 265, "serializedSizeAsMessageSet": 932}, "initialized": true, "features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 471, "serializedSizeAsMessageSet": 792}, "initialized": false, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_MethodOptions"}}, "defaultInstanceForType": {"id": 1, "name": "mock_MethodDescriptorProto"}, "parserForType": {}, "serializedSize": 884, "clientStreaming": false, "serverStreaming": true}, {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 899, "serializedSizeAsMessageSet": 975}, "initialized": true, "outputType": "medical", "inputType": "once", "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 267, "serializedSizeAsMessageSet": 790}, "initialized": false, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 947, "serializedSizeAsMessageSet": 744}, "initialized": false, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_MethodOptions"}}, "defaultInstanceForType": {"id": 1, "name": "mock_MethodDescriptorProto"}, "parserForType": {}, "serializedSize": 912, "clientStreaming": true, "serverStreaming": true}, {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 198, "serializedSizeAsMessageSet": 833}, "initialized": false, "outputType": "herself", "inputType": "door", "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 470, "serializedSizeAsMessageSet": 941}, "initialized": false, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 622, "serializedSizeAsMessageSet": 835}, "initialized": false, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_MethodOptions"}}, "defaultInstanceForType": {"id": 1, "name": "mock_MethodDescriptorProto"}, "parserForType": {}, "serializedSize": 189, "clientStreaming": true, "serverStreaming": false}], "nameBytes": {"validUtf8": true, "empty": true}, "methodOrBuilderList": [{"outputType": "check", "inputType": "who", "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 510, "serializedSizeAsMessageSet": 565}, "initialized": true, "features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 227, "serializedSizeAsMessageSet": 731}, "initialized": true, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_MethodOptions"}}, "clientStreaming": true, "serverStreaming": false, "nameBytes": {"validUtf8": false, "empty": true}, "inputTypeBytes": {"validUtf8": false, "empty": true}, "outputTypeBytes": {"validUtf8": false, "empty": false}, "optionsOrBuilder": {"features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 316, "serializedSizeAsMessageSet": 536}, "initialized": false, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": true, "uninterpretedOptionOrBuilderList": [{"doubleValue": 815.58, "stringValue": {"validUtf8": false, "empty": true}, "nameList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 628, "serializedSizeAsMessageSet": 888}, "initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 864}, {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 848, "serializedSizeAsMessageSet": 620}, "initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 242}], "identifierValue": "that", "positiveIntValue": 994}], "uninterpretedOptionCount": 777, "uninterpretedOptionList": [{"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 795, "serializedSizeAsMessageSet": 538}, "initialized": true, "doubleValue": 675.62, "defaultInstanceForType": {"id": 1, "name": "mock_UninterpretedOption"}, "parserForType": {}}, {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 569, "serializedSizeAsMessageSet": 308}, "initialized": false, "doubleValue": 298.83, "defaultInstanceForType": {"id": 1, "name": "mock_UninterpretedOption"}, "parserForType": {}}]}, "name": "college"}], "optionsOrBuilder": {"features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 534, "serializedSizeAsMessageSet": 481}, "initialized": true, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}, "serializedSize": 136, "fieldPresence": "IMPLICIT", "messageEncoding": "DELIMITED", "utf8Validation": "NONE", "repeatedFieldEncoding": "EXPANDED"}, "deprecated": true, "uninterpretedOptionOrBuilderList": [{"doubleValue": 745.69, "stringValue": {"validUtf8": false, "empty": true}, "nameList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 499, "serializedSizeAsMessageSet": 920}, "initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 905}], "identifierValue": "rock", "positiveIntValue": 8034}, {"doubleValue": 298.65, "stringValue": {"validUtf8": true, "empty": true}, "nameList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 560, "serializedSizeAsMessageSet": 228}, "initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 934}, {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 487, "serializedSizeAsMessageSet": 721}, "initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 251}], "identifierValue": "nor", "positiveIntValue": 8630}], "uninterpretedOptionCount": 67, "uninterpretedOptionList": [{"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 304, "serializedSizeAsMessageSet": 391}, "initialized": false, "doubleValue": 803.44, "defaultInstanceForType": {"id": 1, "name": "mock_UninterpretedOption"}, "parserForType": {}}, {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 714, "serializedSizeAsMessageSet": 509}, "initialized": false, "doubleValue": 91.4, "defaultInstanceForType": {"id": 1, "name": "mock_UninterpretedOption"}, "parserForType": {}}], "featuresOrBuilder": {"enumType": "OPEN", "fieldPresence": "EXPLICIT", "messageEncoding": "MESSAGE_ENCODING_UNKNOWN", "utf8Validation": "VERIFY", "repeatedFieldEncoding": "PACKED", "jsonFormat": "ALLOW", "defaultInstanceForType": {"parserForType": {}, "serializedSize": 473, "initialized": false, "defaultInstanceForType": {"parserForType": {}, "serializedSize": 724, "initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_MessageLite"}}, "descriptorForType": {"index": 70, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 777, "serializedSizeAsMessageSet": 857}, "initialized": false, "fieldCount": 811, "reservedRangeList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": false, "start": 303, "end": 1, "defaultInstanceForType": {"id": 1, "name": "mock_ReservedRange"}}, {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": false, "start": 746, "end": 273, "defaultInstanceForType": {"id": 1, "name": "mock_ReservedRange"}}], "reservedNameList": ["company", "later"]}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 788, "serializedSizeAsMessageSet": 219}, "initialized": false, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 522, "serializedSizeAsMessageSet": 814}, "initialized": true, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_MessageOptions"}}, "fullName": "control", "file": {"proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 299, "serializedSizeAsMessageSet": 553}, "initialized": false, "enumTypeCount": 612, "extensionCount": 40, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 471, "serializedSizeAsMessageSet": 925}, "initialized": true, "features": {"unknownFields": {"mock": true}, "initialized": false, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {"mock": true}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}, "messageTypes": [{"id": 1, "name": "mock_Descriptor"}], "enumTypes": [{"index": 756, "proto": {"unknownFields": {"mock": true}, "initialized": true, "reservedRangeList": [], "reservedNameList": [], "valueList": []}, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"mock": true}}, "fullName": "protect", "file": {"id": 1, "name": "mock_FileDescriptor"}}, {"index": 826, "proto": {"unknownFields": {"mock": true}, "initialized": true, "reservedRangeList": [], "reservedNameList": [], "valueList": []}, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"mock": true}}, "fullName": "support", "file": {"id": 1, "name": "mock_FileDescriptor"}}], "services": [{"index": 38, "proto": {"unknownFields": {"mock": true}, "initialized": true, "options": {"mock": true}, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"mock": true}}, "fullName": "development", "file": {"id": 1, "name": "mock_FileDescriptor"}}]}}}, "descriptorForType": {"index": 938, "proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 736, "serializedSizeAsMessageSet": 411}, "initialized": false, "fieldCount": 413, "reservedRangeList": [{"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 495, "serializedSizeAsMessageSet": 604}, "initialized": true, "start": 355, "end": 676, "defaultInstanceForType": {"id": 1, "name": "mock_ReservedRange"}}, {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 300, "serializedSizeAsMessageSet": 985}, "initialized": true, "start": 785, "end": 890, "defaultInstanceForType": {"id": 1, "name": "mock_ReservedRange"}}], "reservedNameList": ["return"]}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 564, "serializedSizeAsMessageSet": 65}, "initialized": false, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 542, "serializedSizeAsMessageSet": 140}, "initialized": true, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_MessageOptions"}}, "fullName": "race", "file": {"proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 226, "serializedSizeAsMessageSet": 921}, "initialized": true, "enumTypeCount": 350, "extensionCount": 586, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 880, "serializedSizeAsMessageSet": 334}, "initialized": false, "features": {"unknownFields": {"mock": true}, "initialized": false, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {"mock": true}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 256, "serializedSizeAsMessageSet": 676}, "initialized": false, "features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 650, "serializedSizeAsMessageSet": 866}, "initialized": true, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}, "messageTypes": [{"id": 1, "name": "mock_Descriptor"}], "enumTypes": [{"index": 754, "proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": true, "reservedRangeList": [{"mock": true}, {"mock": true}], "reservedNameList": ["third", "member"], "valueList": [{"mock": true}, {"mock": true}]}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": true, "features": {"unknownFields": {"mock": true}, "initialized": true, "enumType": "mock_string", "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_EnumOptions"}}, "fullName": "too", "file": {"id": 1, "name": "mock_FileDescriptor"}}], "services": [{"index": 98, "proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": true, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"mock": true}}, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceDescriptorProto"}, "parserForType": {}}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": false, "features": {"unknownFields": {"mock": true}, "initialized": true, "enumType": "mock_string", "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}}, "fullName": "newspaper", "file": {"id": 1, "name": "mock_FileDescriptor"}}, {"index": 682, "proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": true, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"mock": true}}, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceDescriptorProto"}, "parserForType": {}}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": false, "features": {"unknownFields": {"mock": true}, "initialized": true, "enumType": "mock_string", "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}}, "fullName": "foot", "file": {"id": 1, "name": "mock_FileDescriptor"}}]}}, "allFields": {}, "initializationErrorString": "save"}, "defaultInstanceForType": {"parserForType": {}, "serializedSize": 391, "initialized": true, "defaultInstanceForType": {"parserForType": {}, "serializedSize": 129, "initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_MessageLite"}}, "descriptorForType": {"index": 138, "proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 41, "serializedSizeAsMessageSet": 745}, "initialized": true, "fieldCount": 510, "reservedRangeList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 843, "serializedSizeAsMessageSet": 542}, "initialized": true, "start": 733, "end": 136, "defaultInstanceForType": {"id": 1, "name": "mock_ReservedRange"}}, {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 867, "serializedSizeAsMessageSet": 41}, "initialized": true, "start": 81, "end": 658, "defaultInstanceForType": {"id": 1, "name": "mock_ReservedRange"}}], "reservedNameList": ["get"]}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 991, "serializedSizeAsMessageSet": 667}, "initialized": false, "features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 731, "serializedSizeAsMessageSet": 279}, "initialized": true, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_MessageOptions"}}, "fullName": "their", "file": {"proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 969, "serializedSizeAsMessageSet": 267}, "initialized": true, "enumTypeCount": 222, "extensionCount": 838, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 625, "serializedSizeAsMessageSet": 567}, "initialized": false, "features": {"unknownFields": {"mock": true}, "initialized": false, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {"mock": true}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 748, "serializedSizeAsMessageSet": 586}, "initialized": true, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 962, "serializedSizeAsMessageSet": 358}, "initialized": false, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}, "messageTypes": [{"id": 1, "name": "mock_Descriptor"}, {"id": 1, "name": "mock_Descriptor"}], "enumTypes": [{"index": 564, "proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": true, "reservedRangeList": [{"mock": true}, {"mock": true}], "reservedNameList": ["ever", "hope"], "valueList": [{"mock": true}, {"mock": true}]}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": true, "features": {"unknownFields": {"mock": true}, "initialized": true, "enumType": "mock_string", "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_EnumOptions"}}, "fullName": "future", "file": {"id": 1, "name": "mock_FileDescriptor"}}, {"index": 803, "proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": true, "reservedRangeList": [{"mock": true}, {"mock": true}], "reservedNameList": ["sense", "floor"], "valueList": [{"mock": true}]}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": true, "features": {"unknownFields": {"mock": true}, "initialized": true, "enumType": "mock_string", "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_EnumOptions"}}, "fullName": "line", "file": {"id": 1, "name": "mock_FileDescriptor"}}], "services": [{"index": 72, "proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": false, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"mock": true}}, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceDescriptorProto"}, "parserForType": {}}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": true, "features": {"unknownFields": {"mock": true}, "initialized": true, "enumType": "mock_string", "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}}, "fullName": "call", "file": {"id": 1, "name": "mock_FileDescriptor"}}, {"index": 935, "proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": false, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"mock": true}}, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceDescriptorProto"}, "parserForType": {}}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": true, "features": {"unknownFields": {"mock": true}, "initialized": true, "enumType": "mock_string", "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}}, "fullName": "major", "file": {"id": 1, "name": "mock_FileDescriptor"}}]}}, "allFields": {}, "initializationErrorString": "federal", "unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 371, "serializedSizeAsMessageSet": 140}}, "descriptorForType": {"index": 255, "proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 702, "serializedSizeAsMessageSet": 350}, "initialized": false, "fieldCount": 141, "reservedRangeList": [{"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 423, "serializedSizeAsMessageSet": 527}, "initialized": false, "start": 479, "end": 879, "defaultInstanceForType": {"id": 1, "name": "mock_ReservedRange"}}, {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 597, "serializedSizeAsMessageSet": 514}, "initialized": true, "start": 76, "end": 478, "defaultInstanceForType": {"id": 1, "name": "mock_ReservedRange"}}], "reservedNameList": ["like"]}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 414, "serializedSizeAsMessageSet": 157}, "initialized": false, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 469, "serializedSizeAsMessageSet": 413}, "initialized": true, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_MessageOptions"}}, "fullName": "model", "file": {"proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 172, "serializedSizeAsMessageSet": 920}, "initialized": false, "enumTypeCount": 685, "extensionCount": 943, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 531, "serializedSizeAsMessageSet": 25}, "initialized": false, "features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 162, "serializedSizeAsMessageSet": 56}, "initialized": true, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 842, "serializedSizeAsMessageSet": 853}, "initialized": false, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 675, "serializedSizeAsMessageSet": 230}, "initialized": true, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}, "messageTypes": [{"id": 1, "name": "mock_Descriptor"}], "enumTypes": [{"index": 931, "proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 379, "serializedSizeAsMessageSet": 621}, "initialized": false, "reservedRangeList": [{"unknownFields": {"mock": true}, "initialized": false, "start": 113, "end": 340, "defaultInstanceForType": {"id": 1, "name": "mock_EnumReservedRange"}}], "reservedNameList": ["guy", "west"], "valueList": [{"unknownFields": {"mock": true}, "initialized": false, "options": {"mock": true}, "defaultInstanceForType": {"id": 1, "name": "mock_EnumValueDescriptorProto"}, "parserForType": {"mock": true}}, {"unknownFields": {"mock": true}, "initialized": true, "options": {"mock": true}, "defaultInstanceForType": {"id": 1, "name": "mock_EnumValueDescriptorProto"}, "parserForType": {"mock": true}}]}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 376, "serializedSizeAsMessageSet": 951}, "initialized": true, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": false, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_EnumOptions"}}, "fullName": "image", "file": {"id": 1, "name": "mock_FileDescriptor"}}], "services": [{"index": 180, "proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 889, "serializedSizeAsMessageSet": 350}, "initialized": true, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": true, "features": {"unknownFields": {"mock": true}, "initialized": true, "enumType": "mock_string", "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}}, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceDescriptorProto"}, "parserForType": {}}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 550, "serializedSizeAsMessageSet": 142}, "initialized": true, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": true, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}}, "fullName": "maintain", "file": {"id": 1, "name": "mock_FileDescriptor"}}, {"index": 976, "proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 556, "serializedSizeAsMessageSet": 545}, "initialized": true, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": true, "features": {"unknownFields": {"mock": true}, "initialized": true, "enumType": "mock_string", "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}}, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceDescriptorProto"}, "parserForType": {}}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 630, "serializedSizeAsMessageSet": 840}, "initialized": true, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": false, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}}, "fullName": "pattern", "file": {"id": 1, "name": "mock_FileDescriptor"}}]}, "containingType": {"id": 1, "name": "mock_Descriptor"}, "nestedTypes": [{"id": 1, "name": "mock_Descriptor"}, {"id": 1, "name": "mock_Descriptor"}], "enumTypes": [{"index": 855, "proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 34, "serializedSizeAsMessageSet": 892}, "initialized": false, "reservedRangeList": [{"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 90, "serializedSizeAsMessageSet": 774}, "initialized": true, "start": 346, "end": 851, "defaultInstanceForType": {"id": 1, "name": "mock_EnumReservedRange"}}, {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 361, "serializedSizeAsMessageSet": 203}, "initialized": false, "start": 721, "end": 337, "defaultInstanceForType": {"id": 1, "name": "mock_EnumReservedRange"}}], "reservedNameList": ["environmental", "politics"], "valueList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 407, "serializedSizeAsMessageSet": 108}, "initialized": true, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_EnumValueOptions"}}, "defaultInstanceForType": {"id": 1, "name": "mock_EnumValueDescriptorProto"}, "parserForType": {}}, {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 178, "serializedSizeAsMessageSet": 407}, "initialized": false, "options": {"unknownFields": {"mock": true}, "initialized": false, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_EnumValueOptions"}}, "defaultInstanceForType": {"id": 1, "name": "mock_EnumValueDescriptorProto"}, "parserForType": {}}]}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 676, "serializedSizeAsMessageSet": 657}, "initialized": true, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 769, "serializedSizeAsMessageSet": 357}, "initialized": true, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_EnumOptions"}}, "fullName": "his", "file": {"proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 389, "serializedSizeAsMessageSet": 132}, "initialized": true, "enumTypeCount": 724, "extensionCount": 623, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": false, "features": {"unknownFields": {"mock": true}, "initialized": true, "enumType": "mock_string", "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 443, "serializedSizeAsMessageSet": 888}, "initialized": false, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": false, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}, "messageTypes": [{"id": 1, "name": "mock_Descriptor"}, {"id": 1, "name": "mock_Descriptor"}], "enumTypes": [{"id": 1, "name": "mock_EnumDescriptor"}], "services": [{"index": 573, "proto": {"unknownFields": {"mock": true}, "initialized": false, "options": {"mock": true}, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceDescriptorProto"}, "parserForType": {"mock": true}}, "options": {"unknownFields": {"mock": true}, "initialized": false, "features": {"mock": true}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}}, "fullName": "whatever", "file": {"id": 1, "name": "mock_FileDescriptor"}}, {"index": 794, "proto": {"unknownFields": {"mock": true}, "initialized": false, "options": {"mock": true}, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceDescriptorProto"}, "parserForType": {"mock": true}}, "options": {"unknownFields": {"mock": true}, "initialized": false, "features": {"mock": true}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}}, "fullName": "TV", "file": {"id": 1, "name": "mock_FileDescriptor"}}]}}, {"index": 437, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 936, "serializedSizeAsMessageSet": 262}, "initialized": false, "reservedRangeList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 714, "serializedSizeAsMessageSet": 76}, "initialized": false, "start": 338, "end": 695, "defaultInstanceForType": {"id": 1, "name": "mock_EnumReservedRange"}}, {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 430, "serializedSizeAsMessageSet": 87}, "initialized": false, "start": 599, "end": 929, "defaultInstanceForType": {"id": 1, "name": "mock_EnumReservedRange"}}], "reservedNameList": ["late", "summer"], "valueList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 334, "serializedSizeAsMessageSet": 495}, "initialized": false, "options": {"unknownFields": {"mock": true}, "initialized": false, "features": {"mock": true}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_EnumValueOptions"}}, "defaultInstanceForType": {"id": 1, "name": "mock_EnumValueDescriptorProto"}, "parserForType": {}}]}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 298, "serializedSizeAsMessageSet": 873}, "initialized": true, "features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 947, "serializedSizeAsMessageSet": 684}, "initialized": true, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_EnumOptions"}}, "fullName": "must", "file": {"proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 818, "serializedSizeAsMessageSet": 374}, "initialized": true, "enumTypeCount": 941, "extensionCount": 725, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": false, "features": {"unknownFields": {"mock": true}, "initialized": true, "enumType": "mock_string", "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 284, "serializedSizeAsMessageSet": 963}, "initialized": false, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": false, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}, "messageTypes": [{"id": 1, "name": "mock_Descriptor"}], "enumTypes": [{"id": 1, "name": "mock_EnumDescriptor"}], "services": [{"index": 457, "proto": {"unknownFields": {"mock": true}, "initialized": false, "options": {"mock": true}, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceDescriptorProto"}, "parserForType": {"mock": true}}, "options": {"unknownFields": {"mock": true}, "initialized": false, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}}, "fullName": "news", "file": {"id": 1, "name": "mock_FileDescriptor"}}, {"index": 469, "proto": {"unknownFields": {"mock": true}, "initialized": false, "options": {"mock": true}, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceDescriptorProto"}, "parserForType": {"mock": true}}, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}}, "fullName": "poor", "file": {"id": 1, "name": "mock_FileDescriptor"}}]}}], "fields": [{"index": 948, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 911, "serializedSizeAsMessageSet": 615}, "initialized": false, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 321, "serializedSizeAsMessageSet": 215}, "initialized": true, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": true, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "lazy": false, "deprecated": true}, "label": "LABEL_REPEATED", "defaultInstanceForType": {"id": 1, "name": "mock_FieldDescriptorProto"}}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 143, "serializedSizeAsMessageSet": 940}, "initialized": false, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 913, "serializedSizeAsMessageSet": 403}, "initialized": true, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "lazy": false, "deprecated": true}, "fullName": "care", "jsonName": "fact"}], "extensions": [{"index": 802, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 777, "serializedSizeAsMessageSet": 747}, "initialized": true, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 274, "serializedSizeAsMessageSet": 658}, "initialized": true, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": true, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "lazy": true, "deprecated": false}, "label": "LABEL_REPEATED", "defaultInstanceForType": {"id": 1, "name": "mock_FieldDescriptorProto"}}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 92, "serializedSizeAsMessageSet": 810}, "initialized": false, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 264, "serializedSizeAsMessageSet": 180}, "initialized": true, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "lazy": true, "deprecated": false}, "fullName": "vote", "jsonName": "value"}]}, "allFields": {}, "initializationErrorString": "people"}};
}

module.exports = {
  generateServiceDescriptorProto
};
