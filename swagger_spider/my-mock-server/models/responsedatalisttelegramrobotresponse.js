
// ResponseDataListTelegramRobotResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListTelegramRobotResponse模型的模拟数据
 * @returns {ResponseDataListTelegramRobotResponse} 模拟数据
 */
function generateResponseDataListTelegramRobotResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 638, "maxPage": 487, "total": 3703, "data": [{"id": 221, "status": "1", "name": "compare", "botName": "own", "username": "clearly"}, {"id": 686, "status": "1", "name": "claim", "botName": "perform", "username": "leg"}]}, "result": {"code": "quick_message_id_not_exists", "data": {"curPage": 520, "maxPage": 865, "total": 2777, "data": [{"id": 13, "status": "1", "name": "area", "botName": "customer", "username": "population"}, {"id": 164, "status": "1", "name": "red", "botName": "risk", "username": "box"}, {"id": 537, "status": "1", "name": "president", "botName": "whose", "username": "two"}]}}, "errMessageOnly": "play", "successMessage": "long", "errMessage": "run"};
}

module.exports = {
  generateResponseDataListTelegramRobotResponse
};
