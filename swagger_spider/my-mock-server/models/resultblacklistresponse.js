
// ResultBlackListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultBlackListResponse模型的模拟数据
 * @returns {ResultBlackListResponse} 模拟数据
 */
function generateResultBlackListResponse() {
  return {"code": "store_apply_failed", "data": {"id": 743, "user": {"id": 512, "name": "hard", "url": "enter", "avatar": "respond"}, "limitType": "1", "reason": "responsibility", "startTime": 1753433976, "endTime": 1753433976, "createTime": 1753433976}};
}

module.exports = {
  generateResultBlackListResponse
};
