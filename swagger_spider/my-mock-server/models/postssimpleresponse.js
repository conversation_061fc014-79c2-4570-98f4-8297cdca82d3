
// PostsSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成PostsSimpleResponse模型的模拟数据
 * @returns {PostsSimpleResponse} 模拟数据
 */
function generatePostsSimpleResponse() {
  return {"id": 644, "postsType": 844, "user": {"id": 41, "name": "society", "url": "officer", "avatar": "card"}, "store": {"id": 507, "name": "drive", "url": "tell", "avatar": "as"}, "brandList": [{"id": 841, "name": "resource", "url": "certainly", "avatar": "late"}, {"id": 256, "name": "husband", "url": "home", "avatar": "guy"}], "tagList": [{"id": 532, "name": "blue", "url": "half", "avatar": "protect"}, {"id": 447, "name": "direction", "url": "follow", "avatar": "professor"}, {"id": 540, "name": "add", "url": "pull", "avatar": "party"}], "status": "1", "language": "en_US", "title": "anyone", "money": 587};
}

module.exports = {
  generatePostsSimpleResponse
};
