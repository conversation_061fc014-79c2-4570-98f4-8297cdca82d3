
// ResponseDataListStoreSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListStoreSimpleResponse模型的模拟数据
 * @returns {ResponseDataListStoreSimpleResponse} 模拟数据
 */
function generateResponseDataListStoreSimpleResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 78, "maxPage": 323, "total": 2671, "data": [{"id": 991, "user": {"id": 701, "name": "it", "url": "opportunity", "avatar": "station"}, "status": "1", "language": "en_US", "name": "admit"}, {"id": 66, "user": {"id": 880, "name": "product", "url": "answer", "avatar": "among"}, "status": "1", "language": "en_US", "name": "information"}, {"id": 399, "user": {"id": 386, "name": "recognize", "url": "send", "avatar": "stage"}, "status": "1", "language": "en_US", "name": "account"}]}, "result": {"code": "faq_batch_update_error", "data": {"curPage": 450, "maxPage": 916, "total": 9402, "data": [{"id": 929, "user": {"id": 527, "name": "clear", "url": "deep", "avatar": "practice"}, "status": "1", "language": "en_US", "name": "wide"}, {"id": 943, "user": {"id": 526, "name": "theory", "url": "green", "avatar": "should"}, "status": "1", "language": "en_US", "name": "woman"}, {"id": 293, "user": {"id": 762, "name": "expert", "url": "data", "avatar": "serve"}, "status": "1", "language": "en_US", "name": "whether"}]}}, "errMessageOnly": "tree", "successMessage": "Congress", "errMessage": "anything"};
}

module.exports = {
  generateResponseDataListStoreSimpleResponse
};
