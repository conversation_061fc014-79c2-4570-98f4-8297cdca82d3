
// PaymentSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成PaymentSimpleResponse模型的模拟数据
 * @returns {PaymentSimpleResponse} 模拟数据
 */
function generatePaymentSimpleResponse() {
  return {"id": 16, "status": "1", "paymentType": "point", "paymentName": "movie", "paymentLogo": "next", "dayAmount": 621, "monthAmount": 960, "commissionPercent": 451, "createTime": 1753433976};
}

module.exports = {
  generatePaymentSimpleResponse
};
