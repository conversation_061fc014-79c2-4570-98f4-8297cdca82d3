
// ResponseDataListItemWholesaleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListItemWholesaleResponse模型的模拟数据
 * @returns {ResponseDataListItemWholesaleResponse} 模拟数据
 */
function generateResponseDataListItemWholesaleResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 470, "maxPage": 66, "total": 4401, "data": [{"id": 229, "item": {"id": 721, "name": "attention", "url": "machine", "avatar": "week"}, "status": "1", "quantity": 351, "discount": 35}, {"id": 366, "item": {"id": 842, "name": "notice", "url": "door", "avatar": "mean"}, "status": "1", "quantity": 570, "discount": 923}, {"id": 38, "item": {"id": 127, "name": "kid", "url": "foot", "avatar": "can"}, "status": "1", "quantity": 840, "discount": 203}]}, "result": {"code": "schedule_status_invalid", "data": {"curPage": 740, "maxPage": 350, "total": 3530, "data": [{"id": 154, "item": {"id": 472, "name": "front", "url": "though", "avatar": "simply"}, "status": "1", "quantity": 727, "discount": 728}, {"id": 910, "item": {"id": 549, "name": "treatment", "url": "physical", "avatar": "including"}, "status": "1", "quantity": 255, "discount": 356}]}}, "errMessageOnly": "finish", "successMessage": "individual", "errMessage": "business"};
}

module.exports = {
  generateResponseDataListItemWholesaleResponse
};
