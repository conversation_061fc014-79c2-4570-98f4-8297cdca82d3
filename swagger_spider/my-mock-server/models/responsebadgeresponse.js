
// ResponseBadgeResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseBadgeResponse模型的模拟数据
 * @returns {ResponseBadgeResponse} 模拟数据
 */
function generateResponseBadgeResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 294, "status": "1", "language": "en_US", "badgeType": "1", "badgeName": "information", "customUrl": "federal", "icon": "pressure", "createTime": 1753433976, "sortIndex": "0", "weight": "1"}, "result": {"code": "orders_logs_id_not_exists", "data": {"id": 829, "status": "1", "language": "en_US", "badgeType": "1", "badgeName": "culture", "customUrl": "water", "icon": "particularly", "createTime": 1753433976, "sortIndex": "0", "weight": "1"}}, "errMessageOnly": "type", "successMessage": "five", "errMessage": "five"};
}

module.exports = {
  generateResponseBadgeResponse
};
