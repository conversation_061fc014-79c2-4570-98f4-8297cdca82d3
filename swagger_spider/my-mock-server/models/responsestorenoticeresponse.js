
// ResponseStoreNoticeResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseStoreNoticeResponse模型的模拟数据
 * @returns {ResponseStoreNoticeResponse} 模拟数据
 */
function generateResponseStoreNoticeResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 160, "store": {"id": 999, "name": "western", "url": "door", "avatar": "safe"}, "noticeType": "1", "status": "1", "staffList": "land", "createTime": 1753433976}, "result": {"code": "orders_comment_like_not_null", "data": {"id": 918, "store": {"id": 84, "name": "remember", "url": "recently", "avatar": "blood"}, "noticeType": "1", "status": "1", "staffList": "likely", "createTime": 1753433976}}, "errMessageOnly": "nation", "successMessage": "evidence", "errMessage": "future"};
}

module.exports = {
  generateResponseStoreNoticeResponse
};
