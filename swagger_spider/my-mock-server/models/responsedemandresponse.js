
// ResponseDemandResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDemandResponse模型的模拟数据
 * @returns {ResponseDemandResponse} 模拟数据
 */
function generateResponseDemandResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 131, "user": {"id": 14, "name": "protect", "url": "thousand", "avatar": "positive"}, "brand": {"id": 741, "name": "art", "url": "leg", "avatar": "actually"}, "services": {"id": 597, "name": "daughter", "url": "bed", "avatar": "camera"}, "attrList": [{"attrId": 628, "attrName": "war", "attrValueId": 630, "attrValueName": "really"}], "status": "1", "language": "en_US", "demandName": "culture", "priceFrom": 962, "priceTo": 921}, "result": {"code": "item_aftersales_stat_id_not_exists", "data": {"id": 941, "user": {"id": 554, "name": "whom", "url": "look", "avatar": "well"}, "brand": {"id": 840, "name": "north", "url": "that", "avatar": "person"}, "services": {"id": 344, "name": "allow", "url": "news", "avatar": "beat"}, "attrList": [{"attrId": 843, "attrName": "weight", "attrValueId": 269, "attrValueName": "shake"}, {"attrId": 224, "attrName": "explain", "attrValueId": 151, "attrValueName": "out"}], "status": "1", "language": "en_US", "demandName": "around", "priceFrom": 425, "priceTo": 445}}, "errMessageOnly": "natural", "successMessage": "animal", "errMessage": "tell"};
}

module.exports = {
  generateResponseDemandResponse
};
