
// ResultDataListStoreTagListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListStoreTagListResponse模型的模拟数据
 * @returns {ResultDataListStoreTagListResponse} 模拟数据
 */
function generateResultDataListStoreTagListResponse() {
  return {"code": "brand_lang_name_exists", "data": {"curPage": 288, "maxPage": 285, "total": 8707, "data": [{"id": 907, "store": {"id": 543, "name": "without", "url": "six", "avatar": "Mrs"}, "tag": {"id": 733, "name": "speak", "url": "agent", "avatar": "growth"}, "createTime": 1753433976}, {"id": 754, "store": {"id": 160, "name": "matter", "url": "stock", "avatar": "knowledge"}, "tag": {"id": 891, "name": "response", "url": "special", "avatar": "bad"}, "createTime": 1753433976}, {"id": 721, "store": {"id": 852, "name": "trip", "url": "couple", "avatar": "mission"}, "tag": {"id": 53, "name": "phone", "url": "we", "avatar": "whether"}, "createTime": 1753433976}]}};
}

module.exports = {
  generateResultDataListStoreTagListResponse
};
