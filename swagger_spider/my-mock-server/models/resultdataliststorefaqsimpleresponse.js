
// ResultDataListStoreFaqSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListStoreFaqSimpleResponse模型的模拟数据
 * @returns {ResultDataListStoreFaqSimpleResponse} 模拟数据
 */
function generateResultDataListStoreFaqSimpleResponse() {
  return {"code": "orders_data_id_not_exists", "data": {"curPage": 954, "maxPage": 584, "total": 921, "data": [{"id": 260, "store": {"id": 207, "name": "though", "url": "sense", "avatar": "choose"}, "item": {"id": 337, "name": "receive", "url": "baby", "avatar": "politics"}, "status": "1", "language": "en_US"}, {"id": 808, "store": {"id": 278, "name": "degree", "url": "ball", "avatar": "science"}, "item": {"id": 202, "name": "interest", "url": "figure", "avatar": "produce"}, "status": "1", "language": "en_US"}]}};
}

module.exports = {
  generateResultDataListStoreFaqSimpleResponse
};
