
// ResponseUserWalletResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseUserWalletResponse模型的模拟数据
 * @returns {ResponseUserWalletResponse} 模拟数据
 */
function generateResponseUserWalletResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 198, "status": "1", "walletName": "American", "user": {"id": 369, "name": "especially", "url": "line", "avatar": "prepare"}, "walletType": "1", "country": "practice", "firstName": "lead", "receiveAccount": "use", "createTime": **********, "isDefault": "1"}, "result": {"code": "store_notice_id_not_exists", "data": {"id": 387, "status": "1", "walletName": "first", "user": {"id": 657, "name": "wonder", "url": "act", "avatar": "a"}, "walletType": "1", "country": "without", "firstName": "whatever", "receiveAccount": "amount", "createTime": **********, "isDefault": "1"}}, "errMessageOnly": "ago", "successMessage": "question", "errMessage": "per"};
}

module.exports = {
  generateResponseUserWalletResponse
};
