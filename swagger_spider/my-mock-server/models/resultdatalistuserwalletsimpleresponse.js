
// ResultDataListUserWalletSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListUserWalletSimpleResponse模型的模拟数据
 * @returns {ResultDataListUserWalletSimpleResponse} 模拟数据
 */
function generateResultDataListUserWalletSimpleResponse() {
  return {"code": "demand_pay_deposit_failed", "data": {"curPage": 705, "maxPage": 449, "total": 6766, "data": [{"id": 408, "status": "1", "walletName": "movement", "user": {"id": 168, "name": "up", "url": "by", "avatar": "answer"}, "walletType": "1"}, {"id": 737, "status": "1", "walletName": "decision", "user": {"id": 617, "name": "discuss", "url": "news", "avatar": "practice"}, "walletType": "1"}, {"id": 610, "status": "1", "walletName": "action", "user": {"id": 830, "name": "country", "url": "risk", "avatar": "management"}, "walletType": "1"}]}};
}

module.exports = {
  generateResultDataListUserWalletSimpleResponse
};
