
// ResultListFieldResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultListFieldResponse模型的模拟数据
 * @returns {ResultListFieldResponse} 模拟数据
 */
function generateResultListFieldResponse() {
  return {"code": "quick_message_batch_delete_failure", "data": [{"label": "book", "fieldType": "occur", "fieldName": "believe", "required": false, "defaultValue": "our", "placeholder": "market", "valueList": [{}, {}, {}]}, {"label": "say", "fieldType": "anyone", "fieldName": "course", "required": false, "defaultValue": "establish", "placeholder": "rate", "valueList": [{}]}, {"label": "party", "fieldType": "notice", "fieldName": "game", "required": false, "defaultValue": "account", "placeholder": "help", "valueList": [{}, {}, {}]}]};
}

module.exports = {
  generateResultListFieldResponse
};
