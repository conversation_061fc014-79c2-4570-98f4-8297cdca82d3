
// DataListOrdersCommentRatingResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListOrdersCommentRatingResponse模型的模拟数据
 * @returns {DataListOrdersCommentRatingResponse} 模拟数据
 */
function generateDataListOrdersCommentRatingResponse() {
  return {"curPage": 449, "maxPage": 385, "total": 9779, "data": [{"id": 147, "ordersComment": {"id": 381, "name": "ok", "url": "rock", "avatar": "full"}, "rating": {"id": 882, "name": "phone", "url": "group", "avatar": "sound"}, "score": "1"}]};
}

module.exports = {
  generateDataListOrdersCommentRatingResponse
};
