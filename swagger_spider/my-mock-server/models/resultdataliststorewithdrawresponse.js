
// ResultDataListStoreWithdrawResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListStoreWithdrawResponse模型的模拟数据
 * @returns {ResultDataListStoreWithdrawResponse} 模拟数据
 */
function generateResultDataListStoreWithdrawResponse() {
  return {"code": "schedule_manage_id_not_null", "data": {"curPage": 343, "maxPage": 798, "total": 5132, "data": [{"id": 197, "user": {"id": 605, "name": "responsibility", "url": "group", "avatar": "whole"}, "store": {"id": 810, "name": "Democrat", "url": "building", "avatar": "organization"}, "userWallet": {"id": 418, "name": "near", "url": "personal", "avatar": "share"}, "status": "1"}, {"id": 314, "user": {"id": 230, "name": "outside", "url": "anyone", "avatar": "chair"}, "store": {"id": 585, "name": "no", "url": "already", "avatar": "push"}, "userWallet": {"id": 679, "name": "article", "url": "simply", "avatar": "standard"}, "status": "1"}]}};
}

module.exports = {
  generateResultDataListStoreWithdrawResponse
};
