
// ResultDataListUserKycSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListUserKycSimpleResponse模型的模拟数据
 * @returns {ResultDataListUserKycSimpleResponse} 模拟数据
 */
function generateResultDataListUserKycSimpleResponse() {
  return {"code": "system_config_key_not_exists", "data": {"curPage": 516, "maxPage": 122, "total": 564, "data": [{"id": 22, "status": "1", "user": {"id": 532, "name": "middle", "url": "then", "avatar": "shake"}, "version": 401, "kycType": "1"}]}};
}

module.exports = {
  generateResultDataListUserKycSimpleResponse
};
