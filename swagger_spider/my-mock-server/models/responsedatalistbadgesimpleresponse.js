
// ResponseDataListBadgeSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListBadgeSimpleResponse模型的模拟数据
 * @returns {ResponseDataListBadgeSimpleResponse} 模拟数据
 */
function generateResponseDataListBadgeSimpleResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 739, "maxPage": 863, "total": 6662, "data": [{"id": 710, "status": "1", "language": "en_US", "badgeType": "1", "badgeName": "affect"}]}, "result": {"code": "posts_status_invalid", "data": {"curPage": 546, "maxPage": 425, "total": 8918, "data": [{"id": 778, "status": "1", "language": "en_US", "badgeType": "1", "badgeName": "everything"}, {"id": 490, "status": "1", "language": "en_US", "badgeType": "1", "badgeName": "out"}]}}, "errMessageOnly": "not", "successMessage": "size", "errMessage": "reflect"};
}

module.exports = {
  generateResponseDataListBadgeSimpleResponse
};
