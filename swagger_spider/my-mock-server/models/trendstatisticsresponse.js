
// TrendStatisticsResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成TrendStatisticsResponse模型的模拟数据
 * @returns {TrendStatisticsResponse} 模拟数据
 */
function generateTrendStatisticsResponse() {
  return {"amountGroupChart": {"labels": ["notice", "treatment"], "datasets": [{"name": "talk", "points": [974], "fill": false}, {"name": "area", "points": [271, 519], "fill": true}], "title": "easy", "xaxisLabel": "condition", "yaxisLabel": "customer"}, "customerAveragePurchaseChart": {"labels": ["marriage", "team", "account"], "datasets": [{"name": "time", "points": [198], "fill": false}, {"name": "cell", "points": [602], "fill": true}], "title": "set", "xaxisLabel": "mother", "yaxisLabel": "thus"}, "salesCountGroupChart": {"labels": ["far", "already"], "datasets": [{"name": "determine", "points": [255, 2], "fill": false}], "title": "letter", "xaxisLabel": "himself", "yaxisLabel": "fear"}, "ordersAveragePurchaseChart": {"labels": ["special"], "datasets": [{"name": "factor", "points": [626], "fill": false}, {"name": "then", "points": [32], "fill": true}], "title": "note", "xaxisLabel": "phone", "yaxisLabel": "list"}, "newStoreAndItemCountChart": {"labels": ["lawyer"], "datasets": [{"name": "necessary", "points": [737, 790], "fill": false}, {"name": "none", "points": [213, 332], "fill": false}, {"name": "method", "points": [319], "fill": false}], "title": "explain", "xaxisLabel": "full", "yaxisLabel": "west"}, "newUserCountChart": {"labels": ["hit"], "datasets": [{"name": "dark", "points": [925], "fill": true}, {"name": "per", "points": [750], "fill": false}], "title": "option", "xaxisLabel": "include", "yaxisLabel": "particular"}, "newPostsAndBlogCountChart": {"labels": ["list"], "datasets": [{"name": "attack", "points": [554, 5], "fill": true}], "title": "quite", "xaxisLabel": "although", "yaxisLabel": "and"}, "complaintCountChart": {"labels": ["real", "away", "contain"], "datasets": [{"name": "about", "points": [174], "fill": true}], "title": "large", "xaxisLabel": "picture", "yaxisLabel": "a"}};
}

module.exports = {
  generateTrendStatisticsResponse
};
