
// ResultCampaignResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultCampaignResponse模型的模拟数据
 * @returns {ResultCampaignResponse} 模拟数据
 */
function generateResultCampaignResponse() {
  return {"code": "manage_add_error", "data": {"id": 94, "status": "1", "customUrl": "focus", "coverPic": "according", "createTime": 1753433976, "campaignLangId": 516, "language": "en_US", "title": "believe", "summary": "condition", "seoKeywords": "happy"}};
}

module.exports = {
  generateResultCampaignResponse
};
