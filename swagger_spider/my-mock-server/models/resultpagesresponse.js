
// ResultPagesResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultPagesResponse模型的模拟数据
 * @returns {ResultPagesResponse} 模拟数据
 */
function generateResultPagesResponse() {
  return {"code": "demand_can_not_bid", "data": {"id": 139, "manageId": 191, "pagesType": 461, "status": "1", "language": "en_US", "title": "shoulder", "customUrl": "land", "coverPic": "camera", "createTime": 1753433976, "seoKeywords": "wrong"}};
}

module.exports = {
  generateResultPagesResponse
};
