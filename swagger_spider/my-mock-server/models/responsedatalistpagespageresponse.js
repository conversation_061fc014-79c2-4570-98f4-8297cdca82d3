
// ResponseDataListPagesPageResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListPagesPageResponse模型的模拟数据
 * @returns {ResponseDataListPagesPageResponse} 模拟数据
 */
function generateResponseDataListPagesPageResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 650, "maxPage": 239, "total": 4861, "data": [{"id": 852, "manage": {"id": 908, "name": "ten", "url": "garden", "avatar": "company"}, "pagesType": {"id": 851, "name": "treatment", "url": "summer", "avatar": "evening"}, "statusName": "method", "language": "en_US"}, {"id": 968, "manage": {"id": 741, "name": "able", "url": "guy", "avatar": "than"}, "pagesType": {"id": 374, "name": "behind", "url": "plan", "avatar": "save"}, "statusName": "character", "language": "en_US"}]}, "result": {"code": "user_not_exits", "data": {"curPage": 38, "maxPage": 419, "total": 5993, "data": [{"id": 623, "manage": {"id": 472, "name": "fish", "url": "once", "avatar": "about"}, "pagesType": {"id": 545, "name": "picture", "url": "parent", "avatar": "together"}, "statusName": "decade", "language": "en_US"}, {"id": 571, "manage": {"id": 142, "name": "thousand", "url": "change", "avatar": "together"}, "pagesType": {"id": 778, "name": "article", "url": "same", "avatar": "product"}, "statusName": "time", "language": "en_US"}]}}, "errMessageOnly": "indicate", "successMessage": "assume", "errMessage": "simple"};
}

module.exports = {
  generateResponseDataListPagesPageResponse
};
