
// ResponseDataListAftersalesResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListAftersalesResponse模型的模拟数据
 * @returns {ResponseDataListAftersalesResponse} 模拟数据
 */
function generateResponseDataListAftersalesResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 201, "maxPage": 571, "total": 4574, "data": [{"id": 119, "brand": {"id": 83, "name": "must", "url": "view", "avatar": "mother"}, "aftersalesType": {"id": 152, "name": "moment", "url": "example", "avatar": "price"}, "status": "1", "statusName": "vote"}, {"id": 855, "brand": {"id": 118, "name": "production", "url": "son", "avatar": "good"}, "aftersalesType": {"id": 236, "name": "series", "url": "security", "avatar": "get"}, "status": "1", "statusName": "fill"}, {"id": 44, "brand": {"id": 897, "name": "site", "url": "process", "avatar": "traditional"}, "aftersalesType": {"id": 390, "name": "government", "url": "stuff", "avatar": "receive"}, "status": "1", "statusName": "scientist"}]}, "result": {"code": "login_fail", "data": {"curPage": 80, "maxPage": 632, "total": 7442, "data": [{"id": 285, "brand": {"id": 322, "name": "look", "url": "suffer", "avatar": "fight"}, "aftersalesType": {"id": 629, "name": "school", "url": "source", "avatar": "cup"}, "status": "1", "statusName": "sound"}, {"id": 954, "brand": {"id": 523, "name": "one", "url": "ago", "avatar": "major"}, "aftersalesType": {"id": 674, "name": "simply", "url": "part", "avatar": "candidate"}, "status": "1", "statusName": "push"}, {"id": 901, "brand": {"id": 383, "name": "tax", "url": "bar", "avatar": "history"}, "aftersalesType": {"id": 57, "name": "place", "url": "person", "avatar": "issue"}, "status": "1", "statusName": "catch"}]}}, "errMessageOnly": "wear", "successMessage": "board", "errMessage": "professor"};
}

module.exports = {
  generateResponseDataListAftersalesResponse
};
