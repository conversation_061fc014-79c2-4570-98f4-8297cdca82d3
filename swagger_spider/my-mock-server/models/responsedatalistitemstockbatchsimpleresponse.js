
// ResponseDataListItemStockBatchSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListItemStockBatchSimpleResponse模型的模拟数据
 * @returns {ResponseDataListItemStockBatchSimpleResponse} 模拟数据
 */
function generateResponseDataListItemStockBatchSimpleResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 427, "maxPage": 793, "total": 3392, "data": [{"id": 581, "store": {"id": 51, "name": "effect", "url": "where", "avatar": "dream"}, "item": {"id": 121, "name": "real", "url": "head", "avatar": "difficult"}, "batchCount": 885, "salesCount": 526}, {"id": 882, "store": {"id": 576, "name": "state", "url": "agree", "avatar": "necessary"}, "item": {"id": 434, "name": "strong", "url": "task", "avatar": "certain"}, "batchCount": 899, "salesCount": 946}, {"id": 333, "store": {"id": 378, "name": "measure", "url": "tough", "avatar": "to"}, "item": {"id": 70, "name": "brother", "url": "always", "avatar": "specific"}, "batchCount": 881, "salesCount": 133}]}, "result": {"code": "item_tag_list_id_not_exists", "data": {"curPage": 4, "maxPage": 164, "total": 6485, "data": [{"id": 203, "store": {"id": 72, "name": "imagine", "url": "learn", "avatar": "hair"}, "item": {"id": 436, "name": "these", "url": "new", "avatar": "pay"}, "batchCount": 312, "salesCount": 656}, {"id": 440, "store": {"id": 243, "name": "rock", "url": "two", "avatar": "deal"}, "item": {"id": 164, "name": "speak", "url": "student", "avatar": "article"}, "batchCount": 993, "salesCount": 846}]}}, "errMessageOnly": "professor", "successMessage": "only", "errMessage": "surface"};
}

module.exports = {
  generateResponseDataListItemStockBatchSimpleResponse
};
