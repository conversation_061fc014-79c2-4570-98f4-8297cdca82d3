
// ResultTagResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultTagResponse模型的模拟数据
 * @returns {ResultTagResponse} 模拟数据
 */
function generateResultTagResponse() {
  return {"code": "system_error", "data": {"id": 970, "status": "1", "sortIndex": "0", "useType": "1", "language": "en_US", "tagName": "real", "customUrl": "six", "createTime": 1753433976, "seoKeywords": "avoid", "seoDescription": "safe"}};
}

module.exports = {
  generateResultTagResponse
};
