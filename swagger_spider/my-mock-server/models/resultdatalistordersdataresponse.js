
// ResultDataListOrdersDataResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListOrdersDataResponse模型的模拟数据
 * @returns {ResultDataListOrdersDataResponse} 模拟数据
 */
function generateResultDataListOrdersDataResponse() {
  return {"code": "badge_get_list_id_not_exists", "data": {"curPage": 317, "maxPage": 107, "total": 4633, "data": [{"id": 746, "ordersId": 350, "deliveryTime": 1753433975, "dataType": "1", "downloadTime": 1753433975}, {"id": 717, "ordersId": 826, "deliveryTime": 1753433975, "dataType": "1", "downloadTime": 1753433975}]}};
}

module.exports = {
  generateResultDataListOrdersDataResponse
};
