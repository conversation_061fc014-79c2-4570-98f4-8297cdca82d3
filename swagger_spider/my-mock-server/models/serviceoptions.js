
// ServiceOptions 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ServiceOptions模型的模拟数据
 * @returns {ServiceOptions} 模拟数据
 */
function generateServiceOptions() {
  return {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 317, "serializedSizeAsMessageSet": 165, "empty": false}, "initialized": true, "features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 949, "serializedSizeAsMessageSet": 162, "empty": false}, "initialized": false, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}, "serializedSize": 212, "fieldPresence": "EXPLICIT", "messageEncoding": "DELIMITED", "utf8Validation": "VERIFY", "repeatedFieldEncoding": "PACKED"}, "deprecated": false, "defaultInstanceForType": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 776, "serializedSizeAsMessageSet": 974, "empty": true}, "initialized": true, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 206, "serializedSizeAsMessageSet": 755}, "initialized": false, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}, "serializedSize": 992, "fieldPresence": "FIELD_PRESENCE_UNKNOWN", "messageEncoding": "LENGTH_PREFIXED", "utf8Validation": "VERIFY", "repeatedFieldEncoding": "PACKED"}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}, "parserForType": {}, "serializedSize": 91, "uninterpretedOptionOrBuilderList": [{"doubleValue": 907.19, "stringValue": {"validUtf8": true, "empty": true}, "nameList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 441, "serializedSizeAsMessageSet": 545}, "initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 619}, {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 71, "serializedSizeAsMessageSet": 829}, "initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 219}], "identifierValue": "scene", "positiveIntValue": 5123}, {"doubleValue": 342.14, "stringValue": {"validUtf8": false, "empty": true}, "nameList": [{"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 645, "serializedSizeAsMessageSet": 72}, "initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 920}, {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 349, "serializedSizeAsMessageSet": 494}, "initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 539}], "identifierValue": "none", "positiveIntValue": 3255}], "uninterpretedOptionCount": 339, "uninterpretedOptionList": [{"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 698, "serializedSizeAsMessageSet": 659}, "initialized": false, "doubleValue": 145.52, "defaultInstanceForType": {"id": 1, "name": "mock_UninterpretedOption"}, "parserForType": {}}, {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 387, "serializedSizeAsMessageSet": 547}, "initialized": true, "doubleValue": 778.28, "defaultInstanceForType": {"id": 1, "name": "mock_UninterpretedOption"}, "parserForType": {}}]}, "parserForType": {}, "serializedSize": 545, "uninterpretedOptionOrBuilderList": [{"doubleValue": 789.8, "stringValue": {"validUtf8": false, "empty": true}, "nameList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 50, "serializedSizeAsMessageSet": 180}, "initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 588}, {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 168, "serializedSizeAsMessageSet": 730}, "initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 596}, {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 728, "serializedSizeAsMessageSet": 724}, "initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 827}], "identifierValue": "history", "positiveIntValue": 4670, "negativeIntValue": 6787, "aggregateValue": "movie", "nameOrBuilderList": [{"namePartBytes": {"validUtf8": false, "empty": false}, "isExtension": true, "namePart": "south", "descriptorForType": {"index": 934, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 521, "serializedSizeAsMessageSet": 369}, "initialized": false, "fieldCount": 181, "reservedRangeList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": false, "start": 593, "end": 677, "defaultInstanceForType": {"id": 1, "name": "mock_ReservedRange"}}], "reservedNameList": ["defense", "visit"]}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 115, "serializedSizeAsMessageSet": 687}, "initialized": true, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 991, "serializedSizeAsMessageSet": 938}, "initialized": true, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_MessageOptions"}}, "fullName": "will", "file": {"proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 191, "serializedSizeAsMessageSet": 74}, "initialized": false, "enumTypeCount": 396, "extensionCount": 339, "options": {"unknownFields": {"mock": true}, "initialized": false, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 189, "serializedSizeAsMessageSet": 960}, "initialized": false, "features": {"unknownFields": {"mock": true}, "initialized": true, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {"mock": true}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}, "messageTypes": [{"id": 1, "name": "mock_Descriptor"}], "enumTypes": [{"index": 547, "proto": {"unknownFields": {"mock": true}, "initialized": true, "reservedRangeList": [], "reservedNameList": [], "valueList": []}, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"mock": true}}, "fullName": "miss", "file": {"id": 1, "name": "mock_FileDescriptor"}}], "services": [{"index": 682, "proto": {"unknownFields": {"mock": true}, "initialized": true, "options": {"mock": true}, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"mock": true}}, "fullName": "may", "file": {"id": 1, "name": "mock_FileDescriptor"}}, {"index": 269, "proto": {"unknownFields": {"mock": true}, "initialized": true, "options": {"mock": true}, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"mock": true}}, "fullName": "though", "file": {"id": 1, "name": "mock_FileDescriptor"}}]}}, "defaultInstanceForType": {"parserForType": {}, "serializedSize": 655, "initialized": false, "defaultInstanceForType": {"parserForType": {}, "serializedSize": 8, "initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_MessageLite"}}, "descriptorForType": {"index": 730, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 8, "serializedSizeAsMessageSet": 580}, "initialized": true, "fieldCount": 299, "reservedRangeList": [{"unknownFields": {"mock": true}, "initialized": true, "start": 1, "end": 1, "defaultInstanceForType": {"mock": true}}], "reservedNameList": ["accept", "charge"]}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 965, "serializedSizeAsMessageSet": 963}, "initialized": true, "features": {"unknownFields": {"mock": true}, "initialized": true, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {"mock": true}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_MessageOptions"}}, "fullName": "audience", "file": {"proto": {"unknownFields": {"mock": true}, "initialized": true, "enumTypeCount": 354, "extensionCount": 675, "options": {"mock": true}}, "options": {"unknownFields": {"mock": true}, "initialized": false, "features": {"mock": true}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}, "messageTypes": [{"id": 1, "name": "mock_Descriptor"}, {"id": 1, "name": "mock_Descriptor"}], "enumTypes": [{"index": 1, "proto": {"mock": true}, "options": {"mock": true}, "fullName": "mock_string", "file": {"mock": true}}, {"index": 1, "proto": {"mock": true}, "options": {"mock": true}, "fullName": "mock_string", "file": {"mock": true}}], "services": [{"index": 1, "proto": {"mock": true}, "options": {"mock": true}, "fullName": "mock_string", "file": {"mock": true}}, {"index": 1, "proto": {"mock": true}, "options": {"mock": true}, "fullName": "mock_string", "file": {"mock": true}}]}}}}], "identifierValueBytes": {"validUtf8": true, "empty": false}, "aggregateValueBytes": {"validUtf8": true, "empty": false}}, {"doubleValue": 435.01, "stringValue": {"validUtf8": true, "empty": false}, "nameList": [{"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 876, "serializedSizeAsMessageSet": 700}, "initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 492}, {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 365, "serializedSizeAsMessageSet": 138}, "initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 737}], "identifierValue": "wide", "positiveIntValue": 1425, "negativeIntValue": 1574, "aggregateValue": "door", "nameOrBuilderList": [{"namePartBytes": {"validUtf8": false, "empty": false}, "isExtension": true, "namePart": "contain", "descriptorForType": {"index": 937, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 711, "serializedSizeAsMessageSet": 490}, "initialized": false, "fieldCount": 824, "reservedRangeList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": false, "start": 926, "end": 747, "defaultInstanceForType": {"id": 1, "name": "mock_ReservedRange"}}, {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": false, "start": 658, "end": 111, "defaultInstanceForType": {"id": 1, "name": "mock_ReservedRange"}}], "reservedNameList": ["spring", "around"]}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 202, "serializedSizeAsMessageSet": 115}, "initialized": true, "features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 497, "serializedSizeAsMessageSet": 233}, "initialized": true, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_MessageOptions"}}, "fullName": "work", "file": {"proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 872, "serializedSizeAsMessageSet": 488}, "initialized": true, "enumTypeCount": 631, "extensionCount": 771, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 666, "serializedSizeAsMessageSet": 864}, "initialized": false, "features": {"unknownFields": {"mock": true}, "initialized": false, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {"mock": true}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}, "messageTypes": [{"id": 1, "name": "mock_Descriptor"}], "enumTypes": [{"index": 975, "proto": {"unknownFields": {"mock": true}, "initialized": true, "reservedRangeList": [], "reservedNameList": [], "valueList": []}, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"mock": true}}, "fullName": "world", "file": {"id": 1, "name": "mock_FileDescriptor"}}, {"index": 168, "proto": {"unknownFields": {"mock": true}, "initialized": true, "reservedRangeList": [], "reservedNameList": [], "valueList": []}, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"mock": true}}, "fullName": "our", "file": {"id": 1, "name": "mock_FileDescriptor"}}], "services": [{"index": 900, "proto": {"unknownFields": {"mock": true}, "initialized": true, "options": {"mock": true}, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"mock": true}}, "fullName": "officer", "file": {"id": 1, "name": "mock_FileDescriptor"}}, {"index": 677, "proto": {"unknownFields": {"mock": true}, "initialized": true, "options": {"mock": true}, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"mock": true}}, "fullName": "during", "file": {"id": 1, "name": "mock_FileDescriptor"}}]}}, "defaultInstanceForType": {"parserForType": {}, "serializedSize": 740, "initialized": false, "defaultInstanceForType": {"parserForType": {}, "serializedSize": 422, "initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_MessageLite"}}, "descriptorForType": {"index": 406, "proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 517, "serializedSizeAsMessageSet": 222}, "initialized": true, "fieldCount": 42, "reservedRangeList": [{"unknownFields": {"mock": true}, "initialized": true, "start": 1, "end": 1, "defaultInstanceForType": {"mock": true}}, {"unknownFields": {"mock": true}, "initialized": true, "start": 1, "end": 1, "defaultInstanceForType": {"mock": true}}], "reservedNameList": ["board", "action"]}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 950, "serializedSizeAsMessageSet": 288}, "initialized": false, "features": {"unknownFields": {"mock": true}, "initialized": true, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {"mock": true}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_MessageOptions"}}, "fullName": "listen", "file": {"proto": {"unknownFields": {"mock": true}, "initialized": false, "enumTypeCount": 873, "extensionCount": 538, "options": {"mock": true}}, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}, "messageTypes": [{"id": 1, "name": "mock_Descriptor"}, {"id": 1, "name": "mock_Descriptor"}], "enumTypes": [{"index": 1, "proto": {"mock": true}, "options": {"mock": true}, "fullName": "mock_string", "file": {"mock": true}}], "services": [{"index": 1, "proto": {"mock": true}, "options": {"mock": true}, "fullName": "mock_string", "file": {"mock": true}}, {"index": 1, "proto": {"mock": true}, "options": {"mock": true}, "fullName": "mock_string", "file": {"mock": true}}]}}}}, {"namePartBytes": {"validUtf8": false, "empty": false}, "isExtension": true, "namePart": "sound", "descriptorForType": {"index": 963, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 224, "serializedSizeAsMessageSet": 366}, "initialized": false, "fieldCount": 639, "reservedRangeList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": true, "start": 383, "end": 984, "defaultInstanceForType": {"id": 1, "name": "mock_ReservedRange"}}], "reservedNameList": ["respond"]}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 522, "serializedSizeAsMessageSet": 678}, "initialized": false, "features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 121, "serializedSizeAsMessageSet": 374}, "initialized": true, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_MessageOptions"}}, "fullName": "picture", "file": {"proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 648, "serializedSizeAsMessageSet": 510}, "initialized": true, "enumTypeCount": 71, "extensionCount": 145, "options": {"unknownFields": {"mock": true}, "initialized": false, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 925, "serializedSizeAsMessageSet": 549}, "initialized": true, "features": {"unknownFields": {"mock": true}, "initialized": true, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {"mock": true}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}, "messageTypes": [{"id": 1, "name": "mock_Descriptor"}], "enumTypes": [{"index": 308, "proto": {"unknownFields": {"mock": true}, "initialized": true, "reservedRangeList": [], "reservedNameList": [], "valueList": []}, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"mock": true}}, "fullName": "walk", "file": {"id": 1, "name": "mock_FileDescriptor"}}, {"index": 103, "proto": {"unknownFields": {"mock": true}, "initialized": true, "reservedRangeList": [], "reservedNameList": [], "valueList": []}, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"mock": true}}, "fullName": "nation", "file": {"id": 1, "name": "mock_FileDescriptor"}}], "services": [{"index": 733, "proto": {"unknownFields": {"mock": true}, "initialized": true, "options": {"mock": true}, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"mock": true}}, "fullName": "contain", "file": {"id": 1, "name": "mock_FileDescriptor"}}, {"index": 689, "proto": {"unknownFields": {"mock": true}, "initialized": true, "options": {"mock": true}, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"mock": true}}, "fullName": "woman", "file": {"id": 1, "name": "mock_FileDescriptor"}}]}}, "defaultInstanceForType": {"parserForType": {}, "serializedSize": 626, "initialized": true, "defaultInstanceForType": {"parserForType": {}, "serializedSize": 318, "initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_MessageLite"}}, "descriptorForType": {"index": 596, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 844, "serializedSizeAsMessageSet": 13}, "initialized": true, "fieldCount": 648, "reservedRangeList": [{"unknownFields": {"mock": true}, "initialized": true, "start": 1, "end": 1, "defaultInstanceForType": {"mock": true}}, {"unknownFields": {"mock": true}, "initialized": true, "start": 1, "end": 1, "defaultInstanceForType": {"mock": true}}], "reservedNameList": ["more"]}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 158, "serializedSizeAsMessageSet": 365}, "initialized": true, "features": {"unknownFields": {"mock": true}, "initialized": false, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {"mock": true}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_MessageOptions"}}, "fullName": "red", "file": {"proto": {"unknownFields": {"mock": true}, "initialized": false, "enumTypeCount": 574, "extensionCount": 458, "options": {"mock": true}}, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}, "messageTypes": [{"id": 1, "name": "mock_Descriptor"}], "enumTypes": [{"index": 1, "proto": {"mock": true}, "options": {"mock": true}, "fullName": "mock_string", "file": {"mock": true}}, {"index": 1, "proto": {"mock": true}, "options": {"mock": true}, "fullName": "mock_string", "file": {"mock": true}}], "services": [{"index": 1, "proto": {"mock": true}, "options": {"mock": true}, "fullName": "mock_string", "file": {"mock": true}}, {"index": 1, "proto": {"mock": true}, "options": {"mock": true}, "fullName": "mock_string", "file": {"mock": true}}]}}}}], "identifierValueBytes": {"validUtf8": false, "empty": false}, "aggregateValueBytes": {"validUtf8": true, "empty": false}}, {"doubleValue": 17.53, "stringValue": {"validUtf8": true, "empty": true}, "nameList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 22, "serializedSizeAsMessageSet": 779}, "initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 390}, {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 585, "serializedSizeAsMessageSet": 71}, "initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 236}, {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 950, "serializedSizeAsMessageSet": 725}, "initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 201}], "identifierValue": "notice", "positiveIntValue": 8934, "negativeIntValue": 4819, "aggregateValue": "family", "nameOrBuilderList": [{"namePartBytes": {"validUtf8": true, "empty": false}, "isExtension": false, "namePart": "star", "descriptorForType": {"index": 739, "proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 472, "serializedSizeAsMessageSet": 186}, "initialized": true, "fieldCount": 174, "reservedRangeList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": false, "start": 518, "end": 886, "defaultInstanceForType": {"id": 1, "name": "mock_ReservedRange"}}, {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": true, "start": 970, "end": 571, "defaultInstanceForType": {"id": 1, "name": "mock_ReservedRange"}}], "reservedNameList": ["card", "sure"]}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 674, "serializedSizeAsMessageSet": 200}, "initialized": true, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 193, "serializedSizeAsMessageSet": 593}, "initialized": true, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_MessageOptions"}}, "fullName": "discussion", "file": {"proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 848, "serializedSizeAsMessageSet": 385}, "initialized": false, "enumTypeCount": 969, "extensionCount": 474, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 451, "serializedSizeAsMessageSet": 798}, "initialized": false, "features": {"unknownFields": {"mock": true}, "initialized": true, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {"mock": true}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}, "messageTypes": [{"id": 1, "name": "mock_Descriptor"}, {"id": 1, "name": "mock_Descriptor"}], "enumTypes": [{"index": 783, "proto": {"unknownFields": {"mock": true}, "initialized": true, "reservedRangeList": [], "reservedNameList": [], "valueList": []}, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"mock": true}}, "fullName": "more", "file": {"id": 1, "name": "mock_FileDescriptor"}}, {"index": 860, "proto": {"unknownFields": {"mock": true}, "initialized": true, "reservedRangeList": [], "reservedNameList": [], "valueList": []}, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"mock": true}}, "fullName": "glass", "file": {"id": 1, "name": "mock_FileDescriptor"}}], "services": [{"index": 87, "proto": {"unknownFields": {"mock": true}, "initialized": true, "options": {"mock": true}, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"mock": true}}, "fullName": "billion", "file": {"id": 1, "name": "mock_FileDescriptor"}}, {"index": 652, "proto": {"unknownFields": {"mock": true}, "initialized": true, "options": {"mock": true}, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"mock": true}}, "fullName": "table", "file": {"id": 1, "name": "mock_FileDescriptor"}}]}}, "defaultInstanceForType": {"parserForType": {}, "serializedSize": 489, "initialized": false, "defaultInstanceForType": {"parserForType": {}, "serializedSize": 396, "initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_MessageLite"}}, "descriptorForType": {"index": 850, "proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 90, "serializedSizeAsMessageSet": 588}, "initialized": true, "fieldCount": 784, "reservedRangeList": [{"unknownFields": {"mock": true}, "initialized": true, "start": 1, "end": 1, "defaultInstanceForType": {"mock": true}}], "reservedNameList": ["low", "design"]}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 298, "serializedSizeAsMessageSet": 421}, "initialized": true, "features": {"unknownFields": {"mock": true}, "initialized": true, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {"mock": true}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_MessageOptions"}}, "fullName": "lot", "file": {"proto": {"unknownFields": {"mock": true}, "initialized": false, "enumTypeCount": 162, "extensionCount": 21, "options": {"mock": true}}, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}, "messageTypes": [{"id": 1, "name": "mock_Descriptor"}, {"id": 1, "name": "mock_Descriptor"}], "enumTypes": [{"index": 1, "proto": {"mock": true}, "options": {"mock": true}, "fullName": "mock_string", "file": {"mock": true}}, {"index": 1, "proto": {"mock": true}, "options": {"mock": true}, "fullName": "mock_string", "file": {"mock": true}}], "services": [{"index": 1, "proto": {"mock": true}, "options": {"mock": true}, "fullName": "mock_string", "file": {"mock": true}}, {"index": 1, "proto": {"mock": true}, "options": {"mock": true}, "fullName": "mock_string", "file": {"mock": true}}]}}}}, {"namePartBytes": {"validUtf8": true, "empty": false}, "isExtension": true, "namePart": "race", "descriptorForType": {"index": 613, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 181, "serializedSizeAsMessageSet": 749}, "initialized": false, "fieldCount": 147, "reservedRangeList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": false, "start": 72, "end": 663, "defaultInstanceForType": {"id": 1, "name": "mock_ReservedRange"}}, {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": true, "start": 146, "end": 17, "defaultInstanceForType": {"id": 1, "name": "mock_ReservedRange"}}], "reservedNameList": ["compare", "none"]}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 229, "serializedSizeAsMessageSet": 791}, "initialized": true, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 540, "serializedSizeAsMessageSet": 482}, "initialized": false, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_MessageOptions"}}, "fullName": "remember", "file": {"proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 162, "serializedSizeAsMessageSet": 536}, "initialized": true, "enumTypeCount": 964, "extensionCount": 26, "options": {"unknownFields": {"mock": true}, "initialized": false, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 947, "serializedSizeAsMessageSet": 84}, "initialized": true, "features": {"unknownFields": {"mock": true}, "initialized": true, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {"mock": true}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}, "messageTypes": [{"id": 1, "name": "mock_Descriptor"}], "enumTypes": [{"index": 833, "proto": {"unknownFields": {"mock": true}, "initialized": true, "reservedRangeList": [], "reservedNameList": [], "valueList": []}, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"mock": true}}, "fullName": "end", "file": {"id": 1, "name": "mock_FileDescriptor"}}], "services": [{"index": 220, "proto": {"unknownFields": {"mock": true}, "initialized": true, "options": {"mock": true}, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"mock": true}}, "fullName": "consider", "file": {"id": 1, "name": "mock_FileDescriptor"}}, {"index": 762, "proto": {"unknownFields": {"mock": true}, "initialized": true, "options": {"mock": true}, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"mock": true}}, "fullName": "century", "file": {"id": 1, "name": "mock_FileDescriptor"}}]}}, "defaultInstanceForType": {"parserForType": {}, "serializedSize": 616, "initialized": false, "defaultInstanceForType": {"parserForType": {}, "serializedSize": 392, "initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_MessageLite"}}, "descriptorForType": {"index": 125, "proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 991, "serializedSizeAsMessageSet": 36}, "initialized": false, "fieldCount": 346, "reservedRangeList": [{"unknownFields": {"mock": true}, "initialized": true, "start": 1, "end": 1, "defaultInstanceForType": {"mock": true}}], "reservedNameList": ["city", "two"]}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 926, "serializedSizeAsMessageSet": 47}, "initialized": false, "features": {"unknownFields": {"mock": true}, "initialized": false, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {"mock": true}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_MessageOptions"}}, "fullName": "me", "file": {"proto": {"unknownFields": {"mock": true}, "initialized": true, "enumTypeCount": 628, "extensionCount": 812, "options": {"mock": true}}, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}, "messageTypes": [{"id": 1, "name": "mock_Descriptor"}, {"id": 1, "name": "mock_Descriptor"}], "enumTypes": [{"index": 1, "proto": {"mock": true}, "options": {"mock": true}, "fullName": "mock_string", "file": {"mock": true}}, {"index": 1, "proto": {"mock": true}, "options": {"mock": true}, "fullName": "mock_string", "file": {"mock": true}}], "services": [{"index": 1, "proto": {"mock": true}, "options": {"mock": true}, "fullName": "mock_string", "file": {"mock": true}}, {"index": 1, "proto": {"mock": true}, "options": {"mock": true}, "fullName": "mock_string", "file": {"mock": true}}]}}}}], "identifierValueBytes": {"validUtf8": true, "empty": true}, "aggregateValueBytes": {"validUtf8": false, "empty": false}}], "uninterpretedOptionCount": 896, "uninterpretedOptionList": [{"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 576, "serializedSizeAsMessageSet": 668}, "initialized": false, "doubleValue": 368.53, "defaultInstanceForType": {"id": 1, "name": "mock_UninterpretedOption"}, "parserForType": {}, "stringValue": {"validUtf8": false, "empty": false}, "serializedSize": 359, "nameList": [{"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 429, "serializedSizeAsMessageSet": 612}, "initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 701}, {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 747, "serializedSizeAsMessageSet": 821}, "initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 510}], "identifierValue": "bed", "positiveIntValue": 9610}, {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 391, "serializedSizeAsMessageSet": 813}, "initialized": true, "doubleValue": 375.81, "defaultInstanceForType": {"id": 1, "name": "mock_UninterpretedOption"}, "parserForType": {}, "stringValue": {"validUtf8": true, "empty": true}, "serializedSize": 981, "nameList": [{"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 181, "serializedSizeAsMessageSet": 650}, "initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 977}, {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 907, "serializedSizeAsMessageSet": 964}, "initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 32}], "identifierValue": "analysis", "positiveIntValue": 682}, {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 700, "serializedSizeAsMessageSet": 562}, "initialized": true, "doubleValue": 727.3, "defaultInstanceForType": {"id": 1, "name": "mock_UninterpretedOption"}, "parserForType": {}, "stringValue": {"validUtf8": true, "empty": true}, "serializedSize": 886, "nameList": [{"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 940, "serializedSizeAsMessageSet": 704}, "initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 709}], "identifierValue": "base", "positiveIntValue": 9074}]};
}

module.exports = {
  generateServiceOptions
};
