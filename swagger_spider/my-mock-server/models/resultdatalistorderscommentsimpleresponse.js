
// ResultDataListOrdersCommentSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListOrdersCommentSimpleResponse模型的模拟数据
 * @returns {ResultDataListOrdersCommentSimpleResponse} 模拟数据
 */
function generateResultDataListOrdersCommentSimpleResponse() {
  return {"code": "store_withdraw_can_not_approve", "data": {"curPage": 687, "maxPage": 295, "total": 370, "data": [{"id": 226, "user": {"id": 458, "name": "thing", "url": "forget", "avatar": "use"}, "item": {"id": 699, "name": "soon", "url": "serve", "avatar": "reason"}, "ordersId": 365, "store": {"id": 169, "name": "media", "url": "need", "avatar": "Democrat"}}]}};
}

module.exports = {
  generateResultDataListOrdersCommentSimpleResponse
};
