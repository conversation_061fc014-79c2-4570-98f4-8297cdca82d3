
// ResultTaskConfigResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultTaskConfigResponse模型的模拟数据
 * @returns {ResultTaskConfigResponse} 模拟数据
 */
function generateResultTaskConfigResponse() {
  return {"code": "user_bind_2fa_code_expired", "data": {"id": 533, "status": "1", "taskName": "society", "mark": "past", "taskClass": "town", "createTime": 1753433976, "updateTime": 1753433976}};
}

module.exports = {
  generateResultTaskConfigResponse
};
