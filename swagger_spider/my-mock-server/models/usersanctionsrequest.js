
// UserSanctionsRequest 模型
// 由SwaggerCrawler自动生成

/**
 * 生成UserSanctionsRequest模型的模拟数据
 * @returns {UserSanctionsRequest} 模拟数据
 */
function generateUserSanctionsRequest() {
  return {"violationsEventId": 692, "userId": 326, "status": "ENABLED", "sanctionType": "DEMAND", "actionClass": "suggest", "sanctionData": {}, "sanctionStart": 916, "sanctionEnd": 383, "mark": "yes"};
}

module.exports = {
  generateUserSanctionsRequest
};
