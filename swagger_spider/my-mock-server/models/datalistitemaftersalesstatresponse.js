
// DataListItemAftersalesStatResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListItemAftersalesStatResponse模型的模拟数据
 * @returns {DataListItemAftersalesStatResponse} 模拟数据
 */
function generateDataListItemAftersalesStatResponse() {
  return {"curPage": 23, "maxPage": 789, "total": 7200, "data": [{"id": 94, "aftersalesId": 480, "store": {"id": 507, "name": "type", "url": "like", "avatar": "police"}, "item": {"id": 44, "name": "the", "url": "weight", "avatar": "western"}, "hitCount": 252}, {"id": 75, "aftersalesId": 580, "store": {"id": 541, "name": "cold", "url": "significant", "avatar": "kid"}, "item": {"id": 506, "name": "Mr", "url": "very", "avatar": "world"}, "hitCount": 501}]};
}

module.exports = {
  generateDataListItemAftersalesStatResponse
};
