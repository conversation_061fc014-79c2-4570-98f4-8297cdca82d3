
// ResponseDataListItemShowResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListItemShowResponse模型的模拟数据
 * @returns {ResponseDataListItemShowResponse} 模拟数据
 */
function generateResponseDataListItemShowResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 918, "maxPage": 603, "total": 6365, "data": [{"id": 27, "item": {"id": 324, "name": "center", "url": "whose", "avatar": "carry"}, "status": "1", "sortIndex": 723, "version": 449}, {"id": 15, "item": {"id": 599, "name": "modern", "url": "less", "avatar": "management"}, "status": "1", "sortIndex": 922, "version": 356}]}, "result": {"code": "brand_batch_update_parent_id_error", "data": {"curPage": 422, "maxPage": 589, "total": 2993, "data": [{"id": 143, "item": {"id": 130, "name": "mouth", "url": "suddenly", "avatar": "pattern"}, "status": "1", "sortIndex": 466, "version": 472}, {"id": 239, "item": {"id": 367, "name": "reduce", "url": "bad", "avatar": "any"}, "status": "1", "sortIndex": 439, "version": 205}, {"id": 910, "item": {"id": 519, "name": "place", "url": "poor", "avatar": "study"}, "status": "1", "sortIndex": 958, "version": 229}]}}, "errMessageOnly": "I", "successMessage": "necessary", "errMessage": "hear"};
}

module.exports = {
  generateResponseDataListItemShowResponse
};
