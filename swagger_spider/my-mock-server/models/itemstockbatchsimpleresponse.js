
// ItemStockBatchSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ItemStockBatchSimpleResponse模型的模拟数据
 * @returns {ItemStockBatchSimpleResponse} 模拟数据
 */
function generateItemStockBatchSimpleResponse() {
  return {"id": 919, "store": {"id": 199, "name": "though", "url": "attack", "avatar": "prove"}, "item": {"id": 207, "name": "lay", "url": "take", "avatar": "onto"}, "batchCount": 297, "salesCount": 145, "replacementCount": 514, "refundCount": 235, "restCount": 873, "createTime": 1753433976};
}

module.exports = {
  generateItemStockBatchSimpleResponse
};
