
// TodoListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成TodoListResponse模型的模拟数据
 * @returns {TodoListResponse} 模拟数据
 */
function generateTodoListResponse() {
  return {"id": 694, "taskType": "1", "status": "1", "manageId": 284, "roleId": 715, "relationType": "course", "relationId": 128, "prioritySort": 586, "taskName": "food", "summary": "identify"};
}

module.exports = {
  generateTodoListResponse
};
