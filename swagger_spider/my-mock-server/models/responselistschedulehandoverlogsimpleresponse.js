
// ResponseListScheduleHandoverLogSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseListScheduleHandoverLogSimpleResponse模型的模拟数据
 * @returns {ResponseListScheduleHandoverLogSimpleResponse} 模拟数据
 */
function generateResponseListScheduleHandoverLogSimpleResponse() {
  return {"code": 0, "msg": "success", "data": [{"id": 873, "scheduleId": 409, "scheduleName": "recent", "manageId": 744, "manageName": "magazine", "status": "1", "mark": "you", "createTime": **********}, {"id": 14, "scheduleId": 416, "scheduleName": "hit", "manageId": 12, "manageName": "agency", "status": "1", "mark": "give", "createTime": **********}, {"id": 964, "scheduleId": 169, "scheduleName": "fight", "manageId": 768, "manageName": "risk", "status": "1", "mark": "someone", "createTime": **********}], "result": {"code": "attr_value_id_not_exists", "data": [{"id": 291, "scheduleId": 449, "scheduleName": "including", "manageId": 124, "manageName": "fact"}]}, "errMessageOnly": "itself", "successMessage": "account", "errMessage": "agent"};
}

module.exports = {
  generateResponseListScheduleHandoverLogSimpleResponse
};
