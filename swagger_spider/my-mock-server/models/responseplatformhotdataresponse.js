
// ResponsePlatformHotDataResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponsePlatformHotDataResponse模型的模拟数据
 * @returns {ResponsePlatformHotDataResponse} 模拟数据
 */
function generateResponsePlatformHotDataResponse() {
  return {"code": 0, "msg": "success", "data": {"hotItemResponseList": [{"sortIndex": 932, "item": {"id": 691, "name": "year", "url": "understand", "avatar": "foot"}, "salesUnitPrice": 352, "totalSalesAmount": 860}, {"sortIndex": 603, "item": {"id": 97, "name": "after", "url": "woman", "avatar": "box"}, "salesUnitPrice": 79, "totalSalesAmount": 836}, {"sortIndex": 954, "item": {"id": 548, "name": "condition", "url": "enough", "avatar": "news"}, "salesUnitPrice": 996, "totalSalesAmount": 35}], "hotStoreResponseList": [{"sortIndex": 100, "store": {"id": 194, "name": "heavy", "url": "imagine", "avatar": "impact"}, "salesUnitPrice": 182, "totalSalesAmount": 520}, {"sortIndex": 383, "store": {"id": 561, "name": "view", "url": "us", "avatar": "among"}, "salesUnitPrice": 529, "totalSalesAmount": 883}, {"sortIndex": 957, "store": {"id": 2, "name": "hotel", "url": "central", "avatar": "after"}, "salesUnitPrice": 771, "totalSalesAmount": 800}], "hotPostsResponseList": [{"sortIndex": 371, "user": {"id": 872, "name": "sea", "url": "public", "avatar": "either"}, "posts": {"id": 684, "name": "with", "url": "owner", "avatar": "paper"}, "likeCount": 56}, {"sortIndex": 621, "user": {"id": 352, "name": "consider", "url": "dog", "avatar": "gas"}, "posts": {"id": 2, "name": "lay", "url": "research", "avatar": "what"}, "likeCount": 261}], "hotDemandResponseList": [{"sortIndex": 890, "user": {"id": 767, "name": "begin", "url": "attorney", "avatar": "dream"}, "demand": {"id": 157, "name": "hotel", "url": "action", "avatar": "good"}, "demandBidCount": 102}, {"sortIndex": 418, "user": {"id": 937, "name": "way", "url": "book", "avatar": "black"}, "demand": {"id": 890, "name": "current", "url": "whole", "avatar": "a"}, "demandBidCount": 245}, {"sortIndex": 561, "user": {"id": 51, "name": "laugh", "url": "world", "avatar": "subject"}, "demand": {"id": 257, "name": "item", "url": "near", "avatar": "model"}, "demandBidCount": 580}]}, "result": {"code": "schedule_leave_log_status_invalid", "data": {"hotItemResponseList": [{"sortIndex": 478, "item": {"id": 24, "name": "opportunity", "url": "stay", "avatar": "if"}, "salesUnitPrice": 919, "totalSalesAmount": 322}, {"sortIndex": 444, "item": {"id": 459, "name": "feel", "url": "for", "avatar": "age"}, "salesUnitPrice": 808, "totalSalesAmount": 353}, {"sortIndex": 475, "item": {"id": 499, "name": "movement", "url": "conference", "avatar": "carry"}, "salesUnitPrice": 369, "totalSalesAmount": 606}], "hotStoreResponseList": [{"sortIndex": 292, "store": {"id": 281, "name": "number", "url": "operation", "avatar": "reach"}, "salesUnitPrice": 378, "totalSalesAmount": 385}, {"sortIndex": 696, "store": {"id": 272, "name": "stay", "url": "ever", "avatar": "their"}, "salesUnitPrice": 632, "totalSalesAmount": 135}], "hotPostsResponseList": [{"sortIndex": 994, "user": {"id": 372, "name": "arm", "url": "theory", "avatar": "consumer"}, "posts": {"id": 483, "name": "here", "url": "significant", "avatar": "TV"}, "likeCount": 719}], "hotDemandResponseList": [{"sortIndex": 815, "user": {"id": 920, "name": "he", "url": "strong", "avatar": "year"}, "demand": {"id": 345, "name": "source", "url": "pass", "avatar": "give"}, "demandBidCount": 477}, {"sortIndex": 565, "user": {"id": 480, "name": "good", "url": "like", "avatar": "table"}, "demand": {"id": 680, "name": "glass", "url": "him", "avatar": "dinner"}, "demandBidCount": 291}, {"sortIndex": 314, "user": {"id": 174, "name": "enough", "url": "little", "avatar": "anyone"}, "demand": {"id": 311, "name": "paper", "url": "adult", "avatar": "including"}, "demandBidCount": 649}]}}, "errMessageOnly": "per", "successMessage": "minute", "errMessage": "clearly"};
}

module.exports = {
  generateResponsePlatformHotDataResponse
};
