
// ResultDemandAttrListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDemandAttrListResponse模型的模拟数据
 * @returns {ResultDemandAttrListResponse} 模拟数据
 */
function generateResultDemandAttrListResponse() {
  return {"code": "purchase_failure", "data": {"id": 813, "demand": {"id": 711, "name": "full", "url": "arm", "avatar": "mind"}, "attr": {"id": 545, "name": "fund", "url": "recently", "avatar": "offer"}, "attrValue": {"id": 508, "name": "major", "url": "leave", "avatar": "star"}, "createTime": 1753433976}};
}

module.exports = {
  generateResultDemandAttrListResponse
};
