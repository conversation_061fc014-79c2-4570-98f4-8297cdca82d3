
// ScheduleLeaveLogResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ScheduleLeaveLogResponse模型的模拟数据
 * @returns {ScheduleLeaveLogResponse} 模拟数据
 */
function generateScheduleLeaveLogResponse() {
  return {"id": 752, "schedule": {"id": 581, "name": "sea", "url": "face", "avatar": "skin"}, "manage": {"id": 724, "name": "assume", "url": "personal", "avatar": "always"}, "status": "1", "statusName": "professional", "askTime": "1998-06-13T05:01:41.473230", "leaveTime": "2003-08-05T10:16:27.794531", "backTime": "1999-03-09T16:35:53.187149", "leaveType": "1", "leaveTypeName": "suddenly"};
}

module.exports = {
  generateScheduleLeaveLogResponse
};
