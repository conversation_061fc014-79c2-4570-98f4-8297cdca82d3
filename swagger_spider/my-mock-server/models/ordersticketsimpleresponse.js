
// OrdersTicketSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成OrdersTicketSimpleResponse模型的模拟数据
 * @returns {OrdersTicketSimpleResponse} 模拟数据
 */
function generateOrdersTicketSimpleResponse() {
  return {"id": 863, "status": "1", "ordersId": 250, "user": {"id": 799, "name": "together", "url": "approach", "avatar": "nature"}, "store": {"id": 269, "name": "never", "url": "describe", "avatar": "member"}, "manage": {"id": 166, "name": "Mr", "url": "believe", "avatar": "example"}, "aftersalesId": 163, "aftersalesTxt": "picture", "createTime": 1753433976};
}

module.exports = {
  generateOrdersTicketSimpleResponse
};
