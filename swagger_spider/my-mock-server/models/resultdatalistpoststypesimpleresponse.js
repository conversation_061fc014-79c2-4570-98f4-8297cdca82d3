
// ResultDataListPostsTypeSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListPostsTypeSimpleResponse模型的模拟数据
 * @returns {ResultDataListPostsTypeSimpleResponse} 模拟数据
 */
function generateResultDataListPostsTypeSimpleResponse() {
  return {"code": "aftersales_type_status_not_null", "data": {"curPage": 436, "maxPage": 308, "total": 8458, "data": [{"id": 305, "status": "1", "language": "en_US", "typeName": "letter", "customUrl": "fine"}, {"id": 904, "status": "1", "language": "en_US", "typeName": "put", "customUrl": "event"}]}};
}

module.exports = {
  generateResultDataListPostsTypeSimpleResponse
};
