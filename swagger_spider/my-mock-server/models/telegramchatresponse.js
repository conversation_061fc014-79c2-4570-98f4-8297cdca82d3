
// TelegramChatResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成TelegramChatResponse模型的模拟数据
 * @returns {TelegramChatResponse} 模拟数据
 */
function generateTelegramChatResponse() {
  return {"id": 804, "telegramRobot": {"id": 835, "name": "two", "url": "radio", "avatar": "air"}, "status": "1", "username": "treatment", "chatId": 73, "firstName": "camera", "lastName": "financial", "createTime": 1753433976};
}

module.exports = {
  generateTelegramChatResponse
};
