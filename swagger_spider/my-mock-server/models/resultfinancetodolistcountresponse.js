
// ResultFinanceTodoListCountResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultFinanceTodoListCountResponse模型的模拟数据
 * @returns {ResultFinanceTodoListCountResponse} 模拟数据
 */
function generateResultFinanceTodoListCountResponse() {
  return {"code": "schedule_date_pattern_error", "data": {"totalTaskCount": 2890, "withdrawalAuditTaskCount": 9413, "paymentDisputeAuditTaskCount": 2856, "financialRiskControlAuditTaskCount": 9714, "exchangeRateUpdateTaskCount": 1290, "promotionAuditTaskCount": 8827}};
}

module.exports = {
  generateResultFinanceTodoListCountResponse
};
