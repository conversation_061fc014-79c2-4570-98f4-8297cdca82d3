
// BrandServicesListSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成BrandServicesListSimpleResponse模型的模拟数据
 * @returns {BrandServicesListSimpleResponse} 模拟数据
 */
function generateBrandServicesListSimpleResponse() {
  return {"id": 229, "brand": {"id": 544, "name": "add", "url": "head", "avatar": "almost"}, "services": {"id": 751, "name": "plan", "url": "level", "avatar": "involve"}, "createTime": 1753433976, "fieldList": [{"label": "catch", "fieldType": "yes", "fieldName": "low", "required": true, "defaultValue": "theory", "placeholder": "situation", "valueList": [{}]}, {"label": "painting", "fieldType": "several", "fieldName": "compare", "required": false, "defaultValue": "power", "placeholder": "agency", "valueList": [{}]}, {"label": "shake", "fieldType": "century", "fieldName": "end", "required": false, "defaultValue": "treat", "placeholder": "bad", "valueList": [{}]}]};
}

module.exports = {
  generateBrandServicesListSimpleResponse
};
