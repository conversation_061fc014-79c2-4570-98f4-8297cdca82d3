
// ResultDataListResourcesPermissionsSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListResourcesPermissionsSimpleResponse模型的模拟数据
 * @returns {ResultDataListResourcesPermissionsSimpleResponse} 模拟数据
 */
function generateResultDataListResourcesPermissionsSimpleResponse() {
  return {"code": "page_invalid", "data": {"curPage": 107, "maxPage": 318, "total": 4952, "data": [{"id": 956, "resourcesId": 217, "resourceMenuPath": "sound", "resourceMenuName": "water", "sortIndex": "0"}, {"id": 652, "resourcesId": 519, "resourceMenuPath": "half", "resourceMenuName": "size", "sortIndex": "0"}]}};
}

module.exports = {
  generateResultDataListResourcesPermissionsSimpleResponse
};
