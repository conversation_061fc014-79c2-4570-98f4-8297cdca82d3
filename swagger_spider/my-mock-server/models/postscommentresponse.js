
// PostsCommentResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成PostsCommentResponse模型的模拟数据
 * @returns {PostsCommentResponse} 模拟数据
 */
function generatePostsCommentResponse() {
  return {"id": 948, "posts": {"id": 338, "name": "here", "url": "make", "avatar": "focus"}, "user": {"id": 753, "name": "employee", "url": "billion", "avatar": "born"}, "status": "1", "paidStatus": "1", "level": "1", "comment": "occur", "createTime": 1753433976, "replyTime": 1753433976, "rating": 549};
}

module.exports = {
  generatePostsCommentResponse
};
