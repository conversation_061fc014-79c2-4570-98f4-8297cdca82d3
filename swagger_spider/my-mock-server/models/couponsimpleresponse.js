
// CouponSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成CouponSimpleResponse模型的模拟数据
 * @returns {CouponSimpleResponse} 模拟数据
 */
function generateCouponSimpleResponse() {
  return {"id": 75, "brand": {"id": 265, "name": "author", "url": "fine", "avatar": "your"}, "store": {"id": 307, "name": "hand", "url": "sign", "avatar": "mouth"}, "services": {"id": 356, "name": "future", "url": "keep", "avatar": "anything"}, "item": {"id": 84, "name": "meeting", "url": "draw", "avatar": "change"}, "actName": "radio", "couponStatus": "1", "couponCode": "guess", "discount": 478, "startTime": 1753433976};
}

module.exports = {
  generateCouponSimpleResponse
};
