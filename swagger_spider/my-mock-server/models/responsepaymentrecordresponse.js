
// ResponsePaymentRecordResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponsePaymentRecordResponse模型的模拟数据
 * @returns {ResponsePaymentRecordResponse} 模拟数据
 */
function generateResponsePaymentRecordResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 543, "user": {"id": 631, "name": "protect", "url": "her", "avatar": "worry"}, "payment": {"id": 559, "name": "scene", "url": "body", "avatar": "prove"}, "transactionId": "popular", "amount": 867, "currency": "option", "status": "1", "paymentTime": 1753433976, "refundAmount": 116, "refundTime": 1753433976}, "result": {"code": "user_id_not_null", "data": {"id": 747, "user": {"id": 328, "name": "single", "url": "him", "avatar": "husband"}, "payment": {"id": 624, "name": "face", "url": "maintain", "avatar": "picture"}, "transactionId": "against", "amount": 964, "currency": "help", "status": "1", "paymentTime": 1753433976, "refundAmount": 274, "refundTime": 1753433976}}, "errMessageOnly": "entire", "successMessage": "travel", "errMessage": "word"};
}

module.exports = {
  generateResponsePaymentRecordResponse
};
