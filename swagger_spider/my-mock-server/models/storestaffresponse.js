
// StoreStaffResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成StoreStaffResponse模型的模拟数据
 * @returns {StoreStaffResponse} 模拟数据
 */
function generateStoreStaffResponse() {
  return {"id": 538, "store": {"id": 201, "name": "draw", "url": "quickly", "avatar": "effort"}, "user": {"id": 867, "name": "report", "url": "despite", "avatar": "team"}, "status": "1", "nickName": "though", "avatar": "very", "phone": "may", "permissions": "north", "createTime": 1753433976, "title": "message"};
}

module.exports = {
  generateStoreStaffResponse
};
