
// ReservedRange 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ReservedRange模型的模拟数据
 * @returns {ReservedRange} 模拟数据
 */
function generateReservedRange() {
  return {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 966, "serializedSizeAsMessageSet": 662, "empty": false}, "initialized": false, "start": 685, "end": 677, "defaultInstanceForType": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 218, "serializedSizeAsMessageSet": 574, "empty": false}, "initialized": false, "start": 766, "end": 301, "defaultInstanceForType": {"id": 1, "name": "mock_ReservedRange"}, "parserForType": {}, "serializedSize": 908, "descriptorForType": {"index": 638, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 541, "serializedSizeAsMessageSet": 381}, "initialized": true, "fieldCount": 345, "reservedRangeList": [{"id": 1, "name": "mock_ReservedRange"}], "reservedNameList": ["different", "hit"]}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 611, "serializedSizeAsMessageSet": 552}, "initialized": true, "features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 135, "serializedSizeAsMessageSet": 360}, "initialized": true, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_MessageOptions"}}, "fullName": "official", "file": {"proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 796, "serializedSizeAsMessageSet": 245}, "initialized": false, "enumTypeCount": 12, "extensionCount": 566, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 188, "serializedSizeAsMessageSet": 715}, "initialized": false, "features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 843, "serializedSizeAsMessageSet": 201}, "initialized": false, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 966, "serializedSizeAsMessageSet": 597}, "initialized": true, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 574, "serializedSizeAsMessageSet": 179}, "initialized": true, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}, "messageTypes": [{"id": 1, "name": "mock_Descriptor"}], "enumTypes": [{"index": 439, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 402, "serializedSizeAsMessageSet": 159}, "initialized": false, "reservedRangeList": [{"unknownFields": {"mock": true}, "initialized": false, "start": 809, "end": 633, "defaultInstanceForType": {"id": 1, "name": "mock_EnumReservedRange"}}, {"unknownFields": {"mock": true}, "initialized": false, "start": 445, "end": 598, "defaultInstanceForType": {"id": 1, "name": "mock_EnumReservedRange"}}], "reservedNameList": ["control"], "valueList": [{"unknownFields": {"mock": true}, "initialized": false, "options": {"mock": true}, "defaultInstanceForType": {"id": 1, "name": "mock_EnumValueDescriptorProto"}, "parserForType": {"mock": true}}, {"unknownFields": {"mock": true}, "initialized": true, "options": {"mock": true}, "defaultInstanceForType": {"id": 1, "name": "mock_EnumValueDescriptorProto"}, "parserForType": {"mock": true}}]}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 861, "serializedSizeAsMessageSet": 70}, "initialized": false, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": false, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_EnumOptions"}}, "fullName": "us", "file": {"id": 1, "name": "mock_FileDescriptor"}}], "services": [{"index": 107, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 338, "serializedSizeAsMessageSet": 928}, "initialized": false, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": true, "features": {"unknownFields": {"mock": true}, "initialized": true, "enumType": "mock_string", "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}}, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceDescriptorProto"}, "parserForType": {}}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 685, "serializedSizeAsMessageSet": 906}, "initialized": false, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": false, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}}, "fullName": "keep", "file": {"id": 1, "name": "mock_FileDescriptor"}}, {"index": 9, "proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 924, "serializedSizeAsMessageSet": 138}, "initialized": false, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": false, "features": {"unknownFields": {"mock": true}, "initialized": true, "enumType": "mock_string", "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}}, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceDescriptorProto"}, "parserForType": {}}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 456, "serializedSizeAsMessageSet": 214}, "initialized": true, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": true, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}}, "fullName": "deal", "file": {"id": 1, "name": "mock_FileDescriptor"}}]}, "containingType": {"id": 1, "name": "mock_Descriptor"}, "nestedTypes": [{"id": 1, "name": "mock_Descriptor"}], "enumTypes": [{"index": 126, "proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 142, "serializedSizeAsMessageSet": 500}, "initialized": false, "reservedRangeList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 202, "serializedSizeAsMessageSet": 429}, "initialized": true, "start": 205, "end": 665, "defaultInstanceForType": {"id": 1, "name": "mock_EnumReservedRange"}}, {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 998, "serializedSizeAsMessageSet": 986}, "initialized": false, "start": 700, "end": 883, "defaultInstanceForType": {"id": 1, "name": "mock_EnumReservedRange"}}], "reservedNameList": ["nearly", "pass"], "valueList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 122, "serializedSizeAsMessageSet": 739}, "initialized": true, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_EnumValueOptions"}}, "defaultInstanceForType": {"id": 1, "name": "mock_EnumValueDescriptorProto"}, "parserForType": {}}]}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 579, "serializedSizeAsMessageSet": 346}, "initialized": false, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 55, "serializedSizeAsMessageSet": 704}, "initialized": false, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_EnumOptions"}}, "fullName": "lot", "file": {"proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 439, "serializedSizeAsMessageSet": 728}, "initialized": false, "enumTypeCount": 413, "extensionCount": 459, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": false, "features": {"unknownFields": {"mock": true}, "initialized": true, "enumType": "mock_string", "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 292, "serializedSizeAsMessageSet": 279}, "initialized": true, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": true, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}, "messageTypes": [{"id": 1, "name": "mock_Descriptor"}, {"id": 1, "name": "mock_Descriptor"}], "enumTypes": [{"id": 1, "name": "mock_EnumDescriptor"}, {"id": 1, "name": "mock_EnumDescriptor"}], "services": [{"index": 627, "proto": {"unknownFields": {"mock": true}, "initialized": false, "options": {"mock": true}, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceDescriptorProto"}, "parserForType": {"mock": true}}, "options": {"unknownFields": {"mock": true}, "initialized": false, "features": {"mock": true}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}}, "fullName": "daughter", "file": {"id": 1, "name": "mock_FileDescriptor"}}]}}], "fields": [{"index": 284, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 811, "serializedSizeAsMessageSet": 61}, "initialized": false, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 999, "serializedSizeAsMessageSet": 3}, "initialized": true, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": false, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "lazy": false, "deprecated": false}, "label": "LABEL_OPTIONAL", "defaultInstanceForType": {"id": 1, "name": "mock_FieldDescriptorProto"}}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 753, "serializedSizeAsMessageSet": 735}, "initialized": true, "features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 740, "serializedSizeAsMessageSet": 619}, "initialized": true, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "lazy": true, "deprecated": true}, "fullName": "blue", "jsonName": "head"}, {"index": 709, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 107, "serializedSizeAsMessageSet": 880}, "initialized": false, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 517, "serializedSizeAsMessageSet": 555}, "initialized": true, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": true, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "lazy": false, "deprecated": false}, "label": "LABEL_REPEATED", "defaultInstanceForType": {"id": 1, "name": "mock_FieldDescriptorProto"}}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 819, "serializedSizeAsMessageSet": 175}, "initialized": true, "features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 849, "serializedSizeAsMessageSet": 636}, "initialized": true, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "lazy": true, "deprecated": false}, "fullName": "heavy", "jsonName": "something"}, {"index": 4, "proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 182, "serializedSizeAsMessageSet": 415}, "initialized": true, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 907, "serializedSizeAsMessageSet": 243}, "initialized": false, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": false, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "lazy": true, "deprecated": false}, "label": "LABEL_OPTIONAL", "defaultInstanceForType": {"id": 1, "name": "mock_FieldDescriptorProto"}}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 288, "serializedSizeAsMessageSet": 361}, "initialized": false, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 707, "serializedSizeAsMessageSet": 542}, "initialized": true, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "lazy": false, "deprecated": false}, "fullName": "add", "jsonName": "doctor"}], "extensions": [{"index": 561, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 453, "serializedSizeAsMessageSet": 18}, "initialized": true, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 177, "serializedSizeAsMessageSet": 507}, "initialized": false, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": true, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "lazy": true, "deprecated": true}, "label": "LABEL_REQUIRED", "defaultInstanceForType": {"id": 1, "name": "mock_FieldDescriptorProto"}}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 420, "serializedSizeAsMessageSet": 43}, "initialized": true, "features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 999, "serializedSizeAsMessageSet": 574}, "initialized": true, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "lazy": false, "deprecated": true}, "fullName": "station", "jsonName": "fly"}, {"index": 215, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 406, "serializedSizeAsMessageSet": 554}, "initialized": false, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 55, "serializedSizeAsMessageSet": 32}, "initialized": false, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": true, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "lazy": true, "deprecated": false}, "label": "LABEL_REPEATED", "defaultInstanceForType": {"id": 1, "name": "mock_FieldDescriptorProto"}}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 547, "serializedSizeAsMessageSet": 322}, "initialized": false, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 642, "serializedSizeAsMessageSet": 914}, "initialized": true, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "lazy": false, "deprecated": false}, "fullName": "citizen", "jsonName": "role"}, {"index": 949, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 574, "serializedSizeAsMessageSet": 436}, "initialized": true, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 804, "serializedSizeAsMessageSet": 547}, "initialized": true, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": true, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "lazy": false, "deprecated": false}, "label": "LABEL_OPTIONAL", "defaultInstanceForType": {"id": 1, "name": "mock_FieldDescriptorProto"}}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 304, "serializedSizeAsMessageSet": 723}, "initialized": false, "features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 516, "serializedSizeAsMessageSet": 871}, "initialized": true, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "lazy": true, "deprecated": false}, "fullName": "happy", "jsonName": "watch"}]}, "allFields": {}, "initializationErrorString": "general"}, "parserForType": {}, "serializedSize": 33, "descriptorForType": {"index": 65, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 424, "serializedSizeAsMessageSet": 367}, "initialized": false, "fieldCount": 16, "reservedRangeList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 668, "serializedSizeAsMessageSet": 729}, "initialized": true, "start": 268, "end": 613, "defaultInstanceForType": {"id": 1, "name": "mock_ReservedRange"}}, {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 299, "serializedSizeAsMessageSet": 116}, "initialized": true, "start": 582, "end": 815, "defaultInstanceForType": {"id": 1, "name": "mock_ReservedRange"}}, {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 379, "serializedSizeAsMessageSet": 901}, "initialized": false, "start": 368, "end": 909, "defaultInstanceForType": {"id": 1, "name": "mock_ReservedRange"}}], "reservedNameList": ["three", "government", "discussion"], "extensionRangeList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 797, "serializedSizeAsMessageSet": 799}, "initialized": true, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 603, "serializedSizeAsMessageSet": 18}, "initialized": true, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 710, "serializedSizeAsMessageSet": 804}, "initialized": false, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "declarationCount": 936, "verification": "DECLARATION"}, "start": 193, "end": 339}, {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 296, "serializedSizeAsMessageSet": 490}, "initialized": false, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 819, "serializedSizeAsMessageSet": 271}, "initialized": false, "features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 235, "serializedSizeAsMessageSet": 8}, "initialized": true, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "declarationCount": 31, "verification": "UNVERIFIED"}, "start": 441, "end": 434}], "oneofDeclCount": 285, "nestedTypeCount": 269, "enumTypeCount": 166, "extensionCount": 681}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 554, "serializedSizeAsMessageSet": 989}, "initialized": false, "features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 970, "serializedSizeAsMessageSet": 113}, "initialized": true, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_MessageOptions"}, "parserForType": {}, "serializedSize": 193, "messageSetWireFormat": true, "uninterpretedOptionOrBuilderList": [{"doubleValue": 809.57, "stringValue": {"validUtf8": true, "empty": false}, "nameList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 480, "serializedSizeAsMessageSet": 231}, "initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 735}, {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 134, "serializedSizeAsMessageSet": 648}, "initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 487}], "identifierValue": "two", "positiveIntValue": 4395}, {"doubleValue": 233.26, "stringValue": {"validUtf8": true, "empty": true}, "nameList": [{"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 246, "serializedSizeAsMessageSet": 8}, "initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 360}, {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 933, "serializedSizeAsMessageSet": 973}, "initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 903}], "identifierValue": "garden", "positiveIntValue": 3830}, {"doubleValue": 998.91, "stringValue": {"validUtf8": false, "empty": true}, "nameList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 493, "serializedSizeAsMessageSet": 744}, "initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 332}], "identifierValue": "born", "positiveIntValue": 5768}], "mapEntry": false}, "fullName": "west", "file": {"proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 910, "serializedSizeAsMessageSet": 993}, "initialized": false, "enumTypeCount": 278, "extensionCount": 771, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 670, "serializedSizeAsMessageSet": 253}, "initialized": true, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 57, "serializedSizeAsMessageSet": 70}, "initialized": true, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 956, "serializedSizeAsMessageSet": 151}, "initialized": false, "features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 368, "serializedSizeAsMessageSet": 106}, "initialized": true, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}, "messageTypes": [{"id": 1, "name": "mock_Descriptor"}], "enumTypes": [{"index": 344, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 916, "serializedSizeAsMessageSet": 943}, "initialized": true, "reservedRangeList": [{"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 171, "serializedSizeAsMessageSet": 42}, "initialized": true, "start": 983, "end": 527, "defaultInstanceForType": {"id": 1, "name": "mock_EnumReservedRange"}}, {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 630, "serializedSizeAsMessageSet": 440}, "initialized": false, "start": 450, "end": 514, "defaultInstanceForType": {"id": 1, "name": "mock_EnumReservedRange"}}], "reservedNameList": ["pressure", "hot"], "valueList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 939, "serializedSizeAsMessageSet": 117}, "initialized": false, "options": {"unknownFields": {"mock": true}, "initialized": false, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_EnumValueOptions"}}, "defaultInstanceForType": {"id": 1, "name": "mock_EnumValueDescriptorProto"}, "parserForType": {}}]}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 586, "serializedSizeAsMessageSet": 25}, "initialized": false, "features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 623, "serializedSizeAsMessageSet": 792}, "initialized": false, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_EnumOptions"}}, "fullName": "dream", "file": {"id": 1, "name": "mock_FileDescriptor"}}, {"index": 191, "proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 364, "serializedSizeAsMessageSet": 547}, "initialized": false, "reservedRangeList": [{"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 18, "serializedSizeAsMessageSet": 691}, "initialized": true, "start": 597, "end": 415, "defaultInstanceForType": {"id": 1, "name": "mock_EnumReservedRange"}}], "reservedNameList": ["difficult", "view"], "valueList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 260, "serializedSizeAsMessageSet": 825}, "initialized": false, "options": {"unknownFields": {"mock": true}, "initialized": false, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_EnumValueOptions"}}, "defaultInstanceForType": {"id": 1, "name": "mock_EnumValueDescriptorProto"}, "parserForType": {}}]}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 947, "serializedSizeAsMessageSet": 355}, "initialized": false, "features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 64, "serializedSizeAsMessageSet": 481}, "initialized": false, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_EnumOptions"}}, "fullName": "avoid", "file": {"id": 1, "name": "mock_FileDescriptor"}}], "services": [{"index": 330, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 172, "serializedSizeAsMessageSet": 870}, "initialized": false, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 272, "serializedSizeAsMessageSet": 966}, "initialized": true, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": false, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}}, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceDescriptorProto"}, "parserForType": {}}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 618, "serializedSizeAsMessageSet": 841}, "initialized": true, "features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 742, "serializedSizeAsMessageSet": 332}, "initialized": true, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}}, "fullName": "impact", "file": {"id": 1, "name": "mock_FileDescriptor"}}, {"index": 75, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 875, "serializedSizeAsMessageSet": 520}, "initialized": true, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 935, "serializedSizeAsMessageSet": 678}, "initialized": true, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": false, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}}, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceDescriptorProto"}, "parserForType": {}}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 669, "serializedSizeAsMessageSet": 120}, "initialized": false, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 376, "serializedSizeAsMessageSet": 913}, "initialized": false, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}}, "fullName": "newspaper", "file": {"id": 1, "name": "mock_FileDescriptor"}}, {"index": 306, "proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 20, "serializedSizeAsMessageSet": 770}, "initialized": false, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 171, "serializedSizeAsMessageSet": 444}, "initialized": false, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": true, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}}, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceDescriptorProto"}, "parserForType": {}}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 130, "serializedSizeAsMessageSet": 584}, "initialized": false, "features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 618, "serializedSizeAsMessageSet": 786}, "initialized": true, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}}, "fullName": "whatever", "file": {"id": 1, "name": "mock_FileDescriptor"}}], "extensions": [{"index": 571, "proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 887, "serializedSizeAsMessageSet": 838}, "initialized": false, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 274, "serializedSizeAsMessageSet": 826}, "initialized": true, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": false, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "lazy": true, "deprecated": true}, "label": "LABEL_REQUIRED", "defaultInstanceForType": {"id": 1, "name": "mock_FieldDescriptorProto"}}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 374, "serializedSizeAsMessageSet": 437}, "initialized": false, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 109, "serializedSizeAsMessageSet": 473}, "initialized": true, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "lazy": false, "deprecated": true}, "fullName": "hard", "jsonName": "include"}, {"index": 786, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 519, "serializedSizeAsMessageSet": 245}, "initialized": true, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 203, "serializedSizeAsMessageSet": 55}, "initialized": false, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": true, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "lazy": true, "deprecated": true}, "label": "LABEL_REQUIRED", "defaultInstanceForType": {"id": 1, "name": "mock_FieldDescriptorProto"}}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 966, "serializedSizeAsMessageSet": 944}, "initialized": true, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 497, "serializedSizeAsMessageSet": 60}, "initialized": true, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "lazy": true, "deprecated": true}, "fullName": "color", "jsonName": "save"}], "dependencies": [{"id": 1, "name": "mock_FileDescriptor"}, {"id": 1, "name": "mock_FileDescriptor"}], "publicDependencies": [{"id": 1, "name": "mock_FileDescriptor"}, {"id": 1, "name": "mock_FileDescriptor"}], "fullName": "tonight", "name": "yes"}, "containingType": {"id": 1, "name": "mock_Descriptor"}, "nestedTypes": [{"id": 1, "name": "mock_Descriptor"}], "enumTypes": [{"index": 402, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 758, "serializedSizeAsMessageSet": 274}, "initialized": false, "reservedRangeList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 756, "serializedSizeAsMessageSet": 806}, "initialized": false, "start": 240, "end": 565, "defaultInstanceForType": {"id": 1, "name": "mock_EnumReservedRange"}}, {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 276, "serializedSizeAsMessageSet": 871}, "initialized": true, "start": 816, "end": 468, "defaultInstanceForType": {"id": 1, "name": "mock_EnumReservedRange"}}], "reservedNameList": ["trade", "player"], "valueList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 608, "serializedSizeAsMessageSet": 665}, "initialized": true, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 469, "serializedSizeAsMessageSet": 986}, "initialized": false, "features": {"unknownFields": {"mock": true}, "initialized": false, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {"mock": true}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_EnumValueOptions"}}, "defaultInstanceForType": {"id": 1, "name": "mock_EnumValueDescriptorProto"}, "parserForType": {}}]}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 576, "serializedSizeAsMessageSet": 185}, "initialized": false, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 722, "serializedSizeAsMessageSet": 585}, "initialized": true, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_EnumOptions"}}, "fullName": "would", "file": {"proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 796, "serializedSizeAsMessageSet": 34}, "initialized": true, "enumTypeCount": 754, "extensionCount": 415, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 850, "serializedSizeAsMessageSet": 223}, "initialized": false, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": true, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 915, "serializedSizeAsMessageSet": 944}, "initialized": true, "features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 928, "serializedSizeAsMessageSet": 572}, "initialized": true, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}, "messageTypes": [{"id": 1, "name": "mock_Descriptor"}, {"id": 1, "name": "mock_Descriptor"}], "enumTypes": [{"id": 1, "name": "mock_EnumDescriptor"}, {"id": 1, "name": "mock_EnumDescriptor"}], "services": [{"index": 54, "proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 730, "serializedSizeAsMessageSet": 633}, "initialized": true, "options": {"unknownFields": {"mock": true}, "initialized": false, "features": {"mock": true}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}}, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceDescriptorProto"}, "parserForType": {}}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 409, "serializedSizeAsMessageSet": 410}, "initialized": false, "features": {"unknownFields": {"mock": true}, "initialized": false, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {"mock": true}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}}, "fullName": "blood", "file": {"id": 1, "name": "mock_FileDescriptor"}}]}}], "fields": [{"index": 388, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 534, "serializedSizeAsMessageSet": 857}, "initialized": true, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 296, "serializedSizeAsMessageSet": 951}, "initialized": true, "features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 860, "serializedSizeAsMessageSet": 956}, "initialized": false, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "lazy": false, "deprecated": false}, "label": "LABEL_REQUIRED", "defaultInstanceForType": {"id": 1, "name": "mock_FieldDescriptorProto"}}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 66, "serializedSizeAsMessageSet": 438}, "initialized": false, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 567, "serializedSizeAsMessageSet": 389}, "initialized": false, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "lazy": false, "deprecated": true}, "fullName": "data", "jsonName": "director"}, {"index": 995, "proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 243, "serializedSizeAsMessageSet": 870}, "initialized": true, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 630, "serializedSizeAsMessageSet": 848}, "initialized": false, "features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 175, "serializedSizeAsMessageSet": 827}, "initialized": false, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "lazy": true, "deprecated": false}, "label": "LABEL_REQUIRED", "defaultInstanceForType": {"id": 1, "name": "mock_FieldDescriptorProto"}}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 762, "serializedSizeAsMessageSet": 583}, "initialized": false, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 28, "serializedSizeAsMessageSet": 707}, "initialized": false, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "lazy": false, "deprecated": true}, "fullName": "prepare", "jsonName": "traditional"}, {"index": 420, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 398, "serializedSizeAsMessageSet": 72}, "initialized": true, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 142, "serializedSizeAsMessageSet": 414}, "initialized": true, "features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 969, "serializedSizeAsMessageSet": 571}, "initialized": false, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "lazy": false, "deprecated": false}, "label": "LABEL_REPEATED", "defaultInstanceForType": {"id": 1, "name": "mock_FieldDescriptorProto"}}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 556, "serializedSizeAsMessageSet": 541}, "initialized": false, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 11, "serializedSizeAsMessageSet": 679}, "initialized": false, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "lazy": false, "deprecated": true}, "fullName": "force", "jsonName": "positive"}], "extensions": [{"index": 563, "proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 589, "serializedSizeAsMessageSet": 47}, "initialized": false, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 937, "serializedSizeAsMessageSet": 826}, "initialized": false, "features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 495, "serializedSizeAsMessageSet": 356}, "initialized": true, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "lazy": true, "deprecated": false}, "label": "LABEL_OPTIONAL", "defaultInstanceForType": {"id": 1, "name": "mock_FieldDescriptorProto"}}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 785, "serializedSizeAsMessageSet": 132}, "initialized": false, "features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 209, "serializedSizeAsMessageSet": 61}, "initialized": true, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "lazy": true, "deprecated": true}, "fullName": "indicate", "jsonName": "laugh"}, {"index": 67, "proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 761, "serializedSizeAsMessageSet": 973}, "initialized": true, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 429, "serializedSizeAsMessageSet": 462}, "initialized": true, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 447, "serializedSizeAsMessageSet": 362}, "initialized": false, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "lazy": true, "deprecated": true}, "label": "LABEL_REQUIRED", "defaultInstanceForType": {"id": 1, "name": "mock_FieldDescriptorProto"}}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 626, "serializedSizeAsMessageSet": 374}, "initialized": true, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 827, "serializedSizeAsMessageSet": 378}, "initialized": false, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "lazy": true, "deprecated": true}, "fullName": "thank", "jsonName": "simple"}, {"index": 528, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 460, "serializedSizeAsMessageSet": 956}, "initialized": true, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 884, "serializedSizeAsMessageSet": 673}, "initialized": true, "features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 756, "serializedSizeAsMessageSet": 952}, "initialized": false, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "lazy": true, "deprecated": true}, "label": "LABEL_REQUIRED", "defaultInstanceForType": {"id": 1, "name": "mock_FieldDescriptorProto"}}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 509, "serializedSizeAsMessageSet": 406}, "initialized": true, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 692, "serializedSizeAsMessageSet": 102}, "initialized": true, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "lazy": false, "deprecated": true}, "fullName": "focus", "jsonName": "house"}]}, "allFields": {}, "initializationErrorString": "involve"};
}

module.exports = {
  generateReservedRange
};
