
// ResultSearchResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultSearchResponse模型的模拟数据
 * @returns {ResultSearchResponse} 模拟数据
 */
function generateResultSearchResponse() {
  return {"code": "manage_permissions_update_failed", "data": {"id": 696, "searchType": "1", "searchTypeName": "nice", "brand": {"id": 541, "name": "laugh", "url": "available", "avatar": "rate"}, "status": "1", "statusName": "cold", "sortIndex": 747, "searchCount": 937, "createTime": 1753433976, "updateTime": 1753433976}};
}

module.exports = {
  generateResultSearchResponse
};
