
// ResultDataListScheduleAttendanceReportDTO 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListScheduleAttendanceReportDTO模型的模拟数据
 * @returns {ResultDataListScheduleAttendanceReportDTO} 模拟数据
 */
function generateResultDataListScheduleAttendanceReportDTO() {
  return {"code": "schedule_batch_delete_error", "data": {"curPage": 120, "maxPage": 753, "total": 4421, "data": [{"manage": {"id": 30, "name": "beautiful", "url": "do", "avatar": "past"}, "month": "2024-05-21T11:30:53.461857", "schedulingTimes": 1753433976, "scheduledWorkingHours": 710, "actualWorkingHours": 89}, {"manage": {"id": 525, "name": "tax", "url": "building", "avatar": "answer"}, "month": "1999-10-18T09:08:11.461826", "schedulingTimes": 1753433976, "scheduledWorkingHours": 451, "actualWorkingHours": 178}, {"manage": {"id": 443, "name": "front", "url": "here", "avatar": "leg"}, "month": "1994-09-21T10:14:27.868238", "schedulingTimes": 1753433976, "scheduledWorkingHours": 372, "actualWorkingHours": 130}]}};
}

module.exports = {
  generateResultDataListScheduleAttendanceReportDTO
};
