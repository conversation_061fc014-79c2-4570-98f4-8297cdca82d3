
// ResultOrdersResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultOrdersResponse模型的模拟数据
 * @returns {ResultOrdersResponse} 模拟数据
 */
function generateResultOrdersResponse() {
  return {"code": "upload_error", "data": {"id": 7, "user": {"id": 92, "name": "success", "url": "notice", "avatar": "audience"}, "item": {"id": 541, "name": "their", "url": "fall", "avatar": "much"}, "posts": {"id": 582, "name": "condition", "url": "then", "avatar": "Congress"}, "demand": {"id": 18, "name": "sign", "url": "fire", "avatar": "along"}, "store": {"id": 755, "name": "current", "url": "them", "avatar": "include"}, "ordersType": "1", "status": "1", "price": 889, "originalPrice": 670}};
}

module.exports = {
  generateResultOrdersResponse
};
