
// ViolationsEventResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ViolationsEventResponse模型的模拟数据
 * @returns {ViolationsEventResponse} 模拟数据
 */
function generateViolationsEventResponse() {
  return {"id": 470, "user": {"id": 565, "name": "history", "url": "civil", "avatar": "writer"}, "store": {"id": 885, "name": "go", "url": "alone", "avatar": "either"}, "item": {"id": 92, "name": "child", "url": "bed", "avatar": "million"}, "demand": {"id": 166, "name": "enjoy", "url": "student", "avatar": "mouth"}, "posts": {"id": 872, "name": "but", "url": "operation", "avatar": "body"}, "rulesId": 25, "eventType": "1", "relationId": 68, "status": "1"};
}

module.exports = {
  generateViolationsEventResponse
};
