
// ResultDataListOrdersCommentRatingResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListOrdersCommentRatingResponse模型的模拟数据
 * @returns {ResultDataListOrdersCommentRatingResponse} 模拟数据
 */
function generateResultDataListOrdersCommentRatingResponse() {
  return {"code": "orders_ticket_message_send_failed", "data": {"curPage": 673, "maxPage": 420, "total": 1511, "data": [{"id": 678, "ordersComment": {"id": 745, "name": "two", "url": "property", "avatar": "write"}, "rating": {"id": 34, "name": "magazine", "url": "day", "avatar": "word"}, "score": "1"}, {"id": 894, "ordersComment": {"id": 176, "name": "more", "url": "worry", "avatar": "case"}, "rating": {"id": 265, "name": "want", "url": "thought", "avatar": "investment"}, "score": "1"}, {"id": 230, "ordersComment": {"id": 337, "name": "eye", "url": "admit", "avatar": "deal"}, "rating": {"id": 696, "name": "state", "url": "use", "avatar": "make"}, "score": "1"}]}};
}

module.exports = {
  generateResultDataListOrdersCommentRatingResponse
};
