
// ResultDemandResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDemandResponse模型的模拟数据
 * @returns {ResultDemandResponse} 模拟数据
 */
function generateResultDemandResponse() {
  return {"code": "attr_name_exists", "data": {"id": 424, "user": {"id": 169, "name": "attorney", "url": "its", "avatar": "nothing"}, "brand": {"id": 970, "name": "early", "url": "keep", "avatar": "good"}, "services": {"id": 957, "name": "must", "url": "seven", "avatar": "subject"}, "attrList": [{"attrId": 879, "attrName": "Mr", "attrValueId": 370, "attrValueName": "media"}], "status": "1", "language": "en_US", "demandName": "hope", "priceFrom": 793, "priceTo": 180}};
}

module.exports = {
  generateResultDemandResponse
};
