
// ResultDataListUserSanctionsResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListUserSanctionsResponse模型的模拟数据
 * @returns {ResultDataListUserSanctionsResponse} 模拟数据
 */
function generateResultDataListUserSanctionsResponse() {
  return {"code": "posts_type_id_not_exists", "data": {"curPage": 42, "maxPage": 480, "total": 6196, "data": [{"id": 852, "violationsEventId": 378, "user": {"id": 911, "name": "sign", "url": "than", "avatar": "as"}, "status": "1", "sanctionType": "1"}, {"id": 495, "violationsEventId": 613, "user": {"id": 457, "name": "let", "url": "election", "avatar": "factor"}, "status": "1", "sanctionType": "1"}, {"id": 22, "violationsEventId": 484, "user": {"id": 239, "name": "sell", "url": "whether", "avatar": "meeting"}, "status": "1", "sanctionType": "1"}]}};
}

module.exports = {
  generateResultDataListUserSanctionsResponse
};
