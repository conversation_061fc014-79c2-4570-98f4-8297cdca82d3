
// DataListPromotionResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListPromotionResponse模型的模拟数据
 * @returns {DataListPromotionResponse} 模拟数据
 */
function generateDataListPromotionResponse() {
  return {"curPage": 509, "maxPage": 514, "total": 8477, "data": [{"id": 660, "promotionType": "1", "brand": {"id": 998, "name": "talk", "url": "issue", "avatar": "war"}, "search": {"id": 620, "name": "soon", "url": "movement", "avatar": "operation"}, "user": {"id": 53, "name": "down", "url": "necessary", "avatar": "that"}, "store": {"id": 45, "name": "democratic", "url": "detail", "avatar": "politics"}, "item": {"id": 276, "name": "stage", "url": "paper", "avatar": "attorney"}, "status": "1", "price": 0, "premiumPrice": 828}, {"id": 34, "promotionType": "1", "brand": {"id": 288, "name": "save", "url": "early", "avatar": "community"}, "search": {"id": 78, "name": "community", "url": "coach", "avatar": "dinner"}, "user": {"id": 268, "name": "why", "url": "majority", "avatar": "throughout"}, "store": {"id": 629, "name": "its", "url": "control", "avatar": "improve"}, "item": {"id": 824, "name": "level", "url": "no", "avatar": "religious"}, "status": "1", "price": 254, "premiumPrice": 844}, {"id": 735, "promotionType": "1", "brand": {"id": 960, "name": "agree", "url": "PM", "avatar": "about"}, "search": {"id": 632, "name": "reveal", "url": "concern", "avatar": "anything"}, "user": {"id": 986, "name": "bill", "url": "financial", "avatar": "goal"}, "store": {"id": 309, "name": "kind", "url": "fight", "avatar": "cold"}, "item": {"id": 449, "name": "decision", "url": "travel", "avatar": "world"}, "status": "1", "price": 167, "premiumPrice": 71}]};
}

module.exports = {
  generateDataListPromotionResponse
};
