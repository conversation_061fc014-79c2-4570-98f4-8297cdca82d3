
// BrandFieldResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成BrandFieldResponse模型的模拟数据
 * @returns {BrandFieldResponse} 模拟数据
 */
function generateBrandFieldResponse() {
  return {"id": 598, "brandId": 974, "fieldName": "question", "fieldType": "1", "required": "1", "createTime": 1753433976, "updateTime": 1753433976, "brandFieldLangId": 862, "language": "en_US", "label": "nature"};
}

module.exports = {
  generateBrandFieldResponse
};
