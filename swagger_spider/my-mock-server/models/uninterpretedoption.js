
// UninterpretedOption 模型
// 由SwaggerCrawler自动生成

/**
 * 生成UninterpretedOption模型的模拟数据
 * @returns {UninterpretedOption} 模拟数据
 */
function generateUninterpretedOption() {
  return {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 728, "serializedSizeAsMessageSet": 513, "empty": false}, "initialized": true, "doubleValue": 50.08, "defaultInstanceForType": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 887, "serializedSizeAsMessageSet": 173, "empty": false}, "initialized": false, "doubleValue": 900.61, "defaultInstanceForType": {"id": 1, "name": "mock_UninterpretedOption"}, "parserForType": {}, "stringValue": {"validUtf8": false, "empty": true}, "serializedSize": 287, "nameList": [{"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 6, "serializedSizeAsMessageSet": 991}, "initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 681}, {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 529, "serializedSizeAsMessageSet": 215}, "initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 907}], "identifierValue": "hotel", "positiveIntValue": 8296}, "parserForType": {}, "stringValue": {"validUtf8": true, "empty": true}, "serializedSize": 88, "nameList": [{"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 656, "serializedSizeAsMessageSet": 979}, "initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 394, "namePartBytes": {"validUtf8": false, "empty": true}, "isExtension": false, "namePart": "dream", "descriptorForType": {"index": 693, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 196, "serializedSizeAsMessageSet": 202}, "initialized": false, "fieldCount": 278, "reservedRangeList": [{"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 949, "serializedSizeAsMessageSet": 180}, "initialized": false, "start": 733, "end": 809, "defaultInstanceForType": {"id": 1, "name": "mock_ReservedRange"}}, {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 319, "serializedSizeAsMessageSet": 868}, "initialized": true, "start": 759, "end": 60, "defaultInstanceForType": {"id": 1, "name": "mock_ReservedRange"}}], "reservedNameList": ["never", "specific"]}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 209, "serializedSizeAsMessageSet": 184}, "initialized": true, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 433, "serializedSizeAsMessageSet": 935}, "initialized": false, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_MessageOptions"}}, "fullName": "Mr", "file": {"proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 164, "serializedSizeAsMessageSet": 985}, "initialized": true, "enumTypeCount": 836, "extensionCount": 222, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 358, "serializedSizeAsMessageSet": 33}, "initialized": false, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": true, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 672, "serializedSizeAsMessageSet": 120}, "initialized": false, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 486, "serializedSizeAsMessageSet": 66}, "initialized": true, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}, "messageTypes": [{"id": 1, "name": "mock_Descriptor"}], "enumTypes": [{"index": 332, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 257, "serializedSizeAsMessageSet": 432}, "initialized": false, "reservedRangeList": [{"unknownFields": {"mock": true}, "initialized": true, "start": 1, "end": 1, "defaultInstanceForType": {"mock": true}}], "reservedNameList": ["issue"], "valueList": [{"unknownFields": {"mock": true}, "initialized": true, "options": {"mock": true}, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}, {"unknownFields": {"mock": true}, "initialized": true, "options": {"mock": true}, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}]}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 833, "serializedSizeAsMessageSet": 312}, "initialized": false, "features": {"unknownFields": {"mock": true}, "initialized": false, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {"mock": true}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_EnumOptions"}}, "fullName": "join", "file": {"id": 1, "name": "mock_FileDescriptor"}}, {"index": 305, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 609, "serializedSizeAsMessageSet": 458}, "initialized": true, "reservedRangeList": [{"unknownFields": {"mock": true}, "initialized": true, "start": 1, "end": 1, "defaultInstanceForType": {"mock": true}}, {"unknownFields": {"mock": true}, "initialized": true, "start": 1, "end": 1, "defaultInstanceForType": {"mock": true}}], "reservedNameList": ["become", "future"], "valueList": [{"unknownFields": {"mock": true}, "initialized": true, "options": {"mock": true}, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}]}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 462, "serializedSizeAsMessageSet": 393}, "initialized": false, "features": {"unknownFields": {"mock": true}, "initialized": true, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {"mock": true}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_EnumOptions"}}, "fullName": "term", "file": {"id": 1, "name": "mock_FileDescriptor"}}], "services": [{"index": 174, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 42, "serializedSizeAsMessageSet": 655}, "initialized": false, "options": {"unknownFields": {"mock": true}, "initialized": false, "features": {"mock": true}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}}, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceDescriptorProto"}, "parserForType": {}}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 499, "serializedSizeAsMessageSet": 537}, "initialized": false, "features": {"unknownFields": {"mock": true}, "initialized": false, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {"mock": true}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}}, "fullName": "discussion", "file": {"id": 1, "name": "mock_FileDescriptor"}}]}}, "allFields": {}}, {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 487, "serializedSizeAsMessageSet": 697}, "initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_NamePart"}, "parserForType": {}, "serializedSize": 959, "namePartBytes": {"validUtf8": true, "empty": true}, "isExtension": false, "namePart": "imagine", "descriptorForType": {"index": 810, "proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 193, "serializedSizeAsMessageSet": 719}, "initialized": false, "fieldCount": 216, "reservedRangeList": [{"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 355, "serializedSizeAsMessageSet": 717}, "initialized": true, "start": 257, "end": 583, "defaultInstanceForType": {"id": 1, "name": "mock_ReservedRange"}}], "reservedNameList": ["fall", "country"]}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 945, "serializedSizeAsMessageSet": 631}, "initialized": true, "features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 591, "serializedSizeAsMessageSet": 794}, "initialized": false, "enumType": "ENUM_TYPE_UNKNOWN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_MessageOptions"}}, "fullName": "one", "file": {"proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 928, "serializedSizeAsMessageSet": 521}, "initialized": false, "enumTypeCount": 511, "extensionCount": 307, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 261, "serializedSizeAsMessageSet": 361}, "initialized": true, "features": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}, "serializedSize": 1, "serializedSizeAsMessageSet": 1}, "initialized": true, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 670, "serializedSizeAsMessageSet": 656}, "initialized": false, "features": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {}, "serializedSize": 687, "serializedSizeAsMessageSet": 894}, "initialized": false, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_FileOptions"}}, "messageTypes": [{"id": 1, "name": "mock_Descriptor"}, {"id": 1, "name": "mock_Descriptor"}], "enumTypes": [{"index": 804, "proto": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 674, "serializedSizeAsMessageSet": 435}, "initialized": false, "reservedRangeList": [{"unknownFields": {"mock": true}, "initialized": true, "start": 1, "end": 1, "defaultInstanceForType": {"mock": true}}, {"unknownFields": {"mock": true}, "initialized": true, "start": 1, "end": 1, "defaultInstanceForType": {"mock": true}}], "reservedNameList": ["yes", "law"], "valueList": [{"unknownFields": {"mock": true}, "initialized": true, "options": {"mock": true}, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}, {"unknownFields": {"mock": true}, "initialized": true, "options": {"mock": true}, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}]}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 722, "serializedSizeAsMessageSet": 160}, "initialized": false, "features": {"unknownFields": {"mock": true}, "initialized": true, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {"mock": true}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_EnumOptions"}}, "fullName": "challenge", "file": {"id": 1, "name": "mock_FileDescriptor"}}, {"index": 897, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 108, "serializedSizeAsMessageSet": 895}, "initialized": false, "reservedRangeList": [{"unknownFields": {"mock": true}, "initialized": true, "start": 1, "end": 1, "defaultInstanceForType": {"mock": true}}, {"unknownFields": {"mock": true}, "initialized": true, "start": 1, "end": 1, "defaultInstanceForType": {"mock": true}}], "reservedNameList": ["born"], "valueList": [{"unknownFields": {"mock": true}, "initialized": true, "options": {"mock": true}, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}, {"unknownFields": {"mock": true}, "initialized": true, "options": {"mock": true}, "defaultInstanceForType": {"mock": true}, "parserForType": {"mock": true}}]}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 187, "serializedSizeAsMessageSet": 26}, "initialized": true, "features": {"unknownFields": {"mock": true}, "initialized": true, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {"mock": true}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_EnumOptions"}}, "fullName": "letter", "file": {"id": 1, "name": "mock_FileDescriptor"}}], "services": [{"index": 705, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 991, "serializedSizeAsMessageSet": 398}, "initialized": true, "options": {"unknownFields": {"mock": true}, "initialized": true, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}}, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceDescriptorProto"}, "parserForType": {}}, "options": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 16, "serializedSizeAsMessageSet": 908}, "initialized": true, "features": {"unknownFields": {"mock": true}, "initialized": true, "enumType": "CLOSED", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {"mock": true}}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}}, "fullName": "small", "file": {"id": 1, "name": "mock_FileDescriptor"}}, {"index": 275, "proto": {"unknownFields": {"initialized": false, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 202, "serializedSizeAsMessageSet": 570}, "initialized": true, "options": {"unknownFields": {"mock": true}, "initialized": false, "features": {"mock": true}, "deprecated": true, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}}, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceDescriptorProto"}, "parserForType": {}}, "options": {"unknownFields": {"initialized": true, "defaultInstanceForType": {"id": 1, "name": "mock_UnknownFieldSet"}, "parserForType": {"mock": true}, "serializedSize": 107, "serializedSizeAsMessageSet": 488}, "initialized": false, "features": {"unknownFields": {"mock": true}, "initialized": false, "enumType": "OPEN", "defaultInstanceForType": {"id": 1, "name": "mock_FeatureSet"}, "parserForType": {"mock": true}}, "deprecated": false, "defaultInstanceForType": {"id": 1, "name": "mock_ServiceOptions"}}, "fullName": "far", "file": {"id": 1, "name": "mock_FileDescriptor"}}]}}, "allFields": {}}], "identifierValue": "themselves", "positiveIntValue": 8074};
}

module.exports = {
  generateUninterpretedOption
};
