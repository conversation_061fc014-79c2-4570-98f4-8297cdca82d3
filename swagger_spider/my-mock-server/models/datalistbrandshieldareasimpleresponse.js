
// DataListBrandShieldAreaSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListBrandShieldAreaSimpleResponse模型的模拟数据
 * @returns {DataListBrandShieldAreaSimpleResponse} 模拟数据
 */
function generateDataListBrandShieldAreaSimpleResponse() {
  return {"curPage": 418, "maxPage": 533, "total": 9547, "data": [{"id": 417, "brand": {"id": 753, "name": "term", "url": "young", "avatar": "spring"}, "status": "1", "country": "easy", "mark": "certainly", "createTime": 1753433976}]};
}

module.exports = {
  generateDataListBrandShieldAreaSimpleResponse
};
