
// DataListRulesSanctionsResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListRulesSanctionsResponse模型的模拟数据
 * @returns {DataListRulesSanctionsResponse} 模拟数据
 */
function generateDataListRulesSanctionsResponse() {
  return {"curPage": 496, "maxPage": 839, "total": 4901, "data": [{"id": 499, "rulesId": 451, "status": "1", "sanctionType": "1", "actionClass": "decade", "sanctionConfig": {}, "createTime": 1753433976}, {"id": 727, "rulesId": 755, "status": "1", "sanctionType": "1", "actionClass": "religious", "sanctionConfig": {}, "createTime": 1753433976}, {"id": 810, "rulesId": 139, "status": "1", "sanctionType": "1", "actionClass": "enjoy", "sanctionConfig": {}, "createTime": 1753433976}]};
}

module.exports = {
  generateDataListRulesSanctionsResponse
};
