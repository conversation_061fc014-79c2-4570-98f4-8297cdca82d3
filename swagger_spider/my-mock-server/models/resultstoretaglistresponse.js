
// ResultStoreTagListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultStoreTagListResponse模型的模拟数据
 * @returns {ResultStoreTagListResponse} 模拟数据
 */
function generateResultStoreTagListResponse() {
  return {"code": "pages_type_add_error", "data": {"id": 8, "store": {"id": 270, "name": "build", "url": "particularly", "avatar": "wind"}, "tag": {"id": 560, "name": "appear", "url": "hospital", "avatar": "hotel"}, "createTime": 1753433976}};
}

module.exports = {
  generateResultStoreTagListResponse
};
