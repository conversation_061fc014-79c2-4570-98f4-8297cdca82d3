
// ResultStoreLogsResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultStoreLogsResponse模型的模拟数据
 * @returns {ResultStoreLogsResponse} 模拟数据
 */
function generateResultStoreLogsResponse() {
  return {"code": "schedule_sign_log_batch_add_error", "data": {"id": 799, "store": {"id": 51, "name": "always", "url": "around", "avatar": "respond"}, "staff": {"id": 822, "name": "here", "url": "think", "avatar": "amount"}, "method": "support", "module": "pattern", "action": "thus", "ip": 978, "createTime": 1753433976, "code": 0, "ua": "early"}};
}

module.exports = {
  generateResultStoreLogsResponse
};
