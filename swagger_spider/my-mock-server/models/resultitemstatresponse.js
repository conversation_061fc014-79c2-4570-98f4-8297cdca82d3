
// ResultItemStatResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultItemStatResponse模型的模拟数据
 * @returns {ResultItemStatResponse} 模拟数据
 */
function generateResultItemStatResponse() {
  return {"code": "campaign_custom_url_exists", "data": {"id": 718, "item": {"id": 377, "name": "against", "url": "idea", "avatar": "perform"}, "store": {"id": 368, "name": "draw", "url": "evening", "avatar": "big"}, "visitCount": 907, "ordersCount": 408, "salesAmount": 583, "salesProfit": 998, "updateTime": 1753433976, "itemId": 531, "storeId": 911}};
}

module.exports = {
  generateResultItemStatResponse
};
