
// PostsTagListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成PostsTagListResponse模型的模拟数据
 * @returns {PostsTagListResponse} 模拟数据
 */
function generatePostsTagListResponse() {
  return {"id": 385, "posts": {"id": 638, "name": "treat", "url": "fund", "avatar": "think"}, "tag": {"id": 58, "name": "style", "url": "civil", "avatar": "guy"}, "createTime": 1753433975};
}

module.exports = {
  generatePostsTagListResponse
};
