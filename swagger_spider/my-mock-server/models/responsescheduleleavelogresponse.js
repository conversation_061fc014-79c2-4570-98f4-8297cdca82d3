
// ResponseScheduleLeaveLogResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseScheduleLeaveLogResponse模型的模拟数据
 * @returns {ResponseScheduleLeaveLogResponse} 模拟数据
 */
function generateResponseScheduleLeaveLogResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 275, "schedule": {"id": 440, "name": "sister", "url": "human", "avatar": "wear"}, "manage": {"id": 964, "name": "thus", "url": "that", "avatar": "certainly"}, "status": "1", "statusName": "right", "askTime": "1980-05-29T10:54:15.281514", "leaveTime": "1986-12-14T10:39:25.247748", "backTime": "2001-11-13T03:34:47.230274", "leaveType": "1", "leaveTypeName": "bring"}, "result": {"code": "posts_brand_list_id_not_exists", "data": {"id": 547, "schedule": {"id": 11, "name": "our", "url": "what", "avatar": "price"}, "manage": {"id": 801, "name": "travel", "url": "check", "avatar": "democratic"}, "status": "1", "statusName": "skin", "askTime": "2012-04-05T03:52:52.888402", "leaveTime": "1996-10-22T20:57:50.647797", "backTime": "2002-06-27T07:15:49.486646", "leaveType": "1", "leaveTypeName": "rock"}}, "errMessageOnly": "drug", "successMessage": "return", "errMessage": "information"};
}

module.exports = {
  generateResponseScheduleLeaveLogResponse
};
