
// ResultDataListScheduleCalendarReportDTO 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListScheduleCalendarReportDTO模型的模拟数据
 * @returns {ResultDataListScheduleCalendarReportDTO} 模拟数据
 */
function generateResultDataListScheduleCalendarReportDTO() {
  return {"code": "faq_batch_delete_error", "data": {"curPage": 573, "maxPage": 568, "total": 898, "data": [{"date": "2014-04-30T00:24:14.529082", "schedules": [{"from": "travel", "to": "hour", "manageId": 381, "trueManageId": 868, "scheduleId": 477}, {"from": "financial", "to": "within", "manageId": 997, "trueManageId": 941, "scheduleId": 701}]}, {"date": "1990-10-18T03:55:01.377218", "schedules": [{"from": "capital", "to": "speak", "manageId": 556, "trueManageId": 714, "scheduleId": 963}, {"from": "three", "to": "after", "manageId": 957, "trueManageId": 161, "scheduleId": 117}]}]}};
}

module.exports = {
  generateResultDataListScheduleCalendarReportDTO
};
