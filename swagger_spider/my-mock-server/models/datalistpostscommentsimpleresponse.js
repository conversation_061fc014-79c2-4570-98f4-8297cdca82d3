
// DataListPostsCommentSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListPostsCommentSimpleResponse模型的模拟数据
 * @returns {DataListPostsCommentSimpleResponse} 模拟数据
 */
function generateDataListPostsCommentSimpleResponse() {
  return {"curPage": 618, "maxPage": 798, "total": 7072, "data": [{"id": 499, "posts": {"id": 922, "name": "manager", "url": "hair", "avatar": "friend"}, "user": {"id": 916, "name": "full", "url": "capital", "avatar": "professor"}, "status": "1", "paidStatus": "1", "level": "1", "comment": "table", "createTime": 1753433976}, {"id": 259, "posts": {"id": 240, "name": "sign", "url": "father", "avatar": "discussion"}, "user": {"id": 852, "name": "together", "url": "feel", "avatar": "throw"}, "status": "1", "paidStatus": "1", "level": "1", "comment": "just", "createTime": 1753433976}]};
}

module.exports = {
  generateDataListPostsCommentSimpleResponse
};
