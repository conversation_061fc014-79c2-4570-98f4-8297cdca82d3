
// ResultBrandResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultBrandResponse模型的模拟数据
 * @returns {ResultBrandResponse} 模拟数据
 */
function generateResultBrandResponse() {
  return {"code": "services_update_error", "data": {"id": 921, "parentId": 102, "status": "1", "brandType": "1", "brandLogo": "prevent", "customUrl": "hear", "createTime": 1753433976, "updateTime": 1753433976, "brandLangId": 21, "language": "en_US"}};
}

module.exports = {
  generateResultBrandResponse
};
