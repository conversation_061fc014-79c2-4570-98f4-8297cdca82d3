
// StoreFaqRequest 模型
// 由SwaggerCrawler自动生成

/**
 * 生成StoreFaqRequest模型的模拟数据
 * @returns {StoreFaqRequest} 模拟数据
 */
function generateStoreFaqRequest() {
  return {"storeId": 223, "itemId": 91, "status": "PENDING", "sortIndex": 690, "version": 643, "language": "en_US", "seoKeywords": "special", "seoDescription": "recognize", "question": "ok", "answer": "research"};
}

module.exports = {
  generateStoreFaqRequest
};
