
// ResponseDataListOrdersTicketSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListOrdersTicketSimpleResponse模型的模拟数据
 * @returns {ResponseDataListOrdersTicketSimpleResponse} 模拟数据
 */
function generateResponseDataListOrdersTicketSimpleResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 486, "maxPage": 989, "total": 7430, "data": [{"id": 481, "status": "1", "ordersId": 430, "user": {"id": 74, "name": "political", "url": "public", "avatar": "out"}, "store": {"id": 682, "name": "moment", "url": "wait", "avatar": "leg"}}, {"id": 680, "status": "1", "ordersId": 889, "user": {"id": 464, "name": "attention", "url": "answer", "avatar": "be"}, "store": {"id": 334, "name": "deep", "url": "join", "avatar": "increase"}}]}, "result": {"code": "item_stat_id_not_exists", "data": {"curPage": 321, "maxPage": 660, "total": 9287, "data": [{"id": 351, "status": "1", "ordersId": 593, "user": {"id": 345, "name": "build", "url": "general", "avatar": "keep"}, "store": {"id": 653, "name": "power", "url": "treatment", "avatar": "series"}}]}}, "errMessageOnly": "student", "successMessage": "production", "errMessage": "dog"};
}

module.exports = {
  generateResponseDataListOrdersTicketSimpleResponse
};
