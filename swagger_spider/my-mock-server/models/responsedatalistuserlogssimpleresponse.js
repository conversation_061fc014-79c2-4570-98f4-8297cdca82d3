
// ResponseDataListUserLogsSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListUserLogsSimpleResponse模型的模拟数据
 * @returns {ResponseDataListUserLogsSimpleResponse} 模拟数据
 */
function generateResponseDataListUserLogsSimpleResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 716, "maxPage": 785, "total": 2608, "data": [{"id": 308, "user": {"id": 654, "name": "people", "url": "easy", "avatar": "consumer"}, "method": "establish", "module": "teacher", "action": "eight"}]}, "result": {"code": "remove_fail", "data": {"curPage": 530, "maxPage": 250, "total": 6481, "data": [{"id": 641, "user": {"id": 663, "name": "center", "url": "member", "avatar": "court"}, "method": "benefit", "module": "draw", "action": "local"}]}}, "errMessageOnly": "specific", "successMessage": "factor", "errMessage": "nation"};
}

module.exports = {
  generateResponseDataListUserLogsSimpleResponse
};
