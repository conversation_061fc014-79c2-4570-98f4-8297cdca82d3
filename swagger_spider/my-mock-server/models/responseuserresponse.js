
// ResponseUserResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseUserResponse模型的模拟数据
 * @returns {ResponseUserResponse} 模拟数据
 */
function generateResponseUserResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 89, "username": "citizen", "email": "yourself", "status": 379, "verified": 199, "level": 891, "avatar": "apply", "createTime": 1753433975, "password": "will", "salt": "gun"}, "result": {"code": "user_login_failed", "data": {"id": 95, "username": "run", "email": "describe", "status": 513, "verified": 239, "level": 505, "avatar": "foreign", "createTime": 1753433975, "password": "first", "salt": "win"}}, "errMessageOnly": "scene", "successMessage": "research", "errMessage": "institution"};
}

module.exports = {
  generateResponseUserResponse
};
