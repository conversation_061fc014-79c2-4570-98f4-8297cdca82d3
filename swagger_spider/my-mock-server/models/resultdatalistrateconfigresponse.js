
// ResultDataListRateConfigResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListRateConfigResponse模型的模拟数据
 * @returns {ResultDataListRateConfigResponse} 模拟数据
 */
function generateResultDataListRateConfigResponse() {
  return {"code": "user_email_confirm_failed", "data": {"curPage": 690, "maxPage": 502, "total": 5308, "data": [{"id": 37, "country": "follow", "currency": "fine", "currencyName": "approach", "symbolFloat": 748}]}};
}

module.exports = {
  generateResultDataListRateConfigResponse
};
