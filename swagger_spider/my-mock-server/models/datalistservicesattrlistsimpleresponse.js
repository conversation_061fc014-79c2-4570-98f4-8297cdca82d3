
// DataListServicesAttrListSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListServicesAttrListSimpleResponse模型的模拟数据
 * @returns {DataListServicesAttrListSimpleResponse} 模拟数据
 */
function generateDataListServicesAttrListSimpleResponse() {
  return {"curPage": 235, "maxPage": 334, "total": 6215, "data": [{"id": 431, "services": {"id": 118, "name": "friend", "url": "official", "avatar": "office"}, "attr": {"id": 618, "name": "develop", "url": "late", "avatar": "instead"}, "createTime": 1753433976}, {"id": 456, "services": {"id": 255, "name": "toward", "url": "arm", "avatar": "news"}, "attr": {"id": 317, "name": "accept", "url": "large", "avatar": "task"}, "createTime": 1753433976}]};
}

module.exports = {
  generateDataListServicesAttrListSimpleResponse
};
