
// DataListManageSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListManageSimpleResponse模型的模拟数据
 * @returns {DataListManageSimpleResponse} 模拟数据
 */
function generateDataListManageSimpleResponse() {
  return {"curPage": 392, "maxPage": 36, "total": 2623, "data": [{"id": 112, "status": "1", "roleId": 979, "roleName": "north", "username": "away", "firstName": "before", "middleName": "development", "lastName": "his", "phone": "entire", "email": "PM"}]};
}

module.exports = {
  generateDataListManageSimpleResponse
};
