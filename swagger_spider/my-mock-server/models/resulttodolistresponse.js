
// ResultTodoListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultTodoListResponse模型的模拟数据
 * @returns {ResultTodoListResponse} 模拟数据
 */
function generateResultTodoListResponse() {
  return {"code": "schedule_leave_log_can_not_approve", "data": {"id": 48, "taskType": "1", "status": "1", "manageId": 201, "roleId": 536, "relationType": "couple", "relationId": 364, "prioritySort": 689, "taskName": "no", "summary": "total"}};
}

module.exports = {
  generateResultTodoListResponse
};
