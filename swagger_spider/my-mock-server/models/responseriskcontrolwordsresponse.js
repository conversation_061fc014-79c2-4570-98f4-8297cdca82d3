
// ResponseRiskControlWordsResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseRiskControlWordsResponse模型的模拟数据
 * @returns {ResponseRiskControlWordsResponse} 模拟数据
 */
function generateResponseRiskControlWordsResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 190, "status": "1", "level": "1", "weight": 919, "action": "1", "language": "en_US", "words": "too", "createTime": 1753433976}, "result": {"code": "user_reset_2fa_failed", "data": {"id": 846, "status": "1", "level": "1", "weight": 896, "action": "1", "language": "en_US", "words": "pattern", "createTime": 1753433976}}, "errMessageOnly": "believe", "successMessage": "this", "errMessage": "remain"};
}

module.exports = {
  generateResponseRiskControlWordsResponse
};
