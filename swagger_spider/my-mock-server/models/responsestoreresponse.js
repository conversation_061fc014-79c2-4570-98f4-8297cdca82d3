
// ResponseStoreResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseStoreResponse模型的模拟数据
 * @returns {ResponseStoreResponse} 模拟数据
 */
function generateResponseStoreResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 65, "user": {"id": 171, "name": "show", "url": "safe", "avatar": "marriage"}, "status": "1", "language": "en_US", "name": "remember", "brandList": [{"id": 404, "name": "whose", "url": "sign", "avatar": "green"}, {"id": 336, "name": "daughter", "url": "city", "avatar": "day"}], "tagList": [{"id": 416, "name": "happen", "url": "star", "avatar": "give"}], "servicesList": [{"id": 356, "name": "act", "url": "kitchen", "avatar": "them"}], "logo": "newspaper", "country": "different"}, "result": {"code": "rules_sanctions_add_error", "data": {"id": 163, "user": {"id": 65, "name": "husband", "url": "still", "avatar": "my"}, "status": "1", "language": "en_US", "name": "wait", "brandList": [{"id": 307, "name": "happy", "url": "not", "avatar": "support"}, {"id": 226, "name": "person", "url": "two", "avatar": "push"}, {"id": 580, "name": "threat", "url": "instead", "avatar": "that"}], "tagList": [{"id": 244, "name": "fill", "url": "skin", "avatar": "itself"}], "servicesList": [{"id": 53, "name": "mean", "url": "eye", "avatar": "thousand"}, {"id": 240, "name": "radio", "url": "plant", "avatar": "easy"}, {"id": 56, "name": "find", "url": "what", "avatar": "wonder"}], "logo": "game", "country": "stuff"}}, "errMessageOnly": "environmental", "successMessage": "five", "errMessage": "dream"};
}

module.exports = {
  generateResponseStoreResponse
};
