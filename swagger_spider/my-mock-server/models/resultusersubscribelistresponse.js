
// ResultUserSubscribeListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultUserSubscribeListResponse模型的模拟数据
 * @returns {ResultUserSubscribeListResponse} 模拟数据
 */
function generateResultUserSubscribeListResponse() {
  return {"code": "aftersales_type_delete_error", "data": {"id": 964, "user": {"id": 752, "name": "born", "url": "itself", "avatar": "similar"}, "store": {"id": 81, "name": "also", "url": "model", "avatar": "design"}, "item": {"id": 716, "name": "heart", "url": "yeah", "avatar": "force"}, "noticePrice": 900, "noticeStockNum": 977, "createTime": 1753433975}};
}

module.exports = {
  generateResultUserSubscribeListResponse
};
