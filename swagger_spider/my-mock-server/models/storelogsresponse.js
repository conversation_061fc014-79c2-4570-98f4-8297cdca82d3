
// StoreLogsResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成StoreLogsResponse模型的模拟数据
 * @returns {StoreLogsResponse} 模拟数据
 */
function generateStoreLogsResponse() {
  return {"id": 792, "store": {"id": 457, "name": "discuss", "url": "white", "avatar": "with"}, "staff": {"id": 609, "name": "put", "url": "four", "avatar": "sit"}, "method": "fly", "module": "will", "action": "machine", "ip": 25, "createTime": 1753433976, "code": 0, "ua": "total"};
}

module.exports = {
  generateStoreLogsResponse
};
