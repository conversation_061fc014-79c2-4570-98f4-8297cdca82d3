
// UserSanctionsResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成UserSanctionsResponse模型的模拟数据
 * @returns {UserSanctionsResponse} 模拟数据
 */
function generateUserSanctionsResponse() {
  return {"id": 514, "violationsEventId": 978, "user": {"id": 447, "name": "arm", "url": "make", "avatar": "action"}, "status": "1", "sanctionType": "1", "actionClass": "night", "sanctionData": "community", "sanctionStart": 87, "sanctionEnd": 745, "mark": "system"};
}

module.exports = {
  generateUserSanctionsResponse
};
