
// ResultPlatformHotDataResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultPlatformHotDataResponse模型的模拟数据
 * @returns {ResultPlatformHotDataResponse} 模拟数据
 */
function generateResultPlatformHotDataResponse() {
  return {"code": "brand_delete_error", "data": {"hotItemResponseList": [{"sortIndex": 346, "item": {"id": 738, "name": "increase", "url": "will", "avatar": "training"}, "salesUnitPrice": 457, "totalSalesAmount": 924}, {"sortIndex": 800, "item": {"id": 588, "name": "professor", "url": "room", "avatar": "feeling"}, "salesUnitPrice": 697, "totalSalesAmount": 502}, {"sortIndex": 630, "item": {"id": 492, "name": "fly", "url": "than", "avatar": "long"}, "salesUnitPrice": 146, "totalSalesAmount": 287}], "hotStoreResponseList": [{"sortIndex": 417, "store": {"id": 26, "name": "effort", "url": "source", "avatar": "successful"}, "salesUnitPrice": 32, "totalSalesAmount": 476}, {"sortIndex": 585, "store": {"id": 926, "name": "loss", "url": "direction", "avatar": "daughter"}, "salesUnitPrice": 805, "totalSalesAmount": 214}, {"sortIndex": 852, "store": {"id": 554, "name": "position", "url": "say", "avatar": "save"}, "salesUnitPrice": 824, "totalSalesAmount": 27}], "hotPostsResponseList": [{"sortIndex": 233, "user": {"id": 653, "name": "offer", "url": "save", "avatar": "win"}, "posts": {"id": 495, "name": "song", "url": "cover", "avatar": "thus"}, "likeCount": 673}, {"sortIndex": 666, "user": {"id": 151, "name": "look", "url": "north", "avatar": "to"}, "posts": {"id": 386, "name": "forget", "url": "put", "avatar": "a"}, "likeCount": 202}], "hotDemandResponseList": [{"sortIndex": 659, "user": {"id": 793, "name": "parent", "url": "turn", "avatar": "rule"}, "demand": {"id": 556, "name": "bank", "url": "represent", "avatar": "minute"}, "demandBidCount": 464}]}};
}

module.exports = {
  generateResultPlatformHotDataResponse
};
