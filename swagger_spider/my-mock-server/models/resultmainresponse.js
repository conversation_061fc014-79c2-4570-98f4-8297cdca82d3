
// ResultMainResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultMainResponse模型的模拟数据
 * @returns {ResultMainResponse} 模拟数据
 */
function generateResultMainResponse() {
  return {"code": "manage_error", "data": {"nodeName": "low", "version": {"number": "successful", "buildFlavor": "answer", "buildType": "difficult", "buildHash": "fish", "buildDate": "surface", "luceneVersion": "foreign", "minimumWireCompatibilityVersion": "discuss", "minimumIndexCompatibilityVersion": "together", "snapshot": false}, "clusterName": "company", "clusterUuid": "without", "tagline": "along"}};
}

module.exports = {
  generateResultMainResponse
};
