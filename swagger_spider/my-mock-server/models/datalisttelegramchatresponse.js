
// DataListTelegramChatResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListTelegramChatResponse模型的模拟数据
 * @returns {DataListTelegramChatResponse} 模拟数据
 */
function generateDataListTelegramChatResponse() {
  return {"curPage": 457, "maxPage": 623, "total": 2471, "data": [{"id": 676, "telegramRobot": {"id": 605, "name": "impact", "url": "little", "avatar": "account"}, "status": "1", "username": "reveal", "chatId": 3, "firstName": "campaign", "lastName": "economy", "createTime": **********}, {"id": 482, "telegramRobot": {"id": 15, "name": "near", "url": "sort", "avatar": "nature"}, "status": "1", "username": "value", "chatId": 631, "firstName": "he", "lastName": "current", "createTime": **********}, {"id": 984, "telegramRobot": {"id": 136, "name": "five", "url": "those", "avatar": "among"}, "status": "1", "username": "deal", "chatId": 514, "firstName": "where", "lastName": "scene", "createTime": **********}]};
}

module.exports = {
  generateDataListTelegramChatResponse
};
