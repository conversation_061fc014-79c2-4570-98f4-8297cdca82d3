
// ScheduleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ScheduleResponse模型的模拟数据
 * @returns {ScheduleResponse} 模拟数据
 */
function generateScheduleResponse() {
  return {"id": 951, "status": "1", "statusName": "main", "date": "condition", "from": "form", "to": "agree", "manage": {"id": 5, "name": "able", "url": "institution", "avatar": "question"}, "trueManage": {"id": 438, "name": "activity", "url": "son", "avatar": "face"}, "workStart": "around", "workEnd": "agent"};
}

module.exports = {
  generateScheduleResponse
};
