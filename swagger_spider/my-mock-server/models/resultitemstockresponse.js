
// ResultItemStockResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultItemStockResponse模型的模拟数据
 * @returns {ResultItemStockResponse} 模拟数据
 */
function generateResultItemStockResponse() {
  return {"code": "schedule_handover_log_delete_failed", "data": {"id": 543, "item": {"id": 853, "name": "cost", "url": "table", "avatar": "simple"}, "batchId": 304, "ordersId": 417, "checkStatus": "1", "soldStatus": "1", "deleteStatus": "1", "replacementStatus": "1", "createTime": 1753433976, "checkTime": 1753433976}};
}

module.exports = {
  generateResultItemStockResponse
};
