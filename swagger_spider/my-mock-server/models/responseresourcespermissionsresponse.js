
// ResponseResourcesPermissionsResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseResourcesPermissionsResponse模型的模拟数据
 * @returns {ResponseResourcesPermissionsResponse} 模拟数据
 */
function generateResponseResourcesPermissionsResponse() {
  return {"code": 0, "msg": "success", "data": {"id": 454, "resourcesId": 733, "sortIndex": "0", "showStatus": "1", "permissionName": "and", "fields": ["learn"], "buttons": ["cup"], "urls": ["professor", "station", "television"], "createTime": 1753433976}, "result": {"code": "orders_ticket_message_can_not_read", "data": {"id": 72, "resourcesId": 189, "sortIndex": "0", "showStatus": "1", "permissionName": "heavy", "fields": ["last", "matter"], "buttons": ["election", "ever", "young"], "urls": ["across", "could", "seek"], "createTime": 1753433976}}, "errMessageOnly": "million", "successMessage": "from", "errMessage": "three"};
}

module.exports = {
  generateResponseResourcesPermissionsResponse
};
