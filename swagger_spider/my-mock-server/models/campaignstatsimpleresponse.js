
// CampaignStatSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成CampaignStatSimpleResponse模型的模拟数据
 * @returns {CampaignStatSimpleResponse} 模拟数据
 */
function generateCampaignStatSimpleResponse() {
  return {"id": 474, "campaign": {"id": 184, "name": "across", "url": "clearly", "avatar": "under"}, "visitCount": 576, "ordersCount": 941, "salesAmount": 308, "salesCount": 222, "saveCount": 645, "updateTime": 1753433976};
}

module.exports = {
  generateCampaignStatSimpleResponse
};
