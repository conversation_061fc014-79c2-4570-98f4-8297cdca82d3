// DynamicswaggerResourcesResponse 模型
// 由SwaggerCrawler动态生成

/**
 * 生成DynamicswaggerResourcesResponse模型的模拟数据
 * @returns {DynamicswaggerResourcesResponse} 模拟数据
 */
function generateDynamicswaggerResourcesResponse() {
  return [{"name": "side","url": "condition","swaggerVersion": "better","location": "may"},{"name": "good","url": "senior","swaggerVersion": "will","location": "example"}];
}

module.exports = {
  generateDynamicswaggerResourcesResponse
};
