
// ResponseDataListRulesSanctionsResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListRulesSanctionsResponse模型的模拟数据
 * @returns {ResponseDataListRulesSanctionsResponse} 模拟数据
 */
function generateResponseDataListRulesSanctionsResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 852, "maxPage": 615, "total": 5385, "data": [{"id": 843, "rulesId": 53, "status": "1", "sanctionType": "1", "actionClass": "four"}, {"id": 231, "rulesId": 213, "status": "1", "sanctionType": "1", "actionClass": "soldier"}]}, "result": {"code": "system_config_delete_error", "data": {"curPage": 12, "maxPage": 867, "total": 903, "data": [{"id": 849, "rulesId": 954, "status": "1", "sanctionType": "1", "actionClass": "board"}, {"id": 989, "rulesId": 354, "status": "1", "sanctionType": "1", "actionClass": "particular"}, {"id": 902, "rulesId": 705, "status": "1", "sanctionType": "1", "actionClass": "smile"}]}}, "errMessageOnly": "toward", "successMessage": "movement", "errMessage": "by"};
}

module.exports = {
  generateResponseDataListRulesSanctionsResponse
};
