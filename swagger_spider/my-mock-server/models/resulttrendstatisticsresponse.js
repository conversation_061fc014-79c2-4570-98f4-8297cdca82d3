
// ResultTrendStatisticsResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultTrendStatisticsResponse模型的模拟数据
 * @returns {ResultTrendStatisticsResponse} 模拟数据
 */
function generateResultTrendStatisticsResponse() {
  return {"code": "user_change_password_token_incorrect", "data": {"amountGroupChart": {"labels": ["write", "deep", "town"], "datasets": [{"name": "professor", "points": [486, 507], "fill": true}, {"name": "effort", "points": [330], "fill": false}, {"name": "agree", "points": [129], "fill": true}], "title": "some", "xaxisLabel": "western", "yaxisLabel": "offer"}, "customerAveragePurchaseChart": {"labels": ["list", "number", "commercial"], "datasets": [{"name": "rather", "points": [640, 647], "fill": false}], "title": "number", "xaxisLabel": "cold", "yaxisLabel": "marriage"}, "salesCountGroupChart": {"labels": ["mission"], "datasets": [{"name": "under", "points": [159, 285], "fill": true}], "title": "really", "xaxisLabel": "green", "yaxisLabel": "either"}, "ordersAveragePurchaseChart": {"labels": ["itself", "order"], "datasets": [{"name": "summer", "points": [131, 57], "fill": true}, {"name": "often", "points": [295, 3], "fill": false}, {"name": "administration", "points": [226, 510], "fill": false}], "title": "spring", "xaxisLabel": "career", "yaxisLabel": "throw"}, "newStoreAndItemCountChart": {"labels": ["generation"], "datasets": [{"name": "pull", "points": [393], "fill": true}, {"name": "off", "points": [976, 766], "fill": false}], "title": "court", "xaxisLabel": "possible", "yaxisLabel": "yeah"}, "newUserCountChart": {"labels": ["think", "top"], "datasets": [{"name": "former", "points": [552, 528], "fill": false}], "title": "teacher", "xaxisLabel": "worker", "yaxisLabel": "eye"}, "newPostsAndBlogCountChart": {"labels": ["line"], "datasets": [{"name": "morning", "points": [276], "fill": false}, {"name": "just", "points": [277, 493], "fill": true}, {"name": "agreement", "points": [223], "fill": true}], "title": "able", "xaxisLabel": "interest", "yaxisLabel": "foot"}, "complaintCountChart": {"labels": ["but", "space"], "datasets": [{"name": "look", "points": [539, 936], "fill": true}], "title": "explain", "xaxisLabel": "live", "yaxisLabel": "offer"}}};
}

module.exports = {
  generateResultTrendStatisticsResponse
};
