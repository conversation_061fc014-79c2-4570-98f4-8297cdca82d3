
// ResultOrdersTicketResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultOrdersTicketResponse模型的模拟数据
 * @returns {ResultOrdersTicketResponse} 模拟数据
 */
function generateResultOrdersTicketResponse() {
  return {"code": "whitelist_add_failure", "data": {"id": 784, "status": "1", "ordersId": 646, "user": {"id": 149, "name": "appear", "url": "especially", "avatar": "audience"}, "store": {"id": 1, "name": "quickly", "url": "throw", "avatar": "national"}, "manage": {"id": 425, "name": "impact", "url": "rock", "avatar": "throughout"}, "aftersalesId": 611, "aftersalesTxt": "song", "createTime": 1753433976, "manageLastTime": 1753433976}};
}

module.exports = {
  generateResultOrdersTicketResponse
};
