
// UserKYCCountResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成UserKYCCountResponse模型的模拟数据
 * @returns {UserKYCCountResponse} 模拟数据
 */
function generateUserKYCCountResponse() {
  return {"todayTotalUserKYCCount": 4340, "authenticatedTodayTotalUserKYCCount": 8403, "refuseAuthenticatedTodayTotalUserKYCCount": 9367, "newUserTodayTotalUserKYCCount": 7902};
}

module.exports = {
  generateUserKYCCountResponse
};
