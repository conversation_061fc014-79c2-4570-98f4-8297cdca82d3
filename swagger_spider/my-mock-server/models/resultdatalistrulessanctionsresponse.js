
// ResultDataListRulesSanctionsResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListRulesSanctionsResponse模型的模拟数据
 * @returns {ResultDataListRulesSanctionsResponse} 模拟数据
 */
function generateResultDataListRulesSanctionsResponse() {
  return {"code": "risk_control_words_update_failure", "data": {"curPage": 878, "maxPage": 62, "total": 1236, "data": [{"id": 262, "rulesId": 812, "status": "1", "sanctionType": "1", "actionClass": "middle"}, {"id": 951, "rulesId": 874, "status": "1", "sanctionType": "1", "actionClass": "laugh"}, {"id": 541, "rulesId": 31, "status": "1", "sanctionType": "1", "actionClass": "almost"}]}};
}

module.exports = {
  generateResultDataListRulesSanctionsResponse
};
