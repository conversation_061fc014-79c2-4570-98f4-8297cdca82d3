
// ResultPostsTagListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultPostsTagListResponse模型的模拟数据
 * @returns {ResultPostsTagListResponse} 模拟数据
 */
function generateResultPostsTagListResponse() {
  return {"code": "orders_ticket_message_send_failed", "data": {"id": 842, "posts": {"id": 155, "name": "particular", "url": "for", "avatar": "ask"}, "tag": {"id": 584, "name": "ten", "url": "every", "avatar": "painting"}, "createTime": 1753433976}};
}

module.exports = {
  generateResultPostsTagListResponse
};
