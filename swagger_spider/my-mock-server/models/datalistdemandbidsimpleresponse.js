
// DataListDemandBidSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListDemandBidSimpleResponse模型的模拟数据
 * @returns {DataListDemandBidSimpleResponse} 模拟数据
 */
function generateDataListDemandBidSimpleResponse() {
  return {"curPage": 356, "maxPage": 789, "total": 7472, "data": [{"id": 486, "demand": {"id": 237, "name": "final", "url": "kitchen", "avatar": "item"}, "user": {"id": 304, "name": "join", "url": "character", "avatar": "yourself"}, "store": {"id": 727, "name": "help", "url": "answer", "avatar": "small"}, "status": "1", "bidTime": 1753433976, "bidPrice": 759, "bidPics": ["whatever"]}, {"id": 212, "demand": {"id": 755, "name": "draw", "url": "traditional", "avatar": "nation"}, "user": {"id": 720, "name": "others", "url": "think", "avatar": "another"}, "store": {"id": 37, "name": "answer", "url": "morning", "avatar": "boy"}, "status": "1", "bidTime": 1753433976, "bidPrice": 965, "bidPics": ["its", "certainly", "product"]}]};
}

module.exports = {
  generateDataListDemandBidSimpleResponse
};
