
// ResultDataListItemShowResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListItemShowResponse模型的模拟数据
 * @returns {ResultDataListItemShowResponse} 模拟数据
 */
function generateResultDataListItemShowResponse() {
  return {"code": "attr_name_exists", "data": {"curPage": 834, "maxPage": 824, "total": 2723, "data": [{"id": 697, "item": {"id": 769, "name": "investment", "url": "land", "avatar": "apply"}, "status": "1", "sortIndex": 57, "version": 889}]}};
}

module.exports = {
  generateResultDataListItemShowResponse
};
