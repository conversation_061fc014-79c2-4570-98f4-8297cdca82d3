
// ResultDataListQuickMessageSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListQuickMessageSimpleResponse模型的模拟数据
 * @returns {ResultDataListQuickMessageSimpleResponse} 模拟数据
 */
function generateResultDataListQuickMessageSimpleResponse() {
  return {"code": "orders_ticket_message_type_invalid", "data": {"curPage": 411, "maxPage": 798, "total": 7006, "data": [{"id": 134, "quickMessageTypeId": 697, "quickMessageTypeName": "buy", "manageId": 741, "manageName": "field"}, {"id": 139, "quickMessageTypeId": 288, "quickMessageTypeName": "material", "manageId": 778, "manageName": "strong"}]}};
}

module.exports = {
  generateResultDataListQuickMessageSimpleResponse
};
