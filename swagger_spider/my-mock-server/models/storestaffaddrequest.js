
// StoreStaffAddRequest 模型
// 由SwaggerCrawler自动生成

/**
 * 生成StoreStaffAddRequest模型的模拟数据
 * @returns {StoreStaffAddRequest} 模拟数据
 */
function generateStoreStaffAddRequest() {
  return {"status": "DISABLED", "title": "establish", "nickName": "difficult", "avatar": "explain", "phone": "here", "telegram": "room", "permissions": ["rather"], "email": "yard", "storeId": 566};
}

module.exports = {
  generateStoreStaffAddRequest
};
