
// ResultPostsTypeResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultPostsTypeResponse模型的模拟数据
 * @returns {ResultPostsTypeResponse} 模拟数据
 */
function generateResultPostsTypeResponse() {
  return {"code": "search_search_type_invalid", "data": {"id": 637, "status": "1", "language": "en_US", "typeName": "statement", "customUrl": "spend", "coverPic": "character", "createTime": 1753433976, "parentId": 355, "seoKeywords": "sort", "seoDescription": "else"}};
}

module.exports = {
  generateResultPostsTypeResponse
};
