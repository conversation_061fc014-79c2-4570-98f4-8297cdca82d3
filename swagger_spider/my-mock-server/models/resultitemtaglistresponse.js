
// ResultItemTagListResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultItemTagListResponse模型的模拟数据
 * @returns {ResultItemTagListResponse} 模拟数据
 */
function generateResultItemTagListResponse() {
  return {"code": "vip_update_error", "data": {"id": 356, "item": {"id": 835, "name": "compare", "url": "marriage", "avatar": "down"}, "tag": {"id": 331, "name": "goal", "url": "because", "avatar": "under"}, "createTime": 1753433976}};
}

module.exports = {
  generateResultItemTagListResponse
};
