
// ResponseDataListPaymentDisputeResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListPaymentDisputeResponse模型的模拟数据
 * @returns {ResponseDataListPaymentDisputeResponse} 模拟数据
 */
function generateResponseDataListPaymentDisputeResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 723, "maxPage": 919, "total": 5009, "data": [{"id": 522, "user": {"id": 61, "name": "prepare", "url": "heavy", "avatar": "several"}, "paymentRecordId": 770, "payment": {"id": 613, "name": "quite", "url": "thank", "avatar": "thought"}, "disputeType": "1"}, {"id": 871, "user": {"id": 288, "name": "recognize", "url": "clearly", "avatar": "operation"}, "paymentRecordId": 258, "payment": {"id": 54, "name": "water", "url": "factor", "avatar": "significant"}, "disputeType": "1"}, {"id": 290, "user": {"id": 194, "name": "just", "url": "him", "avatar": "rather"}, "paymentRecordId": 231, "payment": {"id": 333, "name": "per", "url": "gun", "avatar": "plan"}, "disputeType": "1"}]}, "result": {"code": "orders_comment_like_not_null", "data": {"curPage": 297, "maxPage": 439, "total": 4651, "data": [{"id": 271, "user": {"id": 912, "name": "necessary", "url": "way", "avatar": "care"}, "paymentRecordId": 600, "payment": {"id": 770, "name": "test", "url": "with", "avatar": "walk"}, "disputeType": "1"}]}}, "errMessageOnly": "difference", "successMessage": "area", "errMessage": "involve"};
}

module.exports = {
  generateResponseDataListPaymentDisputeResponse
};
