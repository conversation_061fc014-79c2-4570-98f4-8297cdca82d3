
// ResultAftersalesResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultAftersalesResponse模型的模拟数据
 * @returns {ResultAftersalesResponse} 模拟数据
 */
function generateResultAftersalesResponse() {
  return {"code": "orders_ticket_message_can_not_send", "data": {"id": 166, "brand": {"id": 499, "name": "difficult", "url": "ability", "avatar": "career"}, "aftersalesType": {"id": 786, "name": "run", "url": "by", "avatar": "rule"}, "status": "1", "statusName": "in", "createTime": 1753433976, "updateTime": 1753433976, "language": "en_US", "reason": "test"}};
}

module.exports = {
  generateResultAftersalesResponse
};
