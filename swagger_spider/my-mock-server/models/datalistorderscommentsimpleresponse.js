
// DataListOrdersCommentSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成DataListOrdersCommentSimpleResponse模型的模拟数据
 * @returns {DataListOrdersCommentSimpleResponse} 模拟数据
 */
function generateDataListOrdersCommentSimpleResponse() {
  return {"curPage": 321, "maxPage": 534, "total": 4137, "data": [{"id": 849, "user": {"id": 745, "name": "where", "url": "drop", "avatar": "bag"}, "item": {"id": 518, "name": "feel", "url": "brother", "avatar": "sense"}, "ordersId": 925, "store": {"id": 437, "name": "resource", "url": "professor", "avatar": "your"}, "status": "1", "rating": 128, "comment": "travel", "createTime": 1753433976}]};
}

module.exports = {
  generateDataListOrdersCommentSimpleResponse
};
