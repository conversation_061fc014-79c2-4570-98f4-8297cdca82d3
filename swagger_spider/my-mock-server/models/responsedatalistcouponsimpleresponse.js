
// ResponseDataListCouponSimpleResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResponseDataListCouponSimpleResponse模型的模拟数据
 * @returns {ResponseDataListCouponSimpleResponse} 模拟数据
 */
function generateResponseDataListCouponSimpleResponse() {
  return {"code": 0, "msg": "success", "data": {"curPage": 587, "maxPage": 122, "total": 9814, "data": [{"id": 653, "brand": {"id": 845, "name": "night", "url": "Mrs", "avatar": "beyond"}, "store": {"id": 604, "name": "prepare", "url": "anyone", "avatar": "significant"}, "services": {"id": 443, "name": "late", "url": "suffer", "avatar": "off"}, "item": {"id": 618, "name": "save", "url": "friend", "avatar": "leg"}}, {"id": 823, "brand": {"id": 731, "name": "decide", "url": "cut", "avatar": "skin"}, "store": {"id": 629, "name": "by", "url": "of", "avatar": "shake"}, "services": {"id": 24, "name": "discussion", "url": "almost", "avatar": "relate"}, "item": {"id": 677, "name": "dog", "url": "sort", "avatar": "sea"}}, {"id": 601, "brand": {"id": 675, "name": "night", "url": "team", "avatar": "source"}, "store": {"id": 999, "name": "office", "url": "stock", "avatar": "first"}, "services": {"id": 780, "name": "trip", "url": "detail", "avatar": "become"}, "item": {"id": 779, "name": "benefit", "url": "firm", "avatar": "ten"}}]}, "result": {"code": "pages_type_lang_status_invalid", "data": {"curPage": 198, "maxPage": 726, "total": 4299, "data": [{"id": 32, "brand": {"id": 150, "name": "first", "url": "over", "avatar": "only"}, "store": {"id": 483, "name": "though", "url": "pick", "avatar": "assume"}, "services": {"id": 666, "name": "involve", "url": "of", "avatar": "girl"}, "item": {"id": 515, "name": "Congress", "url": "a", "avatar": "contain"}}, {"id": 888, "brand": {"id": 368, "name": "argue", "url": "treatment", "avatar": "opportunity"}, "store": {"id": 147, "name": "heavy", "url": "assume", "avatar": "but"}, "services": {"id": 590, "name": "performance", "url": "single", "avatar": "management"}, "item": {"id": 833, "name": "close", "url": "free", "avatar": "follow"}}]}}, "errMessageOnly": "within", "successMessage": "group", "errMessage": "result"};
}

module.exports = {
  generateResponseDataListCouponSimpleResponse
};
