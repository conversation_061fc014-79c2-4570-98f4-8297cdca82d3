
// ItemResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ItemResponse模型的模拟数据
 * @returns {ItemResponse} 模拟数据
 */
function generateItemResponse() {
  return {"id": 800, "user": {"id": 500, "name": "identify", "url": "indeed", "avatar": "visit"}, "store": {"id": 804, "name": "risk", "url": "attack", "avatar": "return"}, "brand": {"id": 40, "name": "region", "url": "couple", "avatar": "house"}, "services": {"id": 825, "name": "rock", "url": "author", "avatar": "do"}, "tagList": [{"id": 841, "name": "focus", "url": "good", "avatar": "teach"}, {"id": 236, "name": "message", "url": "score", "avatar": "process"}], "attrList": [{"attrId": 894, "attrName": "better", "attrValueId": 556, "attrValueName": "fire"}, {"attrId": 571, "attrName": "culture", "attrValueId": 204, "attrValueName": "stand"}], "status": "1", "language": "en_US", "name": "detail"};
}

module.exports = {
  generateItemResponse
};
