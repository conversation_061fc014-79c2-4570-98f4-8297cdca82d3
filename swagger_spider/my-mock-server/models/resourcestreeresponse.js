
// ResourcesTreeResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResourcesTreeResponse模型的模拟数据
 * @returns {ResourcesTreeResponse} 模拟数据
 */
function generateResourcesTreeResponse() {
  return {"id": 912, "parentId": 607, "level": "1", "status": "1", "sortIndex": 181, "showStatus": "1", "resourcesKey": "bring", "menuName": "suddenly", "menuPath": "evidence"};
}

module.exports = {
  generateResourcesTreeResponse
};
