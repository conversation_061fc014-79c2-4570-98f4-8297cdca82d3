
// ResultDataListTelegramRobotResponse 模型
// 由SwaggerCrawler自动生成

/**
 * 生成ResultDataListTelegramRobotResponse模型的模拟数据
 * @returns {ResultDataListTelegramRobotResponse} 模拟数据
 */
function generateResultDataListTelegramRobotResponse() {
  return {"code": "orders_comment_update_failed", "data": {"curPage": 875, "maxPage": 802, "total": 9733, "data": [{"id": 648, "status": "1", "name": "interesting", "botName": "provide", "username": "someone"}, {"id": 146, "status": "1", "name": "impact", "botName": "away", "username": "throughout"}, {"id": 602, "status": "1", "name": "trade", "botName": "issue", "username": "health"}]}};
}

module.exports = {
  generateResultDataListTelegramRobotResponse
};
