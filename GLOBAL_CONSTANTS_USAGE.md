# 全局枚举配置管理系统使用指南

## 概述

全局枚举配置管理系统提供了一个统一的方式来管理和使用系统中的枚举配置。系统会在应用启动时自动从 `/basic/constant` 接口获取枚举数据，并缓存到 localStorage 中以提升性能。

## 特性

- ✅ **自动缓存**: localStorage 自动管理，提升性能
- ✅ **智能更新**: 启动时从缓存加载，异步获取最新数据
- ✅ **便捷查询**: 支持按 ID、Key 查询，获取显示名称
- ✅ **组件集成**: 直接适配 Ant Design Select 等组件
- ✅ **类型安全**: 完整的错误处理和状态管理
- ✅ **性能优化**: 避免重复请求，内存+localStorage 双重缓存

## 数据结构

接口返回的数据结构：

```json
{
  "code": 0,
  "msg": "成功",
  "data": {
    "User.Verified": [
      {
        "id": 0,
        "key": "UNVERIFIED",
        "name": "未验证"
      },
      {
        "id": 1,
        "key": "VERIFIED",
        "name": "已验证"
      }
    ],
    "Order.Status": [
      {
        "id": 0,
        "key": "PENDING",
        "name": "待处理"
      }
    ]
  }
}
```

## 基本使用

### 1. 导入 Hook

```javascript
import { useGlobalConstants } from '@/hooks/useGlobalConstants';
```

### 2. 在组件中使用

```javascript
function MyComponent() {
  const {
    getSelectOptions,
    getEnumName,
    loading,
    refreshConstants
  } = useGlobalConstants();

  // 获取用户验证状态的选项
  const verifiedOptions = getSelectOptions('User.Verified');
  
  // 显示状态名称
  const statusName = getEnumName('User.Verified', 1); // "已验证"
  
  return (
    <div>
      <Select 
        options={verifiedOptions}
        loading={loading}
        placeholder="选择验证状态"
      />
      <Button onClick={refreshConstants}>刷新配置</Button>
    </div>
  );
}
```

## API 参考

### useGlobalConstants Hook

#### 状态属性

- `constants` - 所有枚举数据
- `loading` - 加载状态
- `error` - 错误信息
- `initialized` - 是否已初始化
- `lastUpdated` - 最后更新时间

#### 操作方法

- `initConstants()` - 初始化（从缓存加载）
- `fetchConstants()` - 获取最新数据
- `refreshConstants()` - 刷新配置
- `clearCache()` - 清除缓存

#### 查询方法

- `getEnumOptions(enumKey)` - 获取枚举的所有选项
- `getEnumById(enumKey, id)` - 根据 ID 获取枚举项
- `getEnumByKey(enumKey, key)` - 根据 Key 获取枚举项
- `getEnumName(enumKey, idOrKey)` - 获取枚举项的显示名称
- `hasEnum(enumKey, idOrKey)` - 检查枚举值是否存在
- `getEnumMap(enumKey)` - 获取键值对映射 (key -> name)
- `getEnumIdMap(enumKey)` - 获取 ID 值对映射 (id -> name)
- `getSelectOptions(enumKey)` - 获取 Ant Design Select 选项 (使用 id)
- `getSelectOptionsByKey(enumKey)` - 获取 Ant Design Select 选项 (使用 key)
- `getAllConstants()` - 获取所有枚举配置
- `getEnumKeys()` - 获取所有枚举的键名

## 使用场景

### 1. 表单中的下拉选择

```javascript
<Form.Item label="验证状态" name="verified">
  <Select
    placeholder="选择验证状态"
    options={getSelectOptions('User.Verified')}
    loading={loading}
  />
</Form.Item>
```

### 2. 表格中显示枚举值

```javascript
const columns = [
  {
    title: '验证状态',
    dataIndex: 'verified',
    render: (value) => {
      const name = getEnumName('User.Verified', value);
      const color = value === 1 ? 'green' : 'orange';
      return <Tag color={color}>{name}</Tag>;
    }
  }
];
```

### 3. 使用 Key 作为值

```javascript
<Select
  placeholder="选择状态 (使用 key)"
  options={getSelectOptionsByKey('User.Verified')}
  onChange={(key) => {
    console.log('选择的 key:', key);
    console.log('对应的名称:', getEnumName('User.Verified', key));
  }}
/>
```

## 测试页面

系统提供了两个测试页面：

1. **功能测试页面**: `/global-constants-test`
   - 展示系统的基本功能和状态
   - 查看所有枚举配置
   - 测试刷新功能

2. **使用示例页面**: `/global-constants-usage`
   - 展示实际业务场景中的使用方法
   - 表单、表格等组件的集成示例
   - 最佳实践演示

## 注意事项

1. **自动初始化**: 系统会在应用启动时自动初始化，无需手动调用
2. **缓存策略**: 数据会缓存到 localStorage，提升性能
3. **错误处理**: 当接口请求失败时，系统会使用缓存数据
4. **性能优化**: 避免在组件中频繁调用查询方法，建议使用 useMemo 缓存结果

## 扩展

如果需要添加新的枚举类型，只需要在后端 `/basic/constant` 接口中添加相应的数据即可，前端会自动识别和使用。
