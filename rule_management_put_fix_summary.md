# 规则管理页面PUT请求参数传递方式修复总结

## 修复概述

根据 `augment_rules/check_page_complete/fix_page_uncomplete.md` 中的 **4.1 PUT请求参数传递方式验证** 要求，对 `system-settings/system-config/rule-management` 页面的PUT请求进行了修复。

## 发现的问题

### 1. `/rules/{rulesId}` PUT请求问题
**问题描述**：根据swagger定义，该API需要两个必填的query参数：
- `rulesLangId` (integer, int32)
- `rulesSanctionsId` (integer, int32)

**原始实现**：只传递了request body，缺少必需的query参数
```javascript
// 修复前
updateRule: (rulesId, ruleData) => {
    return api.put(`/rules/${rulesId}`, ruleData);
}
```

### 2. `/rules/batch/status` PUT请求问题
**问题描述**：根据swagger定义，该API的参数应该通过query参数传递：
- `ids` (array of integers) - 通过query参数传递
- `status` (string) - 通过query参数传递

**原始实现**：错误地通过request body传递参数
```javascript
// 修复前
batchUpdateRulesStatus: (ids, status) => {
    return api.put('/rules/batch/status', {
        ids: ids,
        status: status.toString()
    });
}
```

## 修复方案

### 1. 安装依赖
安装 `qs` 库用于处理数组参数的序列化：
```bash
npm install qs
```

### 2. 修复 `src/services/ruleManagementService.js`

#### 2.1 导入qs库
```javascript
import qs from 'qs';
```

#### 2.2 修复updateRule方法
```javascript
// 修复后
updateRule: (rulesId, ruleData, rulesLangId = 1, rulesSanctionsId = 1) => {
    // 根据swagger定义，需要通过query参数传递rulesLangId和rulesSanctionsId
    return api.put(`/rules/${rulesId}`, ruleData, {
        params: {
            rulesLangId,
            rulesSanctionsId
        }
    });
}
```

#### 2.3 修复batchUpdateRulesStatus方法
```javascript
// 修复后
batchUpdateRulesStatus: (ids, status) => {
    // 根据swagger定义，ids和status都应该通过query参数传递
    return api.put('/rules/batch/status', null, {
        params: { ids, status: status.toString() },
        paramsSerializer: params => qs.stringify(params, { arrayFormat: 'repeat' })
    });
}
```

### 3. 修复 `src/redux/ruleManagementPage/ruleManagementPageSlice.js`

#### 3.1 更新updateRule异步thunk
```javascript
// 修复后
export const updateRule = createAsyncThunk(
    'ruleManagement/updateRule',
    async ({ rulesId, ruleData, rulesLangId = 1, rulesSanctionsId = 1 }, { rejectWithValue }) => {
        try {
            await RuleManagementService.updateRule(rulesId, ruleData, rulesLangId, rulesSanctionsId);
            return { id: rulesId, ...ruleData };
        } catch (error) {
            return rejectWithValue(error.response?.data || { message: error.message });
        }
    }
);
```

#### 3.2 修复状态更新逻辑
```javascript
// 修复后
.addCase(batchUpdateRulesStatus.fulfilled, (state, action) => {
    state.loading = false;
    const { ids, status } = action.payload;
    state.rulesList = state.rulesList.map(rule =>
        ids.includes(rule.id) ? { ...rule, 状态: status === "1" || status === 1 ? '启用' : '禁用' } : rule
    );
})
```

## 修复效果

### 1. 更新规则请求格式
**修复前**：
```
PUT /rules/1
Body: { status: "1", rulesType: "1", ... }
```

**修复后**：
```
PUT /rules/1?rulesLangId=1&rulesSanctionsId=1
Body: { status: "1", rulesType: "1", ... }
```

### 2. 批量更新状态请求格式
**修复前**：
```
PUT /rules/batch/status
Body: { ids: [1, 2, 3], status: "1" }
```

**修复后**：
```
PUT /rules/batch/status?ids=1&ids=2&ids=3&status=1
Body: null
```

## 验证方法

1. 使用提供的测试文件 `test_rule_management_api.js`
2. 在浏览器开发者工具的Network标签页中检查实际发送的HTTP请求
3. 确认请求格式与swagger文档定义一致

## 符合规范

此修复完全符合 `augment_rules/check_page_complete/fix_page_uncomplete.md` 中 **4.1 PUT请求参数传递方式验证** 的要求：

✅ 对比了控制器中的API定义，确认参数传递方式
✅ 修正了前端service层代码中axios调用的参数配置
✅ 使用了qs.stringify处理数组参数，采用重复参数名的形式
✅ 确保前端传递的参数与后端接口定义完全匹配
