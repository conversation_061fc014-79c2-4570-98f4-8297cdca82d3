# Augment AI规则：Modal窗口转PageTabs标签页转换

## 规则概述
当用户要求将页面中的Modal窗口（添加、编辑、批量操作等）转换为PageTabs标签页时，严格按照以下规则执行转换。此规则基于SystemSettings.jsx的成功实现（git提交f357a93到27d4061）。

## 执行条件
- 用户明确要求将Modal转换为PageTabs
- 页面包含添加、编辑、批量添加、批量编辑等Modal窗口
- 需要应用PageTabs组件进行统一的标签页管理
- 无需执行npm start、npm run等命令进行测试

## 执行步骤

### 步骤1：分析现有代码结构
1. 使用codebase-retrieval工具分析目标页面的完整结构
2. 识别所有Modal相关的导入、状态、函数和JSX
3. 确定数据获取函数名、Redux actions、表单字段等关键信息
4. 分析是否存在批量操作功能

### 步骤2：依赖导入变更
**必须移除：**
```javascript
import { Modal, Form } from 'antd';
```

**必须添加：**
```javascript
import PageTabs from '@/components/PageTabs/PageTabs';
import AddXxxComponent from './AddXxxComponent'; // 根据业务命名
import { usePageTabs } from '@/hooks/usePageTabs';
```

### 步骤3：状态管理重构
**必须移除的状态：**
```javascript
const [isModalVisible, setIsModalVisible] = useState(false);
const [editingRecord, setEditingRecord] = useState(null);
const [form] = Form.useForm();
```

**必须替换为usePageTabs：**
```javascript
const {
  activeTab, editingRecord, tabPanes,
  handleAdd, handleEdit, handleTabChange, handleTabEdit,
  handleSaveSuccess, handleCancel, isListTab,
} = usePageTabs({
  listTabLabel: '确定的页面名称', // 必须根据实际页面确定
  tabTypes: {
    add: {
      label: '添加XXX', // 必须根据业务确定
      prefix: 'add'
    },
    edit: {
      label: '编辑',
      prefix: 'edit',
      getLabelFn: (record) => `编辑XXX - ${record.字段名}` // 必须分析record结构确定字段
    }
    // 如果分析发现有批量操作，必须添加对应配置
  },
  dataList: 实际的数据列表变量名, // 必须从代码中确定
  onSaveSuccess: () => {
    实际的数据获取函数名(); // 必须从代码中确定
  },
});
```

### 步骤4：清理事件处理函数
**必须完全移除的函数：**
- 所有自定义的handleAdd函数
- 所有自定义的handleEdit函数
- 所有handleFormSubmit或类似的表单提交函数
- 所有setIsModalVisible相关的函数

**注意：** handleAdd和handleEdit现在由usePageTabs提供，不需要自定义

**如果发现批量操作，必须添加：**
```javascript
const handleBatchAdd = () => {
  return createTab('batchAdd');
};

const handleBatchEdit = () => {
  if (selectedRowKeys.length === 0) {
    message.warning("请选择要编辑的项目");
    return;
  }
  return createTab('batchEdit');
};
```

### 步骤5：重构页面结构
**必须完全替换return语句：**

1. **移除原有的最外层容器**（通常是div或Fragment）
2. **移除页面标题**（Title组件，因为标签页会显示标题）
3. **移除所有Modal相关的JSX**
4. **用以下结构完全替换：**

```javascript
return (
  <Card style={{ backgroundColor: '#fff' }}>
    {/* 页面标签栏 - 必须包含 */}
    <div className="page-tabs-wrapper">
      <PageTabs
        activeKey={activeTab}
        onChange={handleTabChange}
        onEdit={handleTabEdit}
        items={tabPanes}
        type="editable-card"
      />
    </div>

    {/* 条件渲染 - 必须使用isListTab判断 */}
    {isListTab ? (
      <>
        {/* 保留原有的筛选、按钮、表格等，但要包装在Card中 */}
        <Card style={{ marginBottom: 16 }}>
          {/* 原有的筛选条件 */}
        </Card>
        <Card style={{ marginBottom: 16 }}>
          {/* 原有的操作按钮 */}
        </Card>
        <Card>
          {/* 原有的数据表格 */}
        </Card>
      </>
    ) : (
      /* 新的添加/编辑组件 */
      <AddXxxComponent
        editingRecord={activeTab.startsWith('add-') ? null : editingRecord}
        onSave={handleSaveSuccess}
        onCancel={handleCancel}
      />
    )}
  </Card>
);
```

### 步骤6：创建独立的添加/编辑组件
**必须创建新文件：** 在同级目录下创建 `AddXxxComponent.jsx`

**组件结构必须遵循以下模板：**
```javascript
import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Card, Form, Input, Button, Space, Typography, message } from 'antd';
import { SaveOutlined, UndoOutlined } from '@ant-design/icons';
// 必须导入对应的Redux actions

const { Title } = Typography;

function AddXxxComponent({ editingRecord, onSave, onCancel }) {
  const dispatch = useDispatch();
  const [form] = Form.useForm();

  useEffect(() => {
    if (editingRecord) {
      form.setFieldsValue({
        // 必须根据原Modal中的form.setFieldsValue确定字段
      });
    } else {
      form.resetFields();
    }
  }, [editingRecord, form]);

  const handleFormSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (editingRecord) {
        // 必须使用原代码中的更新action
        await dispatch(原更新Action({
          id: editingRecord.id,
          ...values
        })).unwrap();
        message.success('更新成功');
      } else {
        // 必须使用原代码中的添加action
        await dispatch(原添加Action(values)).unwrap();
        message.success('添加成功');
        form.resetFields();
      }

      onSave && onSave();
    } catch (error) {
      console.error('操作失败:', error);
      message.error(editingRecord ? '更新失败' : '添加失败');
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel && onCancel();
  };

  return (
    <div>
      <Title level={3}>
        {editingRecord ? '编辑XXX' : '添加XXX'}
      </Title>
      <Card>
        <Form form={form} layout="vertical" style={{ maxWidth: 600 }}>
          {/* 必须复制原Modal中的所有Form.Item */}
          <Form.Item>
            <Space>
              <Button type="primary" onClick={handleFormSubmit} icon={<SaveOutlined />}>
                保存
              </Button>
              <Button onClick={handleCancel} icon={<UndoOutlined />}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
}

export default AddXxxComponent;
```

### 步骤7：更新操作按钮
**必须找到所有触发Modal的按钮并更新：**

```javascript
// 原来的按钮（必须移除onClick中的Modal逻辑）：
<Button onClick={() => setIsModalVisible(true)}>添加</Button>
<Button onClick={() => handleEdit(record)}>编辑</Button>

// 必须替换为：
<Button onClick={handleAdd}>添加</Button>
<Button onClick={() => handleEdit(record)}>编辑</Button>

// 如果发现批量操作按钮，必须添加：
<Button onClick={handleBatchAdd}>批量添加</Button>
<Button onClick={handleBatchEdit}>批量编辑</Button>
```

## 执行要求

### 必须遵循的原则
1. **完整性**：必须移除所有Modal相关代码，不能留下任何残余
2. **准确性**：所有变量名、函数名、字段名必须与原代码完全一致
3. **功能性**：转换后的功能必须与原Modal功能完全相同
4. **一致性**：必须严格按照SystemSettings.jsx的模式执行

### 执行前必须做的检查
1. 使用codebase-retrieval详细分析目标文件的完整结构
2. 确定所有需要的变量名、函数名、Redux actions
3. 分析表单字段结构和验证规则
4. 确认是否存在批量操作功能

### 执行后必须做的验证
1. 确保没有任何Modal相关的导入和代码残留
2. 确保所有变量名和函数名正确对应
3. 确保新创建的AddXxxComponent包含所有原有的表单字段和验证规则
4. 确保页面结构完全符合模板要求

## 错误处理规则

### 如果遇到以下情况，必须停止并询问用户：
1. 无法确定数据获取函数名
2. 无法确定Redux actions的准确名称
3. 表单字段结构过于复杂或包含特殊组件
4. 存在多个Modal且功能差异较大
5. 页面结构与常规CRUD页面差异较大

### 常见问题的处理方式：
1. **找不到editingRecord的字段**：使用record.id作为默认标识
2. **Redux action名称不确定**：查找slice文件中的对应action
3. **表单验证规则复杂**：完全复制原Modal中的rules配置
4. **特殊的表单组件**：保持原有的组件类型和配置

## 质量保证

### 转换完成后必须验证：
1. **编译检查**：确保没有语法错误和导入错误
2. **功能检查**：所有原有功能都能正常工作
3. **UI检查**：页面布局和交互符合预期
4. **数据流检查**：数据的增删改查流程正常

### 如果验证失败：
1. 立即回滚到转换前的状态
2. 分析失败原因
3. 重新执行转换流程
4. 如果多次失败，停止并报告问题

## 总结

这是一个严格的转换规则，必须：
1. **严格按照步骤执行**，不能跳过任何步骤
2. **完全移除Modal相关代码**，不能有任何残留
3. **准确复制所有业务逻辑**，确保功能一致
4. **遵循SystemSettings.jsx的模式**，确保风格统一
5. **进行充分的验证**，确保转换质量
