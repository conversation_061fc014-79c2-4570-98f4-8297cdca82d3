# Augment AI快速参考：PageTabs转换

## 执行前检查
- [ ] 使用codebase-retrieval分析目标文件完整结构
- [ ] 确定数据获取函数名、Redux actions、表单字段
- [ ] 识别是否有批量操作功能

## 必须执行的转换步骤

### 步骤1：导入变更
```javascript
// 必须移除
import { Modal, Form } from 'antd';

// 必须添加（根据实际业务调整组件名）
import PageTabs from '@/components/PageTabs/PageTabs';
import AddXxxComponent from './AddXxxComponent';
import { usePageTabs } from '@/hooks/usePageTabs';
```

### 步骤2：状态替换
```javascript
// 必须完全移除
const [isModalVisible, setIsModalVisible] = useState(false);
const [editingRecord, setEditingRecord] = useState(null);
const [form] = Form.useForm();

// 必须替换为（所有变量名必须与原代码一致）
const {
  activeTab, editingRecord, tabPanes,
  handleAdd, handleEdit, handleTabChange, handleTabEdit,
  handleSaveSuccess, handleCancel, isListTab,
} = usePageTabs({
  listTabLabel: '实际页面名称', // 必须确定
  tabTypes: {
    add: { label: '添加XXX', prefix: 'add' }, // 必须确定业务名称
    edit: {
      label: '编辑',
      prefix: 'edit',
      getLabelFn: (record) => `编辑XXX - ${record.实际字段名}` // 必须分析确定
    }
    // 如果有批量操作必须添加对应配置
  },
  dataList: 实际数据列表变量名, // 必须从代码确定
  onSaveSuccess: () => 实际数据获取函数名(), // 必须从代码确定
});
```

### 步骤3：页面结构重构
```javascript
// 必须完全替换return语句，移除原有div/Title等
return (
  <Card style={{ backgroundColor: '#fff' }}>
    {/* 必须包含的标签栏 */}
    <div className="page-tabs-wrapper">
      <PageTabs
        activeKey={activeTab}
        onChange={handleTabChange}
        onEdit={handleTabEdit}
        items={tabPanes}
        type="editable-card"
      />
    </div>

    {/* 必须使用isListTab条件渲染 */}
    {isListTab ? (
      <>
        {/* 保留原有内容但包装在Card中，移除Title */}
        <Card style={{ marginBottom: 16 }}>
          {/* 原有筛选条件 */}
        </Card>
        <Card style={{ marginBottom: 16 }}>
          {/* 原有操作按钮 */}
        </Card>
        <Card>
          {/* 原有数据表格 */}
        </Card>
      </>
    ) : (
      <AddXxxComponent
        editingRecord={activeTab.startsWith('add-') ? null : editingRecord}
        onSave={handleSaveSuccess}
        onCancel={handleCancel}
      />
    )}
  </Card>
);
```

### 步骤4：创建AddXxxComponent
**必须在同级目录创建新文件，严格按照此模板：**
```javascript
import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { Card, Form, Input, Button, Space, Typography, message } from 'antd';
import { SaveOutlined, UndoOutlined } from '@ant-design/icons';
// 必须导入原代码中的Redux actions

const { Title } = Typography;

function AddXxxComponent({ editingRecord, onSave, onCancel }) {
  const dispatch = useDispatch();
  const [form] = Form.useForm();

  useEffect(() => {
    if (editingRecord) {
      form.setFieldsValue({
        // 必须根据原Modal中的setFieldsValue确定所有字段
      });
    } else {
      form.resetFields();
    }
  }, [editingRecord, form]);

  const handleFormSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (editingRecord) {
        // 必须使用原代码中的确切action名称
        await dispatch(原更新Action({ id: editingRecord.id, ...values })).unwrap();
        message.success('更新成功');
      } else {
        // 必须使用原代码中的确切action名称
        await dispatch(原添加Action(values)).unwrap();
        message.success('添加成功');
        form.resetFields();
      }

      onSave && onSave();
    } catch (error) {
      console.error('操作失败:', error);
      message.error(editingRecord ? '更新失败' : '添加失败');
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel && onCancel();
  };

  return (
    <div>
      <Title level={3}>
        {editingRecord ? '编辑XXX' : '添加XXX'}
      </Title>
      <Card>
        <Form form={form} layout="vertical" style={{ maxWidth: 600 }}>
          {/* 必须完全复制原Modal中的所有Form.Item及其rules */}
          <Form.Item>
            <Space>
              <Button type="primary" onClick={handleFormSubmit} icon={<SaveOutlined />}>
                保存
              </Button>
              <Button onClick={handleCancel} icon={<UndoOutlined />}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
}

export default AddXxxComponent;
```

## 执行后验证清单

### 必须检查项（全部通过才算成功）
- [ ] 完全移除所有Modal相关导入、状态、函数、JSX
- [ ] 正确添加PageTabs相关导入
- [ ] usePageTabs配置中所有变量名与原代码一致
- [ ] 页面结构完全符合模板要求
- [ ] 创建的AddXxxComponent包含所有原表单字段和验证规则
- [ ] 所有操作按钮正确调用新的处理函数
- [ ] 编译无错误，功能正常

### 如果验证失败
- 立即停止并报告具体问题
- 不要尝试修复，而是重新分析和执行
