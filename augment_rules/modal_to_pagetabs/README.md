# Augment AI执行规则文档

本目录包含Augment AI执行特定任务时必须遵循的严格规则和指导原则。

## 文件说明

### modal-to-pagetabs-conversion-rules.md
**Augment AI规则：Modal窗口转PageTabs标签页转换**

这是Augment AI执行Modal到PageTabs转换任务时必须严格遵循的详细规则文档。

**执行条件：**
- 用户明确要求将Modal转换为PageTabs
- 页面包含添加、编辑、批量操作等Modal窗口
- 需要应用统一的PageTabs标签页管理
- 无需执行npm start、npm run等命令进行测试

**规则内容：**
- 7个必须执行的转换步骤
- 执行前后的检查要求
- 错误处理和质量保证规则
- 基于SystemSettings.jsx成功实现的标准模式

### pagetabs-quick-reference.md
**Augment AI快速参考：PageTabs转换**

这是Augment AI执行转换时的快速参考文档，包含关键步骤和验证清单。

**使用场景：**
- 转换过程中的快速查阅
- 关键代码模板的准确复制
- 执行后的验证检查

**参考内容：**
- 4个核心转换步骤
- 必须遵循的代码模板
- 执行后验证清单

## AI执行原则

### 严格性原则
- **必须完全按照规则执行**，不允许任何偏差或简化
- **所有变量名、函数名必须与原代码完全一致**
- **不能遗漏任何步骤或检查项**

### 准确性原则
- **执行前必须使用codebase-retrieval详细分析目标代码**
- **所有替换的内容必须基于实际代码结构**
- **不能假设或猜测任何变量名或函数名**

### 完整性原则
- **必须完全移除所有Modal相关代码**
- **必须创建完整的AddXxxComponent组件**
- **必须保持所有原有功能的完整性**

### 质量保证原则
- **执行后必须进行完整的验证检查**
- **如果验证失败必须立即停止并报告问题**
- **不允许部分完成或妥协的转换结果**

## 标准实现参考

这些规则基于SystemSettings.jsx的成功转换实现：
- **转换前**：git提交f357a93（Modal窗口实现）
- **转换后**：git提交27d4061（PageTabs标签页实现）

## 核心依赖组件

AI执行转换时必须使用的组件：
- `@/components/PageTabs/PageTabs`：通用标签页组件
- `@/hooks/usePageTabs`：标签页状态管理Hook

## 执行失败处理

如果AI在执行过程中遇到以下情况，必须停止并询问用户：
1. 无法确定关键变量名或函数名
2. 代码结构与标准模式差异过大
3. 存在复杂的特殊逻辑无法准确转换
4. 验证检查未通过

## 规则更新

- 2025-07-10：创建AI执行规则文档
- 基于SystemSettings.jsx成功转换经验制定严格执行标准

**重要提醒：这些规则是为Augment AI制定的执行标准，必须严格遵循，不允许任何形式的简化或变通。**
