# 页面综合改进任务 - Augment通用规则

## 任务概述
这是一个系统性的页面改进任务，按照固定顺序执行三个独立的改进步骤，确保页面功能完整性、用户体验和代码质量的全面提升。

## 前置条件
- 网站已经运行在 `http://localhost:3000`，无需再启动服务
- 已知页面路由格式（例如：`system-settings/system-config/system-settings`）
- 具备完整的项目文件结构和相关配置文件

## 执行顺序（严格按顺序执行）

### 第一阶段：页面功能完整性修复
**参考规则文件**：`@/augment_rules/check_page_complete/fix_page_uncomplete.md`

**执行内容**：
1. **定位页面文件**：根据路由找到对应的React组件文件
2. **修复service文件中的GET请求参数格式**：确保参数用`{}`包裹成对象形式
3. **定位对应的控制器文件**：找到所有相关的API控制器文件
4. **修复API参数不匹配问题**：确保前端请求与后端接口定义完全匹配
5. **为删除功能添加加载状态展示**：实现覆盖式加载效果
6. **修复日期范围选择器空值访问错误**：添加空值检查
7. **为添加和编辑操作添加等待状态**：防止重复提交
8. **修复标签页切换时表单数据清空问题**：实现表单数据持久化

**完成标准**：
- 所有API请求参数格式正确
- 所有用户交互都有适当的加载状态
- 表单数据在标签页切换时不会丢失
- 日期选择器不会出现空值访问错误

### 第二阶段：Modal.confirm批量操作升级
**参考规则文件**：`@/augment_rules/modal_improvements/modal-confirm-to-modal-upgrade.md`

**执行内容**：
1. **识别目标代码模式**：查找使用`Modal.confirm`实现的批量操作
2. **添加状态变量**：为Modal控制添加必要的状态管理
3. **添加必要的导入**：确保导入了`Alert`等组件
4. **重构处理函数**：将`Modal.confirm`拆分为触发、确认、取消三个函数
5. **添加Modal组件**：使用常规`Modal`组件替换`Modal.confirm`

**完成标准**：
- 所有批量操作使用常规Modal组件
- 提供清晰的操作界面和用户反馈
- 显示选中项目数量和操作确认提示
- 保持所有业务逻辑不变

### 第三阶段：枚举值优化检查
**参考规则文件**：`@/augment_rules/check_page_complete/check_enum.md`

**执行内容**：
1. **分析页面业务逻辑**：识别硬编码枚举值、下拉选项、状态显示
2. **自动处理流程**：
   - 100%匹配：立即执行代码替换
   - 非100%匹配：记录到`check_enum_record.md`文件
   - 未找到匹配：记录到`check_enum_record.md`文件
3. **查找匹配的枚举配置**：在`all_constant.md`中搜索匹配项
4. **使用useGlobalConstants Hook替换硬编码值**：实现统一的枚举管理

**重要传值规则**：
- **Locale.Language枚举**：显示和传值都使用`key`字段
- **其他所有枚举**：显示使用`name`字段，传值使用`key`字段

**完成标准**：
- 100%匹配的枚举已自动替换
- 非匹配项已记录到`check_enum_record.md`
- 所有枚举使用统一的管理方式
- 保持向后兼容性

## 任务执行模板

### 输入参数
```
页面路由：[具体的页面路由，如：system-settings/system-config/system-settings]
```

### 执行步骤
1. **启动第一阶段**：
   ```
   根据页面路由 [页面路由] 执行页面功能完整性修复，
   参考规则：@/augment_rules/check_page_complete/fix_page_uncomplete.md
   ```

2. **启动第二阶段**（第一阶段完成后）：
   ```
   对页面 [页面路由] 执行Modal.confirm批量操作升级，
   参考规则：@/augment_rules/modal_improvements/modal-confirm-to-modal-upgrade.md
   ```

3. **启动第三阶段**（第二阶段完成后）：
   ```
   对页面 [页面路由] 执行枚举值优化检查，
   参考规则：@/augment_rules/check_page_complete/check_enum.md
   ```

## 质量保证

### 每个阶段完成后的验证
- **功能验证**：确保所有功能正常工作
- **用户体验验证**：确保交互流畅，加载状态合适
- **代码质量验证**：确保代码结构清晰，符合项目规范

### 整体完成后的检查
- **兼容性检查**：确保修改不影响现有功能
- **性能检查**：确保没有引入性能问题
- **一致性检查**：确保与项目整体风格一致

## 注意事项

1. **严格按顺序执行**：三个阶段必须按顺序完成，不可跳跃或并行
2. **保持业务逻辑不变**：所有修改只改进技术实现，不改变业务逻辑
3. **向后兼容**：确保修改不破坏现有功能
4. **统一标准**：使用项目统一的代码规范和设计模式
5. **完整记录**：对于无法自动处理的项目，完整记录到相应文件中

## 使用指南

### 标准执行命令
```
请对页面 [页面路由] 执行综合改进任务，按照以下顺序：

1. 先根据 @/augment_rules/check_page_complete/fix_page_uncomplete.md 修复页面功能完整性
2. 修复完成后，再根据 @/augment_rules/modal_improvements/modal-confirm-to-modal-upgrade.md 升级Modal组件
3. 升级完成后，最后根据 @/augment_rules/check_page_complete/check_enum.md 检查和优化枚举值

请严格按照这个顺序执行，每个阶段完成后再进行下一个阶段。
```

### 使用示例

#### 单页面改进
```
请对页面 system-settings/system-config/system-settings 执行综合改进任务，按照以下顺序：

1. 先根据 @/augment_rules/check_page_complete/fix_page_uncomplete.md 修复页面功能完整性
2. 修复完成后，再根据 @/augment_rules/modal_improvements/modal-confirm-to-modal-upgrade.md 升级Modal组件
3. 升级完成后，最后根据 @/augment_rules/check_page_complete/check_enum.md 检查和优化枚举值

请严格按照这个顺序执行，每个阶段完成后再进行下一个阶段。
```

#### 批量页面改进
```
请对以下页面依次执行综合改进任务：

页面1：user-management/user-list
页面2：order-management/order-list
页面3：system-settings/system-config

每个页面都按照以下顺序执行：
1. 先根据 @/augment_rules/check_page_complete/fix_page_uncomplete.md 修复页面功能完整性
2. 修复完成后，再根据 @/augment_rules/modal_improvements/modal-confirm-to-modal-upgrade.md 升级Modal组件
3. 升级完成后，最后根据 @/augment_rules/check_page_complete/check_enum.md 检查和优化枚举值

请严格按照这个顺序执行，完成一个页面的所有阶段后再处理下一个页面。
```

### 快速命令模板
复制以下命令，替换 `[页面路由]` 为实际的页面路由即可：

```
请对页面 [页面路由] 执行综合改进任务，按照以下顺序：
1. 先根据 @/augment_rules/check_page_complete/fix_page_uncomplete.md 修复页面功能完整性
2. 修复完成后，再根据 @/augment_rules/modal_improvements/modal-confirm-to-modal-upgrade.md 升级Modal组件
3. 升级完成后，最后根据 @/augment_rules/check_page_complete/check_enum.md 检查和优化枚举值
请严格按照这个顺序执行，每个阶段完成后再进行下一个阶段。
```

## 预期效果

完成此任务后，页面将具备：
- ✅ 完整的功能实现和错误处理
- ✅ 优秀的用户体验和交互反馈
- ✅ 统一的枚举值管理
- ✅ 现代化的Modal交互方式
- ✅ 高质量的代码结构
- ✅ 良好的性能表现

这个综合改进任务确保了页面在功能完整性、用户体验和代码质量方面的全面提升，为项目的长期维护和扩展奠定了坚实基础。
