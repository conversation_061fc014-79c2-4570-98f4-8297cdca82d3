# 页面综合改进任务规则集

## 📁 文件结构

```
augment_rules/comprehensive_page_improvement/
├── README.md                                    # 本文件 - 使用说明
├── comprehensive-page-improvement-task.md       # 详细的任务规则和执行步骤
└── quick-reference.md                          # 快速参考指南和一键命令
```

## 🎯 规则集目标

这个规则集将三个独立的页面改进规则整合成一个系统性的改进流程：

1. **页面功能完整性修复** (`fix_page_uncomplete.md`)
2. **Modal.confirm批量操作升级** (`modal-confirm-to-modal-upgrade.md`)  
3. **枚举值优化检查** (`check_enum.md`)

## 🚀 快速开始

### 最简单的使用方式

1. 复制以下命令：
```
请对页面 [页面路由] 执行综合改进任务，按照以下顺序：
1. 先根据 @/augment_rules/check_page_complete/fix_page_uncomplete.md 修复页面功能完整性
2. 修复完成后，再根据 @/augment_rules/modal_improvements/modal-confirm-to-modal-upgrade.md 升级Modal组件
3. 升级完成后，最后根据 @/augment_rules/check_page_complete/check_enum.md 检查和优化枚举值
请严格按照这个顺序执行，每个阶段完成后再进行下一个阶段。
```

2. 将 `[页面路由]` 替换为实际的页面路由（如：`system-settings/system-config/system-settings`）

3. 发送给Augment AI助手执行

## 📖 详细文档

### comprehensive-page-improvement-task.md
- 完整的任务规则说明
- 详细的执行步骤
- 质量保证标准
- 注意事项和最佳实践

### quick-reference.md  
- 一键执行命令
- 快速参考指南
- 常用页面路由示例
- 重要提醒事项

## 🔄 执行流程

```
输入页面路由
    ↓
第一阶段：功能完整性修复
    ├── 修复API参数格式
    ├── 添加加载状态
    ├── 修复日期选择器
    └── 表单数据持久化
    ↓
第二阶段：Modal组件升级
    ├── 识别Modal.confirm
    ├── 添加状态管理
    ├── 重构处理函数
    └── 添加Modal组件
    ↓
第三阶段：枚举值优化
    ├── 分析硬编码枚举
    ├── 自动匹配处理
    ├── 替换100%匹配项
    └── 记录非匹配项
    ↓
完成综合改进
```

## ✅ 成功标准

完成后的页面应该具备：

- **功能完整性**：所有API正常工作，有适当的错误处理
- **用户体验**：有加载状态，交互流畅
- **代码质量**：使用统一的组件和模式
- **可维护性**：使用全局枚举管理，代码结构清晰

## 🎯 适用场景

- 新开发的页面需要规范化
- 现有页面需要优化改进
- 批量页面标准化处理
- 代码质量提升项目

## 📞 支持

如果在使用过程中遇到问题：

1. 首先查看 `comprehensive-page-improvement-task.md` 的详细说明
2. 参考 `quick-reference.md` 的快速指南
3. 确保严格按照三个阶段的顺序执行
4. 每个阶段完成后再进行下一个阶段

## 🔗 相关规则文件

- `@/augment_rules/check_page_complete/fix_page_uncomplete.md`
- `@/augment_rules/modal_improvements/modal-confirm-to-modal-upgrade.md`
- `@/augment_rules/check_page_complete/check_enum.md`

---

**版本**：v1.0  
**创建时间**：2025-07-30  
**适用于**：Augment AI助手页面改进任务
