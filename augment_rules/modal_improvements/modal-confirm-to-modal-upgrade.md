# Modal.confirm 批量操作升级规则

## 规则概述
将页面中使用 `Modal.confirm` 实现的批量操作（如批量修改状态、批量删除等）升级为使用常规 `Modal` 组件，提供更好的用户体验和更清晰的代码结构。

## 适用场景
- 页面中存在 `Modal.confirm` 实现的批量操作
- 需要用户选择参数的批量操作（如状态选择、分类选择等）
- 希望提供更好用户体验的批量操作界面

## 实施步骤

### 1. 识别目标代码模式
查找类似以下模式的代码：
```jsx
const handleBatchOperation = () => {
  if (selectedRowKeys.length === 0) {
    message.warning("请选择要操作的项目");
    return;
  }

  let parameterValue = "defaultValue";

  Modal.confirm({
    title: "批量操作标题",
    content: (
      <div>
        <p>请选择参数：</p>
        <Select
          defaultValue="defaultValue"
          onChange={(value) => {
            parameterValue = value;
          }}
        >
          <Option value="option1">选项1</Option>
          <Option value="option2">选项2</Option>
        </Select>
      </div>
    ),
    onOk: async () => {
      // 执行批量操作逻辑
    },
  });
};
```

### 2. 添加状态变量
在组件顶部添加Modal控制状态：
```jsx
// 批量操作Modal相关状态
const [batchOperationModalVisible, setBatchOperationModalVisible] = useState(false);
const [selectedParameter, setSelectedParameter] = useState("defaultValue");
```

### 3. 添加必要的导入
确保导入了 `Alert` 组件：
```jsx
import {
  // ... 其他导入
  Modal,
  Alert,
} from "antd";
```

### 4. 重构处理函数
将原有的 `Modal.confirm` 处理函数拆分为三个函数：

#### 4.1 触发函数
```jsx
const handleBatchOperation = () => {
  if (selectedRowKeys.length === 0) {
    message.warning("请选择要操作的项目");
    return;
  }
  setBatchOperationModalVisible(true);
  setSelectedParameter("defaultValue"); // 重置为默认值
};
```

#### 4.2 确认函数
```jsx
const handleBatchOperationConfirm = async () => {
  try {
    await dispatch(
      batchOperationAction({
        ids: selectedRowKeys,
        parameter: selectedParameter,
      })
    ).unwrap();
    message.success(`已操作 ${selectedRowKeys.length} 个项目`);
    setSelectedRowKeys([]);
    fetchData(); // 刷新数据
    setBatchOperationModalVisible(false);
  } catch (error) {
    message.error("批量操作失败");
  }
};
```

#### 4.3 取消函数
```jsx
const handleBatchOperationCancel = () => {
  setBatchOperationModalVisible(false);
  setSelectedParameter("defaultValue");
};
```

### 5. 添加Modal组件
在return语句中，将原有的单一组件包装改为Fragment，并添加Modal：

#### 5.1 修改return结构
```jsx
// 原来：
return (
  <Card>
    {/* 页面内容 */}
  </Card>
);

// 修改为：
return (
  <>
    <Card>
      {/* 页面内容 */}
    </Card>
    
    {/* 批量操作Modal */}
    <Modal
      title="批量操作标题"
      open={batchOperationModalVisible}
      onOk={handleBatchOperationConfirm}
      onCancel={handleBatchOperationCancel}
      okText="确定操作"
      cancelText="取消"
      width={500}
    >
      <div style={{ padding: '16px 0' }}>
        <Alert
          message={`已选中 ${selectedRowKeys.length} 个项目`}
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
        
        <div style={{ marginBottom: 16 }}>
          <label style={{ display: 'block', marginBottom: 8, fontWeight: 'bold' }}>
            请选择参数：
          </label>
          <Select
            value={selectedParameter}
            onChange={setSelectedParameter}
            style={{ width: "100%" }}
            size="large"
          >
            <Option value="option1">选项1</Option>
            <Option value="option2">选项2</Option>
          </Select>
        </div>

        <Alert
          message={`将对选中的 ${selectedRowKeys.length} 个项目执行操作`}
          type="warning"
          showIcon
        />
      </div>
    </Modal>
  </>
);
```

## 具体示例：批量修改状态

### 原代码模式
```jsx
const handleBatchModifyStatus = () => {
  if (selectedRowKeys.length === 0) {
    message.warning("请选择要修改状态的项目");
    return;
  }

  let statusValue = "1";

  Modal.confirm({
    title: "批量修改状态",
    content: (
      <div>
        <p>请选择要设置的状态：</p>
        <Select
          defaultValue="1"
          onChange={(value) => {
            statusValue = value;
          }}
        >
          <Option value="1">启用</Option>
          <Option value="0">禁用</Option>
        </Select>
      </div>
    ),
    onOk: async () => {
      // 批量更新逻辑
    },
  });
};
```

### 升级后代码
```jsx
// 1. 添加状态变量
const [batchStatusModalVisible, setBatchStatusModalVisible] = useState(false);
const [selectedStatus, setSelectedStatus] = useState("1");

// 2. 重构处理函数
const handleBatchModifyStatus = () => {
  if (selectedRowKeys.length === 0) {
    message.warning("请选择要修改状态的项目");
    return;
  }
  setBatchStatusModalVisible(true);
  setSelectedStatus("1");
};

const handleBatchStatusConfirm = async () => {
  try {
    await dispatch(
      batchUpdateStatus({
        ids: selectedRowKeys,
        status: selectedStatus,
      })
    ).unwrap();
    message.success(`已修改 ${selectedRowKeys.length} 个项目的状态`);
    setSelectedRowKeys([]);
    fetchData();
    setBatchStatusModalVisible(false);
  } catch (error) {
    message.error("批量修改状态失败");
  }
};

const handleBatchStatusCancel = () => {
  setBatchStatusModalVisible(false);
  setSelectedStatus("1");
};

// 3. 添加Modal组件
<Modal
  title="批量修改状态"
  open={batchStatusModalVisible}
  onOk={handleBatchStatusConfirm}
  onCancel={handleBatchStatusCancel}
  okText="确定修改"
  cancelText="取消"
  width={500}
>
  <div style={{ padding: '16px 0' }}>
    <Alert
      message={`已选中 ${selectedRowKeys.length} 个项目`}
      type="info"
      showIcon
      style={{ marginBottom: 16 }}
    />
    
    <div style={{ marginBottom: 16 }}>
      <label style={{ display: 'block', marginBottom: 8, fontWeight: 'bold' }}>
        请选择要设置的状态：
      </label>
      <Select
        value={selectedStatus}
        onChange={setSelectedStatus}
        style={{ width: "100%" }}
        size="large"
      >
        <Option value="1">启用</Option>
        <Option value="0">禁用</Option>
      </Select>
    </div>

    <Alert
      message={`将选中的 ${selectedRowKeys.length} 个项目状态修改为 "${selectedStatus === '1' ? '启用' : '禁用'}"`}
      type="warning"
      showIcon
    />
  </div>
</Modal>
```

## 升级优势

### 用户体验改进
- ✅ 更清晰的操作界面
- ✅ 显示选中项目数量
- ✅ 操作确认提示
- ✅ 更好的视觉反馈

### 代码质量提升
- ✅ 使用React状态管理而不是闭包变量
- ✅ 分离的处理函数，更好的可维护性
- ✅ 更清晰的代码结构
- ✅ 更容易扩展和自定义

### 功能保持
- ✅ 所有业务逻辑完全不变
- ✅ Redux集成保持不变
- ✅ API调用保持不变
- ✅ 错误处理机制保持不变

## 注意事项

1. **保持业务逻辑不变**：只改进UI交互，不修改业务逻辑
2. **保持Redux集成**：继续使用现有的action和状态管理
3. **保持验证逻辑**：确保所有验证逻辑都被保留
4. **测试功能完整性**：升级后需要测试所有功能是否正常工作
5. **统一命名规范**：使用一致的变量和函数命名模式

## 应用指导

当遇到需要升级的Modal.confirm批量操作时：
1. 首先识别现有的Modal.confirm模式
2. 按照步骤逐一进行代码重构
3. 确保所有业务逻辑保持不变
4. 测试功能完整性
5. 验证用户体验改进效果

这个规则可以应用于所有使用Modal.confirm实现批量操作的页面，提供统一的用户体验和代码质量。
