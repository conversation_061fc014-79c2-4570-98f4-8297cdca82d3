# 财务风控管理页面 - 标签页表单数据持久化修复

## 修复概述
按照第8条规则"修复标签页切换时表单数据清空问题"，对财务风控管理页面进行了修改，实现了标签页切换时表单数据的持久化功能。

## 修改的文件

### 1. FinancialRiskControl.jsx (主页面组件)
**文件路径**: `src/pages/SystemSettings/RiskControlManagement/FinancialRiskControl.jsx`

**修改内容**:
- 从 `usePageTabs` Hook 中获取表单数据管理函数：
  - `saveTabFormData` - 保存标签页表单数据
  - `getTabFormData` - 获取标签页表单数据  
  - `clearTabFormData` - 清除标签页表单数据

- 将这些函数传递给 `AddFinancialRiskControl` 组件：
  ```jsx
  <AddFinancialRiskControl
    editingRecord={activeTab.startsWith("add-") ? null : editingRecord}
    onSave={handleSaveSuccess}
    onCancel={handleCancel}
    tabKey={activeTab}
    saveTabFormData={saveTabFormData}
    getTabFormData={getTabFormData}
    clearTabFormData={clearTabFormData}
  />
  ```

### 2. AddFinancialRiskControl.jsx (表单组件)
**文件路径**: `src/pages/SystemSettings/RiskControlManagement/AddFinancialRiskControl.jsx`

**修改内容**:

#### 2.1 组件参数扩展
```jsx
function AddFinancialRiskControl({
  editingRecord,
  onSave,
  onCancel,
  tabKey,
  saveTabFormData,
  getTabFormData,
  clearTabFormData,
}) {
```

#### 2.2 智能表单初始化
```jsx
useEffect(() => {
  if (editingRecord) {
    // 编辑模式：设置编辑记录的数据
    form.setFieldsValue({
      status: editingRecord.status,
      riskName: editingRecord.riskName,
      extraData: editingRecord.extraData
        ? JSON.stringify(editingRecord.extraData, null, 2)
        : "",
      checkClass: editingRecord.checkClass,
      actionClass: editingRecord.actionClass,
      riskCount: editingRecord.riskCount,
    });
  } else {
    // 添加模式：尝试恢复之前保存的表单数据
    if (tabKey && getTabFormData) {
      const savedFormData = getTabFormData(tabKey);
      if (savedFormData) {
        form.setFieldsValue(savedFormData);
      } else {
        form.resetFields();
      }
    } else {
      form.resetFields();
    }
  }
}, [editingRecord, form, tabKey, getTabFormData]);
```

#### 2.3 实时保存表单数据
```jsx
<Form
  form={form}
  layout="vertical"
  onFinish={handleSubmit}
  onValuesChange={() => {
    // 实时保存表单数据（仅在添加模式下）
    if (!editingRecord && tabKey && saveTabFormData) {
      const formData = form.getFieldsValue();
      const hasData = Object.values(formData).some(
        (value) => value && value.toString().trim() !== ""
      );
      if (hasData) {
        saveTabFormData(tabKey, formData);
      }
    }
  }}
  initialValues={{
    status: "1",
    riskCount: 0,
  }}
>
```

#### 2.4 提交成功时清除数据
```jsx
// 在提交成功时清除表单数据（仅在添加模式下）
if (!editingRecord && tabKey && clearTabFormData) {
  clearTabFormData(tabKey);
}
```

#### 2.5 取消和重置时清除数据
```jsx
// 重置表单
const handleReset = () => {
  form.resetFields();
  if (tabKey && clearTabFormData) {
    clearTabFormData(tabKey);
  }
};

// 处理取消操作
const handleCancel = () => {
  form.resetFields();
  if (tabKey && clearTabFormData) {
    clearTabFormData(tabKey);
  }
  onCancel && onCancel();
};
```

## 功能特点

### 1. 数据持久化
- 用户在添加财务风控表单中输入数据后，切换到其他标签页再回来时，数据不会丢失
- 只在添加模式下保存表单数据，编辑模式使用原有逻辑

### 2. 实时保存
- 用户每次输入时自动保存表单数据，无需手动操作
- 只有当表单中有实际数据时才进行保存

### 3. 智能清理
- 提交成功后自动清除保存的表单数据
- 取消操作时清除保存的表单数据
- 重置表单时清除保存的表单数据
- 关闭标签页时自动清除对应的表单数据

### 4. 独立管理
- 每个标签页的表单数据独立存储，使用唯一的 tabKey 作为标识
- 多个添加标签页之间的数据互不影响

### 5. 向后兼容
- 不影响编辑模式的正常工作
- 保持原有的用户交互逻辑

## 测试验证
- 应用程序成功启动，无编译错误
- 所有修改都遵循了第8条规则的要求
- 实现了完整的表单数据持久化功能

## 总结
通过这次修改，财务风控管理页面现在支持标签页切换时表单数据的持久化，大大提升了用户体验，避免了用户因意外切换标签页而丢失已输入数据的问题。
